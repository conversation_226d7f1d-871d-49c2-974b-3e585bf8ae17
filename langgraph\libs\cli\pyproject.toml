[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-cli"
version = "0.3.6"
description = "CLI for interacting with LangGraph API"
authors = []
requires-python = ">=3.9"
readme = "README.md"
license = "MIT"
license-files = ['LICENSE']
dependencies = [
    "click>=8.1.7",
    "langgraph-sdk>=0.1.0 ; python_version >= '3.11'",
]

[project.optional-dependencies]
inmem = [
    "langgraph-api>=0.2.67,<0.3.0 ; python_version >= '3.11'",
    "langgraph-runtime-inmem>=0.6.0 ; python_version >= '3.11'",
    "python-dotenv>=0.8.0",
]

[project.urls]
Repository = "https://www.github.com/langchain-ai/langgraph"

[project.scripts]
langgraph = "langgraph_cli.cli:cli"

[dependency-groups]
dev = [
    "ruff",
    "codespell",
    "pytest",
    "pytest-asyncio",
    "pytest-mock",
    "pytest-watch",
    "mypy",
    "msgspec",
]

[tool.uv]
default-groups = ['dev']

[tool.hatch.build.targets.wheel]
include = ["langgraph_cli"]

[tool.pytest.ini_options]
addopts = "--strict-markers --strict-config --durations=5 -vv"
asyncio_mode = "auto"

[tool.ruff]
lint.select = [
  "E",  # pycodestyle
  "F",  # Pyflakes
  "UP", # pyupgrade
  "B",  # flake8-bugbear
  "I",  # isort
]
lint.ignore = ["E501", "B008"]
