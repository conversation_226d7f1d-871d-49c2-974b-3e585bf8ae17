name: Run notebooks

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      changed-files:
        required: false
        type: string
        description: "JSON string of changed files"
  schedule:
    - cron: "0 13 * * *"

permissions:
  contents: read

defaults:
  run:
    working-directory: docs

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        lib-version:
          - "development"
          - "latest"

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python + Poetry
        uses: astral-sh/setup-uv@v6
        with:
          python-version: "3.11"
          enable-cache: true
          cache-suffix: "test-langgraph-notebooks"

      - name: Install dependencies
        run: |
          uv sync --group test
          uv run pip install jupyter

      - name: Start services
        run: make start-services

      - name: Pre-download tiktoken files
        run: |
          uv run python _scripts/download_tiktoken.py

      - name: Prepare notebooks
        run: |
          if [ "${{ matrix.lib-version }}" = "development" ]; then
            uv run python _scripts/prepare_notebooks_for_ci.py --comment-install-cells
          else
            uv run python _scripts/prepare_notebooks_for_ci.py
          fi

      - name: Run notebooks
        env:
          # these won't actually be used because of the VCR cassettes
          # but need to set them to avoid triggering getpass()
          OPENAI_API_KEY: "very-secret-key"
          ANTHROPIC_API_KEY: "very-secret-key"
          TAVILY_API_KEY: "very-secret-key"
          LANGSMITH_API_KEY: "very-secret-key"
          NOMIC_API_KEY: "very-secret-key"
          COHERE_API_KEY: "very-secret-key"
          FIREWORKS_API_KEY: "very-secret-key"
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ] || [ "${{ github.event_name }}" = "schedule" ]; then
            echo "Running all notebooks"
            ./_scripts/execute_notebooks.sh
          else
            CHANGED_FILES=$(echo '${{ inputs.changed-files }}' | tr ' ' '\n' | sed 's|^docs/docs/|docs/|' | grep '\.ipynb$' || true)
            if [ -n "$CHANGED_FILES" ]; then
              echo "Running changed notebooks: $CHANGED_FILES"
              ./_scripts/execute_notebooks.sh $CHANGED_FILES
            else
              echo "No notebook files changed, skipping execution"
            fi
          fi

      - name: Stop services
        run: make stop-services
