{"cells": [{"attachments": {"15cba0ab-a549-4909-8373-fb761e384eff.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "919fe33c-0149-4f7d-b200-544a18986c9a", "metadata": {}, "source": ["# Self-RAG\n", "\n", "Self-RAG is a strategy for RAG that incorporates self-reflection / self-grading on retrieved documents and generations. \n", "\n", "In the [paper](https://arxiv.org/abs/2310.11511), a few decisions are made:\n", "\n", "1. Should I retrieve from retriever, `R` -\n", "\n", "* Input: `x (question)` OR `x (question)`, `y (generation)`\n", "* Decides when to retrieve `D` chunks with `R`\n", "* Output: `yes, no, continue`\n", "\n", "2. Are the retrieved passages `D` relevant to the question `x` -\n", "\n", "* * Input: (`x (question)`, `d (chunk)`) for `d` in `D`\n", "* `d` provides useful information to solve `x`\n", "* Output: `relevant, irrelevant`\n", "\n", "3. Are the LLM generation from each chunk in `D` is relevant to the chunk (hallucinations, etc)  -\n", "\n", "* Input: `x (question)`, `d (chunk)`,  `y (generation)` for `d` in `D`\n", "* All of the verification-worthy statements in `y (generation)` are supported by `d`\n", "* Output: `{fully supported, partially supported, no support`\n", "\n", "4. The LLM generation from each chunk in `D` is a useful response to `x (question)` -\n", "\n", "* Input: `x (question)`, `y (generation)` for `d` in `D`\n", "* `y (generation)` is a useful response to `x (question)`.\n", "* Output: `{5, 4, 3, 2, 1}`\n", "\n", "We will implement some of these ideas from scratch using [LangGraph](https://langchain-ai.github.io/langgraph/).\n", "\n", "![Screenshot 2024-04-01 at 12.41.50 PM.png](attachment:15cba0ab-a549-4909-8373-fb761e384eff.png)"]}, {"cell_type": "markdown", "id": "72f3ee57-68ab-4040-bd36-4014e2a23d96", "metadata": {}, "source": ["## Setup\n", "\n", "First let's install our required packages and set our API keys"]}, {"cell_type": "code", "execution_count": null, "id": "a384cc48-0425-4e8f-aafc-cfb8e56025c9", "metadata": {}, "outputs": [], "source": ["%pip install -U langchain_community tiktoken langchain-openai langchainhub chromadb langchain langgraph"]}, {"cell_type": "code", "execution_count": null, "id": "de4ee2a5", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(key: str):\n", "    if key not in os.environ:\n", "        os.environ[key] = getpass.getpass(f\"{key}:\")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "25d16369", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "c27bebdc-be71-4130-ab9d-42f09f87658b", "metadata": {}, "source": ["## Retriever\n", " \n", "Let's index 3 blog posts."]}, {"cell_type": "code", "execution_count": 3, "id": "565a6d44-2c9f-4fff-b1ec-eea05df9350d", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=OpenAIEmbeddings(),\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "29c12f74-53e2-43cc-896f-875d1c5d9d93", "metadata": {}, "source": ["## LLMs"]}, {"cell_type": "markdown", "id": "6cf1011b-213c-45fd-9608-328fc6eea677", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": 5, "id": "1fafad21-60cc-483e-92a3-6a7edb1838e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["binary_score='no'\n"]}], "source": ["### Retrieval Grader\n", "\n", "\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# Data model\n", "class GradeDocuments(BaseModel):\n", "    \"\"\"Binary score for relevance check on retrieved documents.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Documents are relevant to the question, 'yes' or 'no'\"\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeDocuments)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    It does not need to be a stringent test. The goal is to filter out erroneous retrievals. \\n\n", "    If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\"\n", "grade_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Retrieved document: \\n\\n {document} \\n\\n User question: {question}\"),\n", "    ]\n", ")\n", "\n", "retrieval_grader = grade_prompt | structured_llm_grader\n", "question = \"agent memory\"\n", "docs = retriever.invoke(question)\n", "doc_txt = docs[1].page_content\n", "print(retrieval_grader.invoke({\"question\": question, \"document\": doc_txt}))"]}, {"cell_type": "code", "execution_count": 7, "id": "dcd77cc1-4587-40ec-b633-5364eab9e1ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The design of generative agents combines LLM with memory, planning, and reflection mechanisms to enable agents to behave conditioned on past experience. Memory stream is a long-term memory module that records a comprehensive list of agents' experience in natural language. LLM functions as the agent's brain in an autonomous agent system.\n"]}], "source": ["### Generate\n", "\n", "from langchain import hub\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# Prompt\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# LLM\n", "llm = ChatOpenAI(model_name=\"gpt-4o-mini\", temperature=0)\n", "\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Chain\n", "rag_chain = prompt | llm | StrOutputParser()\n", "\n", "# Run\n", "generation = rag_chain.invoke({\"context\": docs, \"question\": question})\n", "print(generation)"]}, {"cell_type": "code", "execution_count": 8, "id": "e78931ec-940c-46ad-a0b2-f43f953f1fd7", "metadata": {}, "outputs": [{"data": {"text/plain": ["GradeHallucinations(binary_score='yes')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["### Hallucination Grader\n", "\n", "\n", "# Data model\n", "class GradeHallucinations(BaseModel):\n", "    \"\"\"Binary score for hallucination present in generation answer.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Answer is grounded in the facts, 'yes' or 'no'\"\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeHallucinations)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts. \\n \n", "     Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts.\"\"\"\n", "hallucination_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Set of facts: \\n\\n {documents} \\n\\n LLM generation: {generation}\"),\n", "    ]\n", ")\n", "\n", "hallucination_grader = hallucination_prompt | structured_llm_grader\n", "hallucination_grader.invoke({\"documents\": docs, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 9, "id": "bd62276f-bf26-40d0-8cff-e07b10e00321", "metadata": {}, "outputs": [{"data": {"text/plain": ["GradeAnswer(binary_score='yes')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["### Answer Grader\n", "\n", "\n", "# Data model\n", "class GradeAnswer(BaseModel):\n", "    \"\"\"Binary score to assess answer addresses question.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Answer addresses the question, 'yes' or 'no'\"\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeAnswer)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing whether an answer addresses / resolves a question \\n \n", "     Give a binary score 'yes' or 'no'. Yes' means that the answer resolves the question.\"\"\"\n", "answer_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"User question: \\n\\n {question} \\n\\n LLM generation: {generation}\"),\n", "    ]\n", ")\n", "\n", "answer_grader = answer_prompt | structured_llm_grader\n", "answer_grader.invoke({\"question\": question, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 10, "id": "c6f4c70e-1660-4149-82c0-837f19fc9fb5", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"What is the role of memory in an agent's functioning?\""]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["### Question Re-writer\n", "\n", "# LLM\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "\n", "# Prompt\n", "system = \"\"\"You a question re-writer that converts an input question to a better version that is optimized \\n \n", "     for vectorstore retrieval. Look at the input and try to reason about the underlying semantic intent / meaning.\"\"\"\n", "re_write_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\n", "            \"human\",\n", "            \"Here is the initial question: \\n\\n {question} \\n Formulate an improved question.\",\n", "        ),\n", "    ]\n", ")\n", "\n", "question_rewriter = re_write_prompt | llm | StrOutputParser()\n", "question_rewriter.invoke({\"question\": question})"]}, {"cell_type": "markdown", "id": "276001c5-c079-4e5b-9f42-81a06704d200", "metadata": {}, "source": ["# Graph \n", "\n", "Capture the flow in as a graph.\n", "\n", "## Graph state"]}, {"cell_type": "code", "execution_count": 7, "id": "f1617e9e-66a8-4c1a-a1fe-cc936284c085", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        question: question\n", "        generation: LLM generation\n", "        documents: list of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    documents: List[str]"]}, {"cell_type": "code", "execution_count": 8, "id": "add509d8-6682-4127-8d95-13dd37d79702", "metadata": {}, "outputs": [], "source": ["### Nodes\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    question = state[\"question\"]\n", "\n", "    # Retrieval\n", "    documents = retriever.invoke(question)\n", "    return {\"documents\": documents, \"question\": question}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # RAG generation\n", "    generation = rag_chain.invoke({\"context\": documents, \"question\": question})\n", "    return {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with only filtered relevant documents\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> DOCUMENT RELEVANCE TO QUESTION---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Score each doc\n", "    filtered_docs = []\n", "    for d in documents:\n", "        score = retrieval_grader.invoke(\n", "            {\"question\": question, \"document\": d.page_content}\n", "        )\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            continue\n", "    return {\"documents\": filtered_docs, \"question\": question}\n", "\n", "\n", "def transform_query(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates question key with a re-phrased question\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Re-write question\n", "    better_question = question_rewriter.invoke({\"question\": question})\n", "    return {\"documents\": documents, \"question\": better_question}\n", "\n", "\n", "### Edges\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ASSESS GRADED DOCUMENTS---\")\n", "    state[\"question\"]\n", "    filtered_documents = state[\"documents\"]\n", "\n", "    if not filtered_documents:\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\n", "            \"---DECISION: AL<PERSON> DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---\"\n", "        )\n", "        return \"transform_query\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\"\n", "\n", "\n", "def grade_generation_v_documents_and_question(state):\n", "    \"\"\"\n", "    Determines whether the generation is grounded in the document and answers question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---CHEC<PERSON> HALLUCINATIONS---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    generation = state[\"generation\"]\n", "\n", "    score = hallucination_grader.invoke(\n", "        {\"documents\": documents, \"generation\": generation}\n", "    )\n", "    grade = score.binary_score\n", "\n", "    # Check hallucination\n", "    if grade == \"yes\":\n", "        print(\"---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---\")\n", "        # Check question-answering\n", "        print(\"---GRADE GENERATION vs QUESTION---\")\n", "        score = answer_grader.invoke({\"question\": question, \"generation\": generation})\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---DECISION: GENERATION ADDRESSES QUESTION---\")\n", "            return \"useful\"\n", "        else:\n", "            print(\"---DECISION: GENERATION DOES NOT ADDRESS QUESTION---\")\n", "            return \"not useful\"\n", "    else:\n", "        pprint(\"---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---\")\n", "        return \"not supported\""]}, {"cell_type": "markdown", "id": "61cd5797-1782-4d78-a277-8196d13f3e1b", "metadata": {}, "source": ["## Build Graph\n", "\n", "The just follows the flow we outlined in the figure above."]}, {"cell_type": "code", "execution_count": 10, "id": "0e09ca9f-e36d-4ef4-a0d5-79fdbada9fe0", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "workflow.add_node(\"transform_query\", transform_query)  # transform_query\n", "\n", "# Build graph\n", "workflow.add_edge(START, \"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"transform_query\": \"transform_query\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"transform_query\", \"retrieve\")\n", "workflow.add_conditional_edges(\n", "    \"generate\",\n", "    grade_generation_v_documents_and_question,\n", "    {\n", "        \"not supported\": \"generate\",\n", "        \"useful\": END,\n", "        \"not useful\": \"transform_query\",\n", "    },\n", ")\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 11, "id": "fb69dbb9-91ee-4868-8c3c-93af3cd885be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---RETRIEVE---\n", "\"Node 'retrieve':\"\n", "'\\n---\\n'\n", "---<PERSON><PERSON><PERSON> DOCUMENT R<PERSON>EVANCE TO QUESTION---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---ASSESS GRADED DOCUMENTS---\n", "---DECISION: GENERATE---\n", "\"Node 'grade_documents':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "('Short-term memory is used for in-context learning in agents, allowing them '\n", " 'to learn quickly. Long-term memory enables agents to retain and recall vast '\n", " 'amounts of information over extended periods. Agents can also utilize '\n", " 'external tools like APIs to access additional information beyond what is '\n", " 'stored in their memory.')\n"]}], "source": ["from pprint import pprint\n", "\n", "# Run\n", "inputs = {\"question\": \"Explain how the different types of agent memory work?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}, {"cell_type": "code", "execution_count": 12, "id": "4138bc51-8c84-4b8a-8d24-f7f470721f6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---RETRIEVE---\n", "\"Node 'retrieve':\"\n", "'\\n---\\n'\n", "---<PERSON><PERSON><PERSON> DOCUMENT R<PERSON>EVANCE TO QUESTION---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---ASSESS GRADED DOCUMENTS---\n", "---DECISION: GENERATE---\n", "\"Node 'grade_documents':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "('Chain of thought prompting works by repeatedly prompting the model to ask '\n", " 'follow-up questions to construct the thought process iteratively. This '\n", " 'method can be combined with queries to search for relevant entities and '\n", " 'content to add back into the context. It extends the thought process by '\n", " 'exploring multiple reasoning possibilities at each step, creating a tree '\n", " 'structure of thoughts.')\n"]}], "source": ["inputs = {\"question\": \"Explain how chain of thought prompting works?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}