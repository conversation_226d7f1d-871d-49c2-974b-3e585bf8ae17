{"cells": [{"attachments": {"2a4ecdd2-280d-4311-a2cd-cd6138090be9.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "5afcaed0-3d55-4e1f-95d3-c32c751c29d8", "metadata": {"id": "5afcaed0-3d55-4e1f-95d3-c32c751c29d8"}, "source": ["# Adaptive RAG Cohere Command R\n", "\n", "Adaptive RAG is a strategy for RAG that unites (1) [query analysis](https://blog.langchain.dev/query-construction/) with (2) [active / self-corrective RAG](https://blog.langchain.dev/agentic-rag-with-langgraph/).\n", "\n", "In the paper, they report query analysis to route across:\n", "\n", "* No Retrieval (LLM answers)\n", "* Single-shot RAG\n", "* Iterative RAG\n", "\n", "Let's build on this to perform query analysis to route across some more interesting cases:\n", "\n", "* No Retrieval (LLM answers)\n", "* Web-search\n", "* Iterative RAG\n", "\n", "We'll use [Command R](https://cohere.com/blog/command-r), a recent release from Cohere that:\n", "\n", "* Has strong accuracy on RAG and Tool Use\n", "* Has 128k context\n", "* Has low latency \n", "  \n", "![Screenshot 2024-04-02 at 8.11.18 PM.png](attachment:2a4ecdd2-280d-4311-a2cd-cd6138090be9.png)"]}, {"cell_type": "markdown", "id": "a85501ca-eb89-4795-aeab-cdab050ead6b", "metadata": {"id": "a85501ca-eb89-4795-aeab-cdab050ead6b"}, "source": ["# Environment"]}, {"cell_type": "code", "execution_count": null, "id": "f6c329ba-cb85-4576-9828-4f2ac648d1a6", "metadata": {}, "outputs": [], "source": ["! pip install --quiet langchain langchain_cohere langchain-openai tiktoken langchainhub chromadb langgraph"]}, {"cell_type": "code", "execution_count": null, "id": "222f204d-956f-4128-b597-2c698120edda", "metadata": {"id": "222f204d-956f-4128-b597-2c698120edda"}, "outputs": [], "source": ["### LLMs\nimport os\n\nos.environ[\"COHERE_API_KEY\"] = \"<your-api-key>\""]}, {"cell_type": "code", "execution_count": null, "id": "08edba00-988a-478b-96fc-ae0199cbef49", "metadata": {"id": "08edba00-988a-478b-96fc-ae0199cbef49"}, "outputs": [], "source": ["# ### Tracing (optional)\n# os.environ['LANGCHAIN_TRACING_V2'] = 'true'\n# os.environ['LANGCHAIN_ENDPOINT'] = 'https://api.smith.langchain.com'\n# os.environ['LANGCHAIN_API_KEY'] ='<your-api-key>'"]}, {"cell_type": "markdown", "id": "9ac1c2cd-81fb-40eb-8ba1-e9197800cba6", "metadata": {"id": "9ac1c2cd-81fb-40eb-8ba1-e9197800cba6"}, "source": ["## Index"]}, {"cell_type": "code", "execution_count": 1, "id": "b224e5ba-50ca-495a-a7fa-0f75a080e03c", "metadata": {"id": "b224e5ba-50ca-495a-a7fa-0f75a080e03c"}, "outputs": [], "source": ["### Build Index\n\nfrom langchain.text_splitter import RecursiveCharacterTextSplitter\nfrom langchain_cohere import CohereEmbeddings\nfrom langchain_community.document_loaders import WebBaseLoader\nfrom langchain_community.vectorstores import Chroma\n\n# Set embeddings\nembd = CohereEmbeddings()\n\n# Docs to index\nurls = [\n    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n]\n\n# Load\ndocs = [WebBaseLoader(url).load() for url in urls]\ndocs_list = [item for sublist in docs for item in sublist]\n\n# Split\ntext_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n    chunk_size=512, chunk_overlap=0\n)\ndoc_splits = text_splitter.split_documents(docs_list)\n\n# Add to vectorstore\nvectorstore = Chroma.from_documents(\n    documents=doc_splits,\n    embedding=embd,\n)\n\nretriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "0f52b427-750c-40f8-8893-e9caab3afd8d", "metadata": {"id": "0f52b427-750c-40f8-8893-e9caab3afd8d"}, "source": ["## LLMs"]}, {"cell_type": "markdown", "id": "H5CztTqsBOTZ", "metadata": {"id": "H5CztTqsBOTZ"}, "source": ["We use a router to pick between tools. \n", " \n", "Cohere model decides which tool(s) to call, as well as the how to query them."]}, {"cell_type": "code", "execution_count": 2, "id": "bYK-e0diGdPf", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bYK-e0diGdPf", "outputId": "895a8ea5-57ee-49fe-ef28-277eac8a7bb7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': 'f811e3b9-052e-49db-a234-5fc3efbcc5ba', 'function': {'name': 'web_search', 'arguments': '{\"query\": \"NFL draft bears first pick\"}'}, 'type': 'function'}]\n", "[{'id': '4bc53113-8f32-4d6d-ac9b-c07ef9aae9fd', 'function': {'name': 'vectorstore', 'arguments': '{\"query\": \"types of agent memory\"}'}, 'type': 'function'}]\n", "False\n"]}], "source": ["### Router\n\nfrom langchain_cohere import <PERSON><PERSON><PERSON><PERSON><PERSON>\nfrom langchain_core.prompts import <PERSON>t<PERSON>romptT<PERSON>plate\nfrom langchain_core.pydantic_v1 import BaseModel, Field\n\n\n# Data model\nclass web_search(BaseModel):\n    \"\"\"\n    The internet. Use web_search for questions that are related to anything else than agents, prompt engineering, and adversarial attacks.\n    \"\"\"\n\n    query: str = Field(description=\"The query to use when searching the internet.\")\n\n\nclass vectorstore(BaseModel):\n    \"\"\"\n    A vectorstore containing documents related to agents, prompt engineering, and adversarial attacks. Use the vectorstore for questions on these topics.\n    \"\"\"\n\n    query: str = Field(description=\"The query to use when searching the vectorstore.\")\n\n\n# Preamble\npreamble = \"\"\"You are an expert at routing a user question to a vectorstore or web search.\nThe vectorstore contains documents related to agents, prompt engineering, and adversarial attacks.\nUse the vectorstore for questions on these topics. Otherwise, use web-search.\"\"\"\n\n# LLM with tool use and preamble\nllm = ChatCohere(model=\"command-r\", temperature=0)\nstructured_llm_router = llm.bind_tools(\n    tools=[web_search, vectorstore], preamble=preamble\n)\n\n# Prompt\nroute_prompt = ChatPromptTemplate.from_messages(\n    [\n        (\"human\", \"{question}\"),\n    ]\n)\n\nquestion_router = route_prompt | structured_llm_router\nresponse = question_router.invoke(\n    {\"question\": \"Who will the Bears draft first in the NFL draft?\"}\n)\nprint(response.response_metadata[\"tool_calls\"])\nresponse = question_router.invoke({\"question\": \"What are the types of agent memory?\"})\nprint(response.response_metadata[\"tool_calls\"])\nresponse = question_router.invoke({\"question\": \"Hi how are you?\"})\nprint(\"tool_calls\" in response.response_metadata)"]}, {"cell_type": "code", "execution_count": 4, "id": "oaLWNbWxBgjE", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oaLWNbWxBgjE", "outputId": "57a5c27b-044b-4df5-f55d-7bf23d3976d1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["binary_score='yes'\n"]}], "source": ["### Retrieval Grader\n\n\n# Data model\nclass GradeDocuments(BaseModel):\n    \"\"\"Binary score for relevance check on retrieved documents.\"\"\"\n\n    binary_score: str = Field(\n        description=\"Documents are relevant to the question, 'yes' or 'no'\"\n    )\n\n\n# Prompt\npreamble = \"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n\nIf the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\nGive a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\"\n\n# LLM with function call\nllm = Chat<PERSON>ohere(model=\"command-r\", temperature=0)\nstructured_llm_grader = llm.with_structured_output(GradeDocuments, preamble=preamble)\n\ngrade_prompt = ChatPromptTemplate.from_messages(\n    [\n        (\"human\", \"Retrieved document: \\n\\n {document} \\n\\n User question: {question}\"),\n    ]\n)\n\nretrieval_grader = grade_prompt | structured_llm_grader\nquestion = \"types of agent memory\"\ndocs = retriever.invoke(question)\ndoc_txt = docs[1].page_content\nresponse = retrieval_grader.invoke({\"question\": question, \"document\": doc_txt})\nprint(response)"]}, {"cell_type": "markdown", "id": "D43a7vM4EElX", "metadata": {"id": "D43a7vM4EElX"}, "source": ["Generate"]}, {"cell_type": "code", "execution_count": 5, "id": "BTIUdjRMEq_h", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BTIUdjRMEq_h", "outputId": "11a99f62-2449-45db-9281-5bfd60e3c966"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are three types of agent memory: sensory memory, short-term memory, and long-term memory.\n"]}], "source": ["### Generate\n\nfrom langchain_core.messages import HumanMessage\nfrom langchain_core.output_parsers import StrOutputParser\n\n# Preamble\npreamble = \"\"\"You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\"\"\"\n\n# LLM\nllm = ChatCohere(model_name=\"command-r\", temperature=0).bind(preamble=preamble)\n\n\n# Prompt\ndef prompt(x):\n    return ChatPromptTemplate.from_messages(\n        [\n            HumanMessage(\n                f\"Question: {x['question']} \\nAnswer: \",\n                additional_kwargs={\"documents\": x[\"documents\"]},\n            )\n        ]\n    )\n\n\n# Chain\nrag_chain = prompt | llm | StrOutputParser()\n\n# Run\ngeneration = rag_chain.invoke({\"documents\": docs, \"question\": question})\nprint(generation)"]}, {"cell_type": "code", "execution_count": 6, "id": "bc000a7d-84b6-4eb2-88ad-65cc62a44431", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I don't have feelings as an AI chatbot, but I'm here to assist you with any queries or concerns you may have. How can I help you today?\n"]}], "source": ["### LLM fallback\n\nfrom langchain_core.output_parsers import StrOutputParser\n\n# Preamble\npreamble = \"\"\"You are an assistant for question-answering tasks. Answer the question based upon your knowledge. Use three sentences maximum and keep the answer concise.\"\"\"\n\n# LLM\nllm = ChatCohere(model_name=\"command-r\", temperature=0).bind(preamble=preamble)\n\n\n# Prompt\ndef prompt(x):\n    return ChatPromptTemplate.from_messages(\n        [HumanMessage(f\"Question: {x['question']} \\nAnswer: \")]\n    )\n\n\n# Chain\nllm_chain = prompt | llm | StrOutputParser()\n\n# Run\nquestion = \"Hi how are you?\"\ngeneration = llm_chain.invoke({\"question\": question})\nprint(generation)"]}, {"cell_type": "code", "execution_count": 7, "id": "y0msuR2DHQkY", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "y0msuR2DHQkY", "outputId": "f0e91c2a-5542-45c0-a7a6-60453e0b2bc4"}, "outputs": [{"data": {"text/plain": ["GradeHallucinations(binary_score='yes')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["### Hallucination Grader\n\n\n# Data model\nclass GradeHallucinations(BaseModel):\n    \"\"\"Binary score for hallucination present in generation answer.\"\"\"\n\n    binary_score: str = Field(\n        description=\"Answer is grounded in the facts, 'yes' or 'no'\"\n    )\n\n\n# Preamble\npreamble = \"\"\"You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts. \\n\nGive a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts.\"\"\"\n\n# LLM with function call\nllm = ChatCohere(model=\"command-r\", temperature=0)\nstructured_llm_grader = llm.with_structured_output(\n    GradeHallucinations, preamble=preamble\n)\n\n# Prompt\nhallucination_prompt = ChatPromptTemplate.from_messages(\n    [\n        # (\"system\", system),\n        (\"human\", \"Set of facts: \\n\\n {documents} \\n\\n LLM generation: {generation}\"),\n    ]\n)\n\nhallucination_grader = hallucination_prompt | structured_llm_grader\nhallucination_grader.invoke({\"documents\": docs, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 8, "id": "f0c08d14-77a0-4eed-b882-2d636abb22a3", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "f0c08d14-77a0-4eed-b882-2d636abb22a3", "outputId": "c4f88c9a-65fd-4dad-e739-3c9bd547a9f5"}, "outputs": [{"data": {"text/plain": ["GradeAnswer(binary_score='yes')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["### Answer Grader\n\n\n# Data model\nclass GradeAnswer(BaseModel):\n    \"\"\"Binary score to assess answer addresses question.\"\"\"\n\n    binary_score: str = Field(\n        description=\"Answer addresses the question, 'yes' or 'no'\"\n    )\n\n\n# Preamble\npreamble = \"\"\"You are a grader assessing whether an answer addresses / resolves a question \\n\nGive a binary score 'yes' or 'no'. Yes' means that the answer resolves the question.\"\"\"\n\n# LLM with function call\nllm = ChatCohere(model=\"command-r\", temperature=0)\nstructured_llm_grader = llm.with_structured_output(GradeAnswer, preamble=preamble)\n\n# Prompt\nanswer_prompt = ChatPromptTemplate.from_messages(\n    [\n        (\"human\", \"User question: \\n\\n {question} \\n\\n LLM generation: {generation}\"),\n    ]\n)\n\nanswer_grader = answer_prompt | structured_llm_grader\nanswer_grader.invoke({\"question\": question, \"generation\": generation})"]}, {"cell_type": "markdown", "id": "d07c0b31-b919-4498-869f-9673125c2473", "metadata": {"id": "d07c0b31-b919-4498-869f-9673125c2473"}, "source": ["## Web Search Tool"]}, {"cell_type": "code", "execution_count": 9, "id": "01d829bb-1074-4976-b650-ead41dcb9788", "metadata": {"id": "01d829bb-1074-4976-b650-ead41dcb9788"}, "outputs": [], "source": ["### Search\n# os.environ['TAVILY_API_KEY'] ='<your-api-key>'\n\nfrom langchain_community.tools.tavily_search import TavilySearchResults\n\nweb_search_tool = TavilySearchResults()"]}, {"cell_type": "markdown", "id": "efbbff0e-8843-45bb-b2ff-137bef707ef4", "metadata": {"id": "efbbff0e-8843-45bb-b2ff-137bef707ef4"}, "source": ["# Graph\n", "\n", "Capture the flow in as a graph.\n", "\n", "## Graph state"]}, {"cell_type": "code", "execution_count": 10, "id": "e723fcdb-06e6-402d-912e-899795b78408", "metadata": {"id": "e723fcdb-06e6-402d-912e-899795b78408"}, "outputs": [], "source": ["from typing import List\n\nfrom typing_extensions import TypedDict\n\n\nclass GraphState(TypedDict):\n    \"\"\"|\n    Represents the state of our graph.\n\n    Attributes:\n        question: question\n        generation: LLM generation\n        documents: list of documents\n    \"\"\"\n\n    question: str\n    generation: str\n    documents: List[str]"]}, {"cell_type": "markdown", "id": "7e2d6c0d-42e8-4399-9751-e315be16607a", "metadata": {"id": "7e2d6c0d-42e8-4399-9751-e315be16607a"}, "source": ["## Graph Flow"]}, {"cell_type": "code", "execution_count": 11, "id": "b76b5ec3-0720-443d-85b1-c0e79659ca0a", "metadata": {"id": "b76b5ec3-0720-443d-85b1-c0e79659ca0a"}, "outputs": [], "source": ["from langchain.schema import Document\n\n\ndef retrieve(state):\n    \"\"\"\n    Retrieve documents\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        state (dict): New key added to state, documents, that contains retrieved documents\n    \"\"\"\n    print(\"---RETRIEVE---\")\n    question = state[\"question\"]\n\n    # Retrieval\n    documents = retriever.invoke(question)\n    return {\"documents\": documents, \"question\": question}\n\n\ndef llm_fallback(state):\n    \"\"\"\n    Generate answer using the LLM w/o vectorstore\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        state (dict): New key added to state, generation, that contains LLM generation\n    \"\"\"\n    print(\"---LLM Fallback---\")\n    question = state[\"question\"]\n    generation = llm_chain.invoke({\"question\": question})\n    return {\"question\": question, \"generation\": generation}\n\n\ndef generate(state):\n    \"\"\"\n    Generate answer using the vectorstore\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        state (dict): New key added to state, generation, that contains LLM generation\n    \"\"\"\n    print(\"---GENERATE---\")\n    question = state[\"question\"]\n    documents = state[\"documents\"]\n    if not isinstance(documents, list):\n        documents = [documents]\n\n    # RAG generation\n    generation = rag_chain.invoke({\"documents\": documents, \"question\": question})\n    return {\"documents\": documents, \"question\": question, \"generation\": generation}\n\n\ndef grade_documents(state):\n    \"\"\"\n    Determines whether the retrieved documents are relevant to the question.\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        state (dict): Updates documents key with only filtered relevant documents\n    \"\"\"\n\n    print(\"---CHECK DOCUMENT RELEVANCE TO QUESTION---\")\n    question = state[\"question\"]\n    documents = state[\"documents\"]\n\n    # Score each doc\n    filtered_docs = []\n    for d in documents:\n        score = retrieval_grader.invoke(\n            {\"question\": question, \"document\": d.page_content}\n        )\n        grade = score.binary_score\n        if grade == \"yes\":\n            print(\"---GRADE: DOCUMENT RELEVANT---\")\n            filtered_docs.append(d)\n        else:\n            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n            continue\n    return {\"documents\": filtered_docs, \"question\": question}\n\n\ndef web_search(state):\n    \"\"\"\n    Web search based on the re-phrased question.\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        state (dict): Updates documents key with appended web results\n    \"\"\"\n\n    print(\"---WEB SEARCH---\")\n    question = state[\"question\"]\n\n    # Web search\n    docs = web_search_tool.invoke({\"query\": question})\n    web_results = \"\\n\".join([d[\"content\"] for d in docs])\n    web_results = Document(page_content=web_results)\n\n    return {\"documents\": web_results, \"question\": question}\n\n\n### Edges ###\n\n\ndef route_question(state):\n    \"\"\"\n    Route question to web search or RAG.\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        str: Next node to call\n    \"\"\"\n\n    print(\"---ROUTE QUESTION---\")\n    question = state[\"question\"]\n    source = question_router.invoke({\"question\": question})\n\n    # Fallback to LLM or raise error if no decision\n    if \"tool_calls\" not in source.additional_kwargs:\n        print(\"---ROUTE QUESTION TO LLM---\")\n        return \"llm_fallback\"\n    if len(source.additional_kwargs[\"tool_calls\"]) == 0:\n        raise \"Router could not decide source\"\n\n    # Choose datasource\n    datasource = source.additional_kwargs[\"tool_calls\"][0][\"function\"][\"name\"]\n    if datasource == \"web_search\":\n        print(\"---ROUTE QUESTION TO WEB SEARCH---\")\n        return \"web_search\"\n    elif datasource == \"vectorstore\":\n        print(\"---ROUTE QUESTION TO RAG---\")\n        return \"vectorstore\"\n    else:\n        print(\"---ROUTE QUESTION TO LLM---\")\n        return \"vectorstore\"\n\n\ndef decide_to_generate(state):\n    \"\"\"\n    Determines whether to generate an answer, or re-generate a question.\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        str: Binary decision for next node to call\n    \"\"\"\n\n    print(\"---ASSESS GRADED DOCUMENTS---\")\n    state[\"question\"]\n    filtered_documents = state[\"documents\"]\n\n    if not filtered_documents:\n        # All documents have been filtered check_relevance\n        # We will re-generate a new query\n        print(\"---DECISION: ALL DOCUMENTS ARE NOT RELEVANT TO QUESTION, WEB SEARCH---\")\n        return \"web_search\"\n    else:\n        # We have relevant documents, so generate answer\n        print(\"---DECISION: GENERATE---\")\n        return \"generate\"\n\n\ndef grade_generation_v_documents_and_question(state):\n    \"\"\"\n    Determines whether the generation is grounded in the document and answers question.\n\n    Args:\n        state (dict): The current graph state\n\n    Returns:\n        str: Decision for next node to call\n    \"\"\"\n\n    print(\"---CHECK HALLUCINATIONS---\")\n    question = state[\"question\"]\n    documents = state[\"documents\"]\n    generation = state[\"generation\"]\n\n    score = hallucination_grader.invoke(\n        {\"documents\": documents, \"generation\": generation}\n    )\n    grade = score.binary_score\n\n    # Check hallucination\n    if grade == \"yes\":\n        print(\"---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---\")\n        # Check question-answering\n        print(\"---GRADE GENERATION vs QUESTION---\")\n        score = answer_grader.invoke({\"question\": question, \"generation\": generation})\n        grade = score.binary_score\n        if grade == \"yes\":\n            print(\"---DECISION: GENERATION ADDRESSES QUESTION---\")\n            return \"useful\"\n        else:\n            print(\"---DECISION: GENERATION DOES NOT ADDRESS QUESTION---\")\n            return \"not useful\"\n    else:\n        pprint(\"---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---\")\n        return \"not supported\""]}, {"cell_type": "markdown", "id": "3ab01f36-5628-49ab-bfd3-84bb6f1a1b0f", "metadata": {"id": "3ab01f36-5628-49ab-bfd3-84bb6f1a1b0f"}, "source": ["## Build Graph"]}, {"cell_type": "code", "execution_count": 12, "id": "67854e07-9293-4c3c-bf9a-bc9a605570ee", "metadata": {"id": "67854e07-9293-4c3c-bf9a-bc9a605570ee"}, "outputs": [], "source": ["import pprint\n", "\n", "from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"web_search\", web_search)  # web search\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # rag\n", "workflow.add_node(\"llm_fallback\", llm_fallback)  # llm\n", "\n", "# Build graph\n", "workflow.add_conditional_edges(\n", "    START,\n", "    route_question,\n", "    {\n", "        \"web_search\": \"web_search\",\n", "        \"vectorstore\": \"retrieve\",\n", "        \"llm_fallback\": \"llm_fallback\",\n", "    },\n", ")\n", "workflow.add_edge(\"web_search\", \"generate\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"web_search\": \"web_search\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_conditional_edges(\n", "    \"generate\",\n", "    grade_generation_v_documents_and_question,\n", "    {\n", "        \"not supported\": \"generate\",  # Hallucinations: re-generate\n", "        \"not useful\": \"web_search\",  # Fails to answer question: fall-back to web-search\n", "        \"useful\": END,\n", "    },\n", ")\n", "workflow.add_edge(\"llm_fallback\", END)\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 13, "id": "29acc541-d726-4b75-84d1-a215845fe88a", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "29acc541-d726-4b75-84d1-a215845fe88a", "outputId": "47caec8e-54e3-4f89-dfbb-94fc034666f7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "---ROUTE QUESTION TO WEB SEARCH---\n", "---WEB SEARCH---\n", "\"Node 'web_search':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "'The Bears are expected to draft <PERSON> with their first pick.'\n"]}], "source": ["# Run\ninputs = {\n    \"question\": \"What player are the Bears expected to draft first in the 2024 NFL draft?\"\n}\nfor output in app.stream(inputs):\n    for key, value in output.items():\n        # Node\n        pprint.pprint(f\"Node '{key}':\")\n        # Optional: print full state at each node\n    pprint.pprint(\"\\n---\\n\")\n\n# Final generation\npprint.pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "11fddd00-58bf-4910-bf36-be9e5bfba778", "metadata": {"id": "11fddd00-58bf-4910-bf36-be9e5bfba778"}, "source": ["Trace:\n", "\n", "https://smith.langchain.com/public/623da7bb-84a7-4e53-a63e-7ccd77fb9be5/r"]}, {"cell_type": "code", "execution_count": 14, "id": "69a985dd-03c6-45af-a67b-b15746a2cb5f", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "69a985dd-03c6-45af-a67b-b15746a2cb5f", "outputId": "e5f799cc-6f36-494f-c8b2-192de1edb7fc"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "---ROUT<PERSON> QUESTION TO RAG---\n", "---RETRIEVE---\n", "\"Node 'retrieve':\"\n", "'\\n---\\n'\n", "---<PERSON><PERSON><PERSON> DOCUMENT R<PERSON>EVANCE TO QUESTION---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---ASSESS GRADED DOCUMENTS---\n", "---DECISION: GENERATE---\n", "\"Node 'grade_documents':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "'Sensory, short-term, and long-term memory.'\n"]}], "source": ["# Run\ninputs = {\"question\": \"What are the types of agent memory?\"}\nfor output in app.stream(inputs):\n    for key, value in output.items():\n        # Node\n        pprint.pprint(f\"Node '{key}':\")\n        # Optional: print full state at each node\n        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n    pprint.pprint(\"\\n---\\n\")\n\n# Final generation\npprint.pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "ebf41097-fc4c-4072-95b3-e7e07731ada1", "metadata": {"id": "ebf41097-fc4c-4072-95b3-e7e07731ada1"}, "source": ["Trace:\n", "\n", "https://smith.langchain.com/public/57f3973b-6879-4fbe-ae31-9ae524c3a697/r"]}, {"cell_type": "code", "execution_count": 15, "id": "qPwP_2PNiOjQ", "metadata": {"id": "qPwP_2PNiOjQ"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "---ROUTE QUESTION TO LLM---\n", "---<PERSON><PERSON> Fallback---\n", "\"Node 'llm_fallback':\"\n", "'\\n---\\n'\n", "(\"I don't have feelings as an AI assistant, but I'm here to help you with your \"\n", " 'queries. How can I assist you today?')\n"]}], "source": ["# Run\ninputs = {\"question\": \"Hello, how are you today?\"}\nfor output in app.stream(inputs):\n    for key, value in output.items():\n        # Node\n        pprint.pprint(f\"Node '{key}':\")\n        # Optional: print full state at each node\n        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n    pprint.pprint(\"\\n---\\n\")\n\n# Final generation\npprint.pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "4107c8a4-6171-4c1b-840a-77a3d09f84fc", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/1f628ee4-8d2d-451e-aeb1-5d5e0ede2b4f/r"]}, {"cell_type": "code", "execution_count": null, "id": "ce3cda0a-c4bd-41ea-830b-d992f27fde15", "metadata": {}, "outputs": [], "source": [""]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}