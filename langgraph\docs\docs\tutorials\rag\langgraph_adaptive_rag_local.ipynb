{"cells": [{"cell_type": "code", "execution_count": 11, "id": "2631596b-8a66-4d23-a00d-d2c745c611d4", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langchain langchain_community tiktoken langchain-nomic \"nomic[local]\" langchain-ollama scikit-learn langgraph tavily-python bs4"]}, {"attachments": {"6cd777a6-a0b3-4feb-bd07-8e9e8a4b32a0.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAC00AAAK1CAYAAACuduSpAAAAAXNSR0IArs4c6QAAIABJREFUeF7s3QdwVOUaxvEnPSEhECD0EnrvHUGkiHSpYsGG2LGDFwuIYLsKiO3aEFQEpBdpUkSKSJXee++9J<PERSON>QkufMdzZrNbpJNCCmb/5nxhpz92vs7hxnvzLOvHrGxsbHiQgABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE3FTAg9C0mz5ZykIAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABS4DQNC8CAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCLi1AKFpt368FIcAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAChKZ5BxBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDArQUITbv146U4BBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEECA0zTuAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAm4tQGjarR8vxSGAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAoWneAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwawFC0279eCkOAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBAhN8w4ggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgFsLEJp268dLcQgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCBAaJp3AAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQTcWoDQtFs/XopDAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAULTvAMIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4NYChKbd+vFSHAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBAcgLXosI1a+8Ca1i70i0V6JMjuSl8nsUECE1nsQfGcRFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE0k7gxNXT6jb9cV2JvGot6uXppcX3TVawb1DabcJKGS5AaDrDHwEHQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBDIKIHP/xqtkRvH2W3/3u391bZ0i4w6EvveAgFC07cAlSURQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBLKGwAO/9NHWMzvtDvtRswG6M+z2rFEAp3RJgNC0S0wMQgABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwN4FrUeFq9FNHh7K+azNMtQtWc7dys3U9hKaz9eOneAQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCD7Ciw4sFT9Fg9xAJjS+VuVzh2WfWHcsHJC0274UCkJAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCB5gT4L3tDyI6sdBi66d6LyBoQkvwAjsowAoeks86g4KAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAJpJzB8zdfad+GQteDdZe/SnWG3J7p4dEy0+i95T+E3Iqwx/6n/rIoFF067w7DSTQscu3JSA5d9pMJBBVQpXzk1L3Gb8ufId9PruvMC+y8cUudpjzktcd3D8+Tl6eXO5We72ghNZ7tHTsEIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIISBO2z9T7Kz+zKHL5Beu3eycmGhKdsnO2hqwYYY0N8g3U7/dNlrenN4yZSGDtiY3qPbev7USBPjnUulQzVQ+tqOYlGlvPjcte4KVFg7T40B9OWTY8ugAuNxMgNO1mD5RyEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEHBFwHSPbv7zPbp4/ZI1fOBtL6tLuTYOU2NiY9RsfHfbuAGNXlTX8u1c2YIx6Sxw6NJR/XFkjeYfWKL1J7fYdvfz8lXLsCZW9+kmRevL18s3nU+W+bYzPo/OecnpwXL4BGhFz5mZ79Cc6KYECE3fFB+TEUAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEMi6AjN2/6q3lg+1Csjjn1sL750oTw8Pu4J+2TNfA5Z9FG/MBHl6eGbdorPJyU14+s9ja7X8yBoduHjYVnW+HHnUvPjf4ekmxepnEw37Mm/E3FDrSQ/ozLVzTus3Rgt7TMiWNu5cNKFpd3661IYAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBAEgIxsbFqNfFeW3j03dv7q13pFrYZsYrVnRMS/9zZ0seunNSxKydUPLiI8ufIlyL/a1HhVkfr8xEXdTnqqgK9A5TLP9gKdJvuvx6yD3SnaPFsPHjdiU1admSVfj/0p12AunTuMN1VsqlalWyqsFzFso3Q5+tGaeSm8YnWWzJXMU3rMipDPMz7b7pg7zq3T7vO79Pe8wfk4+VjPZ+elbqoWv5KGXIud9iU0LQ7PEVqQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCCVAr/u/13/+f1da3aBwFDNu2esLZw8b/9i9f/9Paefxd/OBD1NN+o/jq5RdEy07SMvTy/dU76DXq73pHw8vZ2e8Gz4eY3bNk2z9i7UyaunE63i69Yfqn6hmqmskmlxAtN3z9OcvYu0+viGf5+Th5ctPH1H8UZujbX/wiF1nvZYkjWWCC6qGV1Hp5uD+Tsw/8ASzdqzUFvP7Ex0X/P36bd7JyqXX3C6nS1uI/O+jNk6WeaLDQ9W7qrk3pPwGxGKirmhYN+gFJ/1xNVTmrl7vhX0P33tnK5EXVUuv5wqGJjfCo+3KdVMtQtWS/GXKAhNp/hRMAEBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAAB9xEw3aTbT35IRy+fsIr68I43rc7D5mo98QGZAKO5hjV/Sy1KNHYofMfZPeo192UrTJnYZcKO07uMkr+3n92Qy5FX1G36E0mGpeMm/NL1BxULLuw+8BlcyYqjazVn3yLN2fubYmJjbKeplLesWpW8Q61L3WGFVN3pMu/6PdOf1O7z+21lmQ7mAxq9qNeWvG+7l9pO0+ciLmjz6R26FnVNhYMKqHr+yonymUDxqmN/afy26daXDVy9xnX4QpXylUty+Jlr5+Tr7ZuqwLKzheN/scJ83rBIbX3Z6gOHoabL/NDVX+qPI2t0PTrS+twEvcOCi+n+Sp3UoUwr+Xr5JHn2fRcO6r6Zz9jmJzbYfMHjo2YDVC20oqt0IjTtMhUDEUAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEHBPgaWHV+r5hQOs4orkLKjZ3cZo0cHleuW3t617JqxsQsvOrq7THtfeCwdsHzUpVl8FA0O178IhrTuxyXb/8eoP6Nlaj9gt0W/xEC04sNR2z3TQrVWgqvIE5FaAt7/VtdoEq89HXNSIloMT7Vbtnk8lfaoyz2723r/D03EBebNzTt8gtS3dXO1KtVC1/JXS5zC3eJfP143SyE3j7XYxXwYwtT4xr5/tftmQkprU6RuH05hA7+HLx1UlX3nlDQix+3zL6R16bO4rdmHfF+r01qNVezis8/P2Gfpo9Zd2XdldLX3pA9MSDUP/eXSd3lv5qQ5fOmYtZ8LgXcu3c3Vpp+OWHV6l5xa+afeZqcnUFneZTvOfrRutyTtnJblX6dxh+r7dx5a3s8uErrtNfzzJL2AknPdQlW56sc7j8vTwTLZOQtPJEjEAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAfcXiB9+HtHibQ1b87UtfPn5ne+qcdF6Dgjxg9Wmo+zY9p+rQt4ytnHz9y/Rq7+/Y/1uPv+z5y92nWabj+8u053XXN0rtNcbDV9Id+iP13yrPRf2ywS2c/nltH46u3w8veXv5Sc/b1/5mZ9evvLz/vtnDm9/BfoGKsgnh+1nuhdykxteibyqOft+09x9v2n9yS12qzUr3khtS7fQnWG33+QuGTd98aE/9NKiQXYHuL1YA33acogV7jeB57jLdNse1/F/dmOXHF6pF/75YoF5lyd0/EplQsKsMaZz9b0zn3Yagv6k5RA1LdbAtpYJV/ec9VyyEEG+gSqfp7Ty+Oe2QsY5/YLUoFAtq8uzsysyOkoNf+rgcIZVD8223tHUXKZjdbvJDzp0ff669YeqX6imteT2s7uteswXHFy5TPfyqZ1HynT4jn+F34iwOtubAHZKL9MZ/JvWHyk0R94kpxKaTqks4xFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwQ4FVx9fryXmvWpWZkOX16Ejrz+XylNLEu792WvGAZR/qlz0LrM/61O6l3tXucxj30OwXtOnUNuu+CUuWyl3CNqbRTx1tXWXbl26ptxq/ku7dpNtO6inT4Tatr/gBahOoDvQOUIBPgBWwjv/T1X095CFPD/O/HvLwiPuz599//ufe33/++57dfXP3n3u2+c7G/TN/x7k9+uvEZv11crPd8UoEF1WDIrXVuEgd+Xr52c5ROncJhfjndrWUdB9nOi93mtbLLthrQslzu/9kBZJNSPzROS/ZzlU1tILGtP/M7pz/XfmFxm+fbrv3ct0nZbocm7Bvu0kP2sL/CYsz+yy5b4r1pQFzDVo+TNN3z3NqYMb0rNRFD1Tuovw58qXIKbEw9vQuoxSWq1iK1oob3GvOyw7vgDlbv3pPW0NMWLznL30cQtXJbTakST91KNPKbtjHa77RD1smOUw14erq+StZzynixnWtPbHRaSdqM25yp29VOKhAotsTmk7uyfA5AggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAtlE4P6Zz2jb2d121X7XZphqF6zmVODBWc9p8+kd1mcTO30tE6pNeH267juN3TrVum06WN9RvJFtiOnua7r8xl0mNHpbkbpWN13TVbdk7uK3XP7gxSOau3+xvlr/4y3fy103iOvYnBnri4q5oU5TH9XRyycSfa83ntqqh2e/aPu8VoGqGtV2uN347zdP0Ii1I233Hq56j16s01vPLxygZYdXJVn613f9V/UL17LGJBWa/qn9Z6oSWiFVjMuPrFafBW84zP2+3QjVyF85xWuO2TpZw1bbf1nCdOD+qcMXVgB//8XDunfGUw6BafN32Hx5onWp5rp0/ZIm7PhFc/Yustvf/B3/otV7tnsxsbFqOKa9w1qPVu2h5+s8Zn1RIP5lAuLD13zjEOhO2NU7YdGEplP8GjABAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAfcUSBgeddZxN37lzcd3T7TDrjOh1xo8px4VO9o+Onz5mHr+8pwuXr/kFDSPf249V7uX7i57lzw9PG8Zuuleu/b4xlu2flILm07F126EKzwqwU9zPyrc6mQc9zNDDpjMpqazb8eyd+mpGg9mxuNp8B8fa+quOXZne7hKd71U9wnbvQ2ntuqReKHp+oVq6uvWH9rN+WXPfA1Y9pHt3n0VO1nv/q/7f0+27s7l2uit2162xu2/cEjdZjxh1/U6/gLNit+mNxo+r3w58iS7bvwBCbtlx322oMfPCs2RN0VrJfQwk00n59ndxijEP5diYmPUZlJPnbx62m7dAoGh+qHdCBUMzG93/7N1o/TdpvG2e6aT/aqHZtt+33R6ux6a9bzdHBPQHtvxC4fAdPxBJig+atPP2nV+n/X3ZHTbj1UttGKitRKaTtFrwGAEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE3Fug+/QntPv8fqvIL1t9YHV9TuxqOaGHzlw75zLIoMavqFPZ1nbjTQBz+u55Gr9tum3fhAuaDtaTO38rH09vl/dyx4HhN8J1LSpC8X9G3IhUVEyUIqOjEv8ZHWV18Y2MjrR+xv1j+/1GpCJuRCg8+rr1M+LGdSuEan4md5UOCdOUTt8mNyxDPl90cLle+e1tu70r5yuvMe0/s7olx11LDq/UCwsH2H5vXLSePr/zXbt5P2yZpI/XfJNkHabL8vDmgzR09Zc6fOmYbWwuv2AtuX+K7fdtZ3bp0TkvOXRWjhtg1nmkyj0y3ayDfYNcsjMB5rsm3m83NuG+5ksKJmhsztalXFuVCQlzWNsEwdtPfsgK6se/4nfBHr99uv678gu7z8vlKSXTlT5ngvM6q7VkrmKa1mWUbf6kHbP07p+f2K1nOteXCynlUu2uDiI07aoU4xBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBDIBgKPzX1F605ssiod1+ELVcpXLtGqn5z3qlYdX2993qHMnapVoGqSQvUL15LpTJzYZYKapuvziqNrNXffYrsO1P3qP6MHKnXOBk8gY0uMjonW6uMbtPr4eq05vkFbzux0OFCwX06ro2+egNwy3cMDvP0z9tBOdj9+5aTaT3nYrqNzkG+gZnX9Qbn9c9lmRMXc0Nfrf9TIeJ2QzYcmOG1qvKdCB2v8sNVfa8zWyYnWaYLOo9sMV7X8lbTr3D7dM+NJu7HrH51v1zXZdFd/989PNX//kiTXvLfi3epd7X6rw3NS18Xrl9V0XBe7IU/VfMjWAfyTtSM1evME2+emc7TpQh3ok8N2z3yBoecvfbTt7G67dYY06acOZVpZ965GXZPpMG+C9wkv49uyRBMrjO3v7ScTWv/z6DqHcabbvHlv4q6EtuZLEjO6jk7zd4rQdJqTsiACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACWVcgJaHpD1f9T+O2TbOKrV+opr5u/WGaFR4TG6v7f3lGO87usdZsW7qF3ru9f5qtz0L/ClyPvq7Fh1bojyNrrMC06Vqc8Cqas7DVdfy2InV0R/FGmZrPdN02HdMPXjpid877KnayAr1Xo8J14OJhrT+5WfsvHk6ylriuyK8v/UBz9i5KdOz37UaoRv7Kts+bjutqF/r/7b5JyuOf22G+CaYP+mOYjl4+keQ5uldoryerP6h8OfI4HWdC2GbP+Fdcd2gTdL7tp7sd5j1b6xE9Xv0B636sYvWf3991CHEbs/80eNY2d+jqr/TT1n+7Zqf0RTDh8tndflTBwPy2qa8teV9z9/1m+71C3jL6ueOXKV062fGEppMlYgACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC2UcgJaHpTae26aHZL9hwOpVtrVfrPyPTxfZmr/AbEbp3xtO24KtZe1DjV252WebHEzDdpE1Y+vdDK3T8yikHm9K5S6hhkTpq9M8/WQXv5d8G6beDf6TJcU2n5/4N+ih+V/WECw+87WV1KdfG7nbCkPXEu79WuTylEj3TuhOb9f2WCVp2eFWS57677F16uubDKhgYajfufMRFNRvfze7enw/+YnUBNx2mTafphFf8LzoMWTFCU3bOthtiQuCj2n4sTw8P677pQt7op452XaZNCNrcd/UyX3wwX4CIfz09v79dR+rK+cprbIfPXV3S5XGEpl2mYiACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC7i+QktC00Xhr+VDN2P2rDcaEKJsUrSfTmdjb01sXIi5q74UD6lKurTonCJZ2mPKwvD28VDS4sNWFN4d3gCJjoqxOx6YD7/XoSNu6Q5r0U4cyrdz/AdziCnef32+FpE1YetuZXQ67mS6/cSHpOgWr3+LTpP3y324cqy/++v6mF66Ut6zVYbn6P92ju0573HqPE14tw5poaLOBDvfHbJ2sYau/tt1/u3FfmcBzctfZ8POaumuORm4cZ/f+J5z3XO1eeqzafbbbZl6Ln++xG7bh0QXacnqHes56zum2Qb6BWv7AdI1YO1Lfb55gN6ZAYKimdh6pQJ8ctvvLj6xWnwVv2I2b0vlbbTm9U8PXfGPXWTvhhuaLFCNaDFa9QjUczvLgrOe0+fQO2/2qoRU0pv1nyVGl+HNC0ykmYwICCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC7iuQ0tD01ahrenr+azJdp5O6TGDUBEfjLtOdtvYPrV2CvKN4QytwyZU6AdO1e8GBpVqwf6mWHXHsZFy7YDXVLVhDjYrUVrX8lVK3SSaYNXXXXA3+Y3iKTmKCw14eXg6B3wU9flZojry2tUyH5WtR4XZrm6D/7O5jrG7OCa8/jq7Rs/Nft91uX7ql3rn9Py6fzTyzsdum6dsNPyUanm5Tqrneb/qatab5csIdCTpNz+42Rt1nPOFw7viHKBZcWIcvHbM7l5+Xr6Z3GaVCQQXs7v9v/Q/6ZsNPtnvxO1Wbv89z9i3S0sOrrAD0+YgLKhxUQFVCK8r8/W1YuHaiHejvmfGkdp3bZ1u3YGB+zbtnrMtWrg4kNO2qFOMQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyAYCT8/vrz+PrrMqndjpa5ULKeVS1SaUO2Lttzp6+YTT8Q2L1NaXrT6wfXbq2hm1mvBvp1xnk4rkLKjO5drqwcpdZYKcXCkT2Hpmp+bvX6oFB5bo2JWTtskmDFy3YHXVLVRDdQtVt7qCZ/Vr1Kaf9em675Isw3RPrpm/iqqElpfpqF02pKRy+QXLBJQbjulgN/ezlu+oSbH61r3I6CjV+7Gtw9rmfTbvtbMrYedns/ev94yzDb0Rc8PqRF2rYFU1KVpf/t5+Ttcx3dbHbZumr9b/6DQ8/WSNnnq65sOKVaxqjrbvxG66vpswc0qvn9p/piqhFRymvbbkfc3d95vtfrfy7fVmoxdSurzD+LunPKqDl47Y7pu/66semn3T6yZcgNB0mpOyIAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALZVyAmNlYnrp6SCUXHxMYo0CeH1XE2p2+QA4oJep68esbqShtx47oV/PT19FGegBCF+Ody2sE3+8q6VnliXaVN997bizXQ7cXqq16hmvL18nFtwSwwyoSPx2yd7HDS3tXvV4ngosrpG6gq+SooX448iVbTeGwnXYm8avv80ao99EKd3tbvCQPQ5l79wrX09V3/TVKn+fjuOhdxwTZm0b0TlTcgxPp90o5ZevfPT6w/m5Cw6UJ9Z9jtia5nnqsJho/a/LNDEHphjwlWbQlrSM2j+/CON9WqZFOnU/stHmJ1LI+7TOD89/sny0MeqdnKNqfDlIcdul0vuX+qcvnlvKl1E04mNJ2mnCyGAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIpL/ArnP7NHvvIruu0ibUGheUNj/dsVv3W8uHasbuXx3A32j4grpXaO/yg6j9Q2u7MLLpwv1t64+s+abbs+mKfvH6Jdt6M7qOtgLZSV0Dln2oX/YssA3pW+8p9azc1fr9/ZWfacL2mXbT6xeqqSdrPqhaBaomuuzyI6vVZ8Ebdp8PvO0ldSnXVo/MflEbTm1NdO6QJv30xV8/WF9qcHY9W+sRPV79gUTnj1g7Ut9vnmD3+bDmb6lFicYuOzsb2G7ygw4d6j9pOURNizW4qXUTTiY0naacLIYAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgikv0CN0XfaNjUB6ZZhTazAdG6/4PQ/TDrtuPjQH3pp0SCH3d69vb/alW7h8ikOXz6mDpMfthtvOjebDs5x17ErJ3XPjCetbtQ9KnbUaw2eS3Z90229zaSetjD2EzV66pmaf++TWHds81mRnAVVKncJ5QvIY3XJNt3bL0de0V8nNzt0ZDbj48LO8/YvVv/f33M4l5enl75s9YHqFaqhnef2qseMpxzG3Fexk/7T4Nkka1p3YrMem/uy3Riz9s8dv1TZkJLJeiQ2wFnYe2Knr1UupFSq13Rl31OHAAAgAElEQVQ2kdB0mnKyGAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIJD+ArP2LtSRy8fVsUwrFQ4qkP4HyIAdH5v7itad2GTb2QR4RzR/W02K1U/RaUwguu2knnZzTFfuVQ/Ntrt3+tpZHbtyQtXzV3Z5/ck7Z+mdFZ8oh0+ApnT6VoX+eTbbz+5Wz1nP2XW3dnnRBANHtHhbdxRvpJjYGD08+wVtPr3DNsLUMbrtx6qUr5zt3uFLx9R/yXu6cP2iquarqIeqdLP7PKlzPL9wgJYeXmk3xNQ2svVQl9e4FhWuHef26OTVM7p0/bIVEo/fPTuPf24tum+iPOSRWhKn8whNpykniyGAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIpIfAXRPv18mrp62tyuUppREtBqc6MP72H8M1bddc27GDfAO1/IHpaVLGmfBzVtfohNfRyyf02tL3tenUtlTvYwLin7YcYgsYm47Un/81WqeunlWBwHx6uuZDypWG3cZNcLz1pAechr3blm6hF2o/pgKBoQ71mKD24kMrtPDgMod6O5RppXqFqlvh8hux0VY9txWpm2qTxCYSmk5zUhZEAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEELjVAj9tnaKZe+brzrDb9Vi1++XpkfrOxJHRUXp6fn9b5+rXGz6neyp0vNUlWOsvOrhcozb9rK1ndrq8n+nu3LfeU+pUts1N1e3yhvEGzt23WK8teS/RqeZsZUJKKpdfTivUfvDiEV2Pjkx0fPMSt2l480FWEDsyJkoB3v6pOVaycwhNJ0vEAAQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACB7CBwNeqafL185ePpne7lnrp2RtvP7tHu8/u159x+Hbp0RB4engrxz6XcfsHK7Z/L6h5dt2ANlc9b2tZdOt0PKmnL6R164tdXdS0q/Ka3f+/2/jJdqm/1RWj6VguzPgIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAm4mcObaOb2x9AOtOr4+xZV5eXqpRv7K6lfvaVXIWybF81MzgdB0atSYgwACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAI6cPGwvtk4Vr/u/13RMdGJipTOHaY7ijdUk2L1VS20ojw9PNNVj9B0unKzGQIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAu4pcOraGR24eERHLx+3CiwYmF8lchW1fnp6eGRo0YSmM5SfzRFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDgVgsQmr7VwqyPAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQBoL7L1wUK8uHqIcPgEa0/6zNF6d5RBAAAH3EyA07X7PlIoQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQTcWOCtZUM1Y8+vVoXNS9ym4c0HuXG1lIYAAgikjQCh6bRxZBUEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEbqnAd5vG68ctk3Xx+iVrH29Pb619eO4t3ZPFEUAAAXcRIDTtLk+SOhBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBNxSYMquORq/bbr2nN9vV99ztXvpsWr3uWXNFIUAAgiktQCh6bQWZT0EEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE0kBg1t6FmrjjF206tc1azXSWvhFzw/pz8eAimtn1+zTYhSUQQACB7CFAaDp7PGeqRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyCICCw4s1cQdM7Xm+EbrxH5efgrxD9aJq6dtFbxS90k9WKVbFqmIYyKAAAIZL0BoOuOfASdAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAQEsOr7TC0n8cWWPT6F6+vXad36eN/3SbNh/kDQjRxLu/tn5yIYAAAgi4JkBo2jUnRiGAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCBwSwSWHl6pyTtny/yMu7qWbyfzz7cbftLiQyus2+XylNauc3utDtOm0zQXAggggIDrAoSmXbdiJAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAJpJpBUWLpS3rJ6bcl7mrtvsbVft/LtNXnnLOvPP3f8UhXylkmzc7AQAgggkB0ECE1nh6dMjQgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAplGILmwtDnooD+GafquedaZe1e/X2fDz2varrlqXaqZPmj6eqaphYMggAACWUWA0HRWeVKcEwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIEsL/Hl0nSbumKnFh1bY6uhavp3MP6azdNz1wcrP9fP2Gdav91XspJ6Vu6rr9N6KuHFdn7V8R02K1c/SDhweAQQQyAgBQtMZoc6eCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC2UZg/cktVlh67r7FtpqdhaXNhyPWfqvvN0+0xnUo00pDmvTT6M0T9Mnakaqcr7zGdvg827hRKAIIIJCWAoSm01KTtRBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBD4R2D72T2asGOGpu+aZzPpVLa17qnY0a6zdNyHX63/UV9tGGP92rxEYw1v/pb15/tmPqPtZ3erf4M+urfi3fgigAACCKRCgNB0KtCYggACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEBiAmfCz+mnrVM1dutURcVEWcNal7zDCkvXKlDV6bSx26bqo1VfWp/VL1xLHzcfpBw+Afr90Aq9uOgtBfkEan6P8dY9LgQQQACBlAsQmk65GTMQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQcCpggtI/bZuq41dOWp83LdZAPSp2VKMidRMVm7lnvgYu+8j6vEpoBSswHZojr/X760ve15x9v+n+Sp31av1nUEcAAQQQSKUAoelUwjENAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgfgC/RYP0YIDS61bVUMr6uEq3dQy7PYkkRYfWqGXFr1ljSmZq5iGt3jb+mmuAxePqNv03roRE61RbYcn2qWap4AAAgggkLwAoenkjRiBAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQLICJjA9YftM3VXqDnUv3z7Z8WtPbFTvuX2tcflz5NXw5oOsTtNx1zcbx+p/f32vuoVq6NvWf3ei5kIAAQQQSJ0AoenUuTELAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgVQL7Dy3Vz1mPGXNz+EToI+bD1L9wrXs1us2/QntOb9fbzZ6Qd1cCGGn+jBMRAABBLKBAKHpbPCQKREBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCDzCBy7clJtJ/W0HWh487fUvERjuwOartX9Fg+xOlBP7jxSwb5BmacAToIAAghkQQFC01nwoXFkBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBrClwOfKKmoztbDv8kCb91KFMK4diTGDaBKd7Vu6qvvX+7kjNhQACCCCQegFC06m3YyYCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACKRKoMfpO2/j/NHhW91Xs5DB/74WD6jqtt3V/TPtPVTW0Yor2YDACCCCAgKMAoWneCgQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQTSQaDej20VGR1l7dSn1qPqXf1+p7t+tf5HfbVhjBoXrafP73w3HU7GFggggID7CxCadv9nTIUIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIZLNBsfHedj7hgneLRqj30Qp2/O0k7u0yXadNtekCjF9W1fLsMPjnbI4AAAu4hQGjaPZ4jVSCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCGRSgQ6TH9bhy8es091ToYNeb/h8oif9/dAKvbjoLQX7Bml6l1HKExCSSaviWAgggEDWEiA0nbWeF6dFAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBDIQgL3znxaO87usU7crnQLvXt7/yRP32/xEC04sFR3l71Lbzfum4Uq5agIIIBA5hYgNJ25nw+nQwABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyKICvef21doTG63T31G8oUa0GJxkJRevX1bTcV2sMR+3eFvNijfKopVzbAQQQCDzCRCaznzPhBMhgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAghkcYEXFw3U74f+tKqoU7C6RrQcrCCfHElW9eOWSRq+5huVz1NaE+7+KosLcHwEEEAgcwkQms5cz4PTIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIZHGBN5b+V7P3LrSqqJi3rEa0eFsFAkOTrarrtN7ae+GgXq3/jO6v1DnZ8QxAAAEEEHBdgNC061aMRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCBJgff//EwTdsy0xhQPLmIFpkvlLpGs2ubTO/TgrOdUNGchq8t0YDJdqZNdkAEIIIAAAnYChKZ5IRBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBIA4FP132nUZt+tlbKGxCij1u8rWqhFV1a+av1P+qrDWP0bK1H9Hj1B1yawyAEEEAAAdcFCE27bsVIBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBJwK/Lhlkoav+cb6zN/bTx83f1sNi9R2WWvBgaWasH2mPm05RDl8Alyex0AEEEAAAdcECE275sQoBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBJwKzNm7SK8v/cD22dBmA9Qy7Ha0EEAAAQQykQCh6Uz0MDgKAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBA1hJYfXy9npj3qu3Qgxq/ok5lW2etIjgtAgggkA0ECE1ng4dMiQgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAmkvsPfCQXWd1tu2cL96T+uByl3SfiNWRAABBBC4aQFC0zdNyAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALZTeDi9ctqOu7fgPTTNR/WkzV6ZjcG6kUAAQSyjACh6SzzqDgoAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBAZhGoMfpO21EeqtJdL9d9IrMcjXMggAACCDgRIDTNa4EAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBACgRMh2nTadpcXcu304BGL6ZgNkMRQAABBDJCgNB0RqizJwIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQJYU6Dqtt/ZeOGidvXWpZvqg6etZsg4OjQACCGQ3AULT2e2JUy8CCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggECqBJ6Y96pWH19vzW1StL4+aTlYnh6eqVqLSQgggAAC6StAaDp9vdkNAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAgCwq8vvQDzdm7yDp5rQJVrcB0Tt+gLFgJR0YAAQSypwCh6ez53KkaAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDARYHha77Rj1smWaPL5SltBaYLBeZ3cTbDEEAAAQQygwCh6czwFDgDAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBAphQYvXmCPlk70jpbkZwF9UmLISoTEpYpz8qhEEAAAQQSFyA0zduBAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgBOByTtn650VI6xPcvsFWx2mq+evjBUCCCCAQBYUIDSdBR8aR0YAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEELi1AvP3L9Grv79jbeLj6a0RLQfrtiJ1b+2mrI4AAgggcMsECE3fMloWRgABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyIoCfx5dp6fn97cd/cM73lSrkk2zYimcGQEEEEDgHwFC07wKCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCPwjsOXMTvX8pY/NY+BtL6tLuTb4IIAAAghkcQFC01n8AXJ8BBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBtBE4dOmoOk55xLbYK/We1IOVu6XN4qyCAAIIIJChAoSmM5SfzRFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBDKDwPmIi2o2/t+A9FM1HtRTNR/KDEfjDAgggAACaSBAaDoNEFkCAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAg6wpEx0Sr9g+tbQX0rNxVfes9lXUL4uQIIIAAAg4ChKZ5KRBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBLK1gAlMm+C0uTqXa6O3bns5W3tQPAIIIOCOAoSm3fGpUhMCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgggkEECX20Yo5m7f9WxKydVPk9p1SlUXU/VeFA5fYMy6ERJb9tsfDedj7hoDWoV1lQfNnszU56TQyGAAAII3JwAoemb82M2AggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICApJ3n9mrgso+snwmvwkEFNLhJP9UpWD1TWXWc8ogOXTpqnalRkTr6tOU78vb0ylRn5DAIIIAAAmkjQGg6bRxZBQEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBDItgIzd8/XwOUf2dXv7+OniKjrdvdev+153VOuQ6Zw6vlLH205s9M6S/X8lazAdC6/nJnibBwCAQQQQCDtBQhNp70pKyKAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC2Ubgo9VfauzWqVa9AT7+alO9uWqGVbUC09uP7tKcjYvswtOZITj91K/9tfLYOuvMZULC9GnLISocVDDbPDMKRQABBLKjAKHp7PjUqRkBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQSAMB013adJk2V1hoMT3QqKtMh+n4lwlPT10zW9uP7bbdzsjgdN/Fg7XwwDLrLAUD81uB6XJ5SqWBBksggAACCGRmAULTmfnpcDYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIJMKxA9M1yxRRV3qtkvypCt2r9XcjYtsYwY2fkldyrZN1+oGLR+m6bvnWXvm9A2yAtM1C1RJ1zOwGQIIIIBAxggQms4Yd3ZFAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBLKsQPzAdJc6bVUzrKpLtew/fUjjVkyV6T5trpFthqpOweouzb3ZQUNXf6Wftk6xlvH08LQC042L1rvZZZmPAAIIIJBFBAhNZ5EHxTERQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgcwgkNrAdNzZj184pVFLxlnB6UDfHBrVZrjK5yl9S0v7asMYfbX+R9seHzR9Xa1LNbule7I4AggggEDmEiA0nbmeB6dBAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBDKtwM0GpuMKM4Hp734fpxMXTymnb5DVcfpWBadNd2nTZTruGtDoRXUt3y7TGnMwBBBAAIFbI0Bo+ta4sioCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4DYClyOvaOCyoVp86A+rpi512qpmWNWbqs8+OB2okW2GpXlwevrueRq0fJjtnC/VfUIPV+l+U+dmMgIIIIBA1hQgNJ01nxunRgABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQTSReDYlZN6adFb2nlur/x9/HR/oy4qGVo8TfaOH5wO9M2hUW2Gp1lweuGBZeq7eLDtnE/U6Klnaj6cJudmEQQQQACBrCdAaDrrPTNOjAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgiki4AJSvee21em07QJTPdqer8K5c6fpnvHD04H+ATo6zb/VbW8FW9qj5XH1umZ+a8rJjbGWueBSp3Vr/4zN7UmkxFAAAEEsrYAoems/fw4PQIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALZViA8MkZLd53Xb9vP68j5CJ2+HKVL4TcU5O+l0Jy+Kp7HX80rhuj2ciEK8PXMtk4UjgACCCCAAAIIpFZg5u75Grj8I2v6rQpMx50tfnDa38dfn9/1juqEVk/V0Tef3qE+C97QxeuXrPl3l71Lbzfum6q1mIQAAggg4D4ChKbd51lSCQIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALZQuDc1Si9O+uAxq864XK9vZoUVv+2YQry83J5DgPdR+DtGft06FyEraBiefw16O5S7lMglSCAAAIIIHALBD5a/aXGbp1qrXyrA9Nxx7cPTvvpy9YfqGa+Kimqbv/Fw3puwZs6cvmYNa9lWBMNbTYwRWswGAEEEEDAPQUITbvnc6UqBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBNxSYNbGM3pmzA7diIlNcX2Bfl5a8EpNheULSPFcJmRtgdqDV+v4heu2Igrl9tO6gfWydlGcHgEEEEAAgVsoYLpLmy7T5kqvwHRcOQmD09+0+VDV8lZyqdrT187qhYUDte3sLmt8g8K19dmdQ+Tj6ePSfAYhgAACCLi3AKFp936+VIcAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICA2wiM+fO4/jNpz03VExLoo9Vv1pUJUHNlHwFC09nnWVMpAggggMDNC2RkYDr+6aeuma31B7cowMdf37T5SFXzVkiyuPAbEXpx0UCtOrbeGlc1tKI+bTlEIf65bh6FFRBAAAEE3EKA0LRbPEaKQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMC9BVbsuahu/9vktEhvTw+VLZBDNYrnVOXCgdp/JlxLdl7QnlPXHMZXKRKk2S/WkI+Xh3uDUZ2dAKFpXggEEEAAAQSSF7gceUUvLRqktSc2WoNNh+lnWz6q3IEZFzqOH5z+rs0wVcpbLtFC+i4erIUHllmfl85dQp+0HKKiOQslXzgjEEAAAQSyjQCh6WzzqCkUAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgawpcPV6tGq/vVqXIm44FNCwdC6N7lVJwQHeDp/tOx2uth9vsM2rVjRI0/pUV4CvZ9aE4NSpFiA0nWo6JiKAAAIIZBMBE5juPbevdp7ba1VsAtO9mt6vQrnzZ7hAXHA6h2+ATDMTTtAAACAASURBVHC6Yp6yTs/04KzndC0qXIMa91XV0KS7Umd4URwAAQQQQCBDBAhNZwg7myKAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgKsCQ+cd1PD5hxyGv9yquF65q4Q8kmgaffJSpNp/skGhOX015ZlqBKZdRXezcYSm3eyBUg4CCCCAQJoKZObAdFyh8YPTo9t8rPJ5SqepAYshgAACCGQPAULT2eM5UyUCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACWVIgJlaq9MafDl2mH2xYSP/tXsalmqKiY61gtbdnEunqeCuZ8XtOXtPmo1e06cgVbT92VYF+XqpcOFCViwSpStEgheX1d2lv0+160+ErtrE5A7zUomIe6/dTlyM1buUJ7TkVrqPnryvQz1OVCwepcpFAa4zZM6VXbKx0+HyEthz5++ybj1xR5I1YVSiUQ1WLBlnrmz97JWNxLTJa87ecS3T7Evn8VbN4TtvnK/Zc1JKd57Xj+FVdCL+hcgVyqFLhQDWrmCdJK2Pw2cLD1rPx8vKQj6eHvL08ldPfS6E5fVSzRLDL1uYwN2JiNWvDGYdz95+8x+4dCvb31gfdkn9/GpTOpYK5fF16DKYj+vbjVy1z8+7sPhmuIrn9VKXI3++N8c8X5OPSWgxCAAEEEEAgvQSyQmA6zoLgdHq9FeyDAAIIuK8AoWn3fbZUhgACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggECWF/ht+3n1/HaLQx0b3q6v/DldC7OmBGHVvot6eOQ2h5B2wjXaV8+nYT3KWeHepK4vFx/RkF/224aYIPTu9xvpq9+PaPDMf+8nXCMk0EcjH6mohqVzuXz84xeu6+HvtmnL0X9D2s4mlwoN0A+PVVbp/AGJrn34XITqv7Mm0c/rl8qlaX2q6cr1aD07ZocWbEs8YP1e1zJ65LZCTtdavvuC7vlyc5I1+nl7qk5YsF5tU0J1SwYnOdacp9xrK1w2S27gNw9XlHnWyV2jlx/TG1P3JjdM/duGqU+LYnIxv5/segxAAAEEEEDgZgSyUmA6rs5xK6Zq+7Hd1q+DG/dTx7KtboaAuQgggAAC2UyA0HQ2e+CUiwACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEBWEjCBYxM8jn/1bFhQH3Yvm6ZlRMfE6v05B/S/3+z3SmoT0614VK9KalQm8WBzwtC0WW/CU1XV46ukg8Jx+37Rs7w618qfbK0zN5xWn592Wp2WXb0Gdyql3rcXcTo8udB0gWBfrR9UXy/9vEsTVp9MdsthPcrqvvoFHcaZuWYNV687K+XR0B5lFZpIYD69Q9Pnr0bpqTE7tGzXBVdLsDpwm9B6kRA/l+cwEAEEEEAAgVsh0GPGU9p57u8v/fj7+KlX0/tVKHfy/95xK87i6poRUdf13e/jdOLiKWsKwWlX5RiHAAIIIGAECE3zHiCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQKYVaP/JBv118LLd+Va8Xkdh+RLvkpyaYv4zaY/G/Hk8xVNNF+SNb9dXcIC307nOQtONy+aW6bDsymU6U28Z0kBmn8SuOZvOqPf3211ZzmHM1GerqYGTbtbJhabNQotfra1mH65zad9Cuf20bmA9h7HD5x/S0HkHXVojblCVIkH69eWa8vBwnJaeoenYWKnhu2t06FxEis5vBlcrGqR5L9dM8TwmIIAAAgggkFYCA5d/pJm751vLZZXAdFztBKfT6i1gHQQQQCD7CRCazn7PnIoRQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyDICZV9boavXo+3Oe3hoY3l5OknMprIqE3pt8M4ah9nenh6qXyqXiuX1k4c8tPHwZW07dtVhXK8mhfVO59JOd3cWmo4/sGKhQFUoFKio6Bgt3XlBlyJuOKzTr3UJvdSquNP1TYfsOoNX6+SlSIfPTTC3atEgRcfG6uCZCP2596LDmFKhAVr+Wh2H+6Zj9QezD+hi+N/nOXg2wiHo/Wzzovrin87cpvN0s4ohqlI4yNpn9qYzDmtOebaaGiYIaE9ae0pT151SRFSMrt+IUeSNGF2LjNHxC9et3xO7Xm1TQi/e6WhigsxDfz2oUwk8xq48YbeUebY96hVI9o3p1biwKhYOdDpu4pqTenG8Y5dsE3RvVDqXCuTy1eWIaK3ce9Hp8/mhd2WZztlcCCCAAAIIpLdAVg5Mx1nFD07n9A3SyDZDVT6P838fS29f9kMAAQQQyLwChKYz77PhZAgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAghke4HCLy+zMwj299aO9xqmqcvDI7dqwbZzdmtWKhyoUb0qqXgef7v7v2w8o2fH7JAJFce/Vg+op6Ihfg7nSiw0nS/IRyY0W7N4Ttuca5HRevz77Vq847zdOnVLBmvGc9Wd1jx6+TG9MXWvg9HIRyvKdLSOf20/flU9v91qBZLjX58/UF5daudP0nTJzvO67+stdmNM92izlglCT3i6qkwQOe566eddmrD6pN34d7uU1qONC7v87KKiY/X7jvP6dNFhrTtwyWHe+kH1ZcLarly1B6+2qzuxzteurGXGmLNVHbDSIeTevU5+vde1jExwOu4ywfYP5x7UZ4sOO/itGVBPaZj/d/X4jEMAAQQQyMYCpru0CU3HXV3qtFXNsKpZUoTgdJZ8bBwaAQQQyFABQtMZys/mCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCQmIDpOhz26h92H1cpEqT5r9RMM7R9p8PV+P21duuFBPror7fqyc/b0+k+0/46pWd/2mn32Vt3l9KTTYs4jE8sNL341doqXzCHw/gL126o2sCVdqFsE7DeNLiB07M468Sd2NpmgSPnr6vekNV2azUtH6LxT1ZJ0tRZaNpMKJM/hxb1qyUfL/vO36Yjd8uhf9mtaTpTv9G+ZKqe3fPjdmry2lN2c0f3qqS7quR1ab20Dk3/uOK4+k/eY7d3q8p59f1jlRI9zwvjdsp01o5/GTvTbZwLAQQQQACB9BBYfOgPvbRokG2rrByYjiuC4HR6vDnsgQACCLiPAKFp93mWVIIAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICAWwmcvhyp6m+tsqupZaU8+rF3ZYc6TRflhQm6RTvDqBsWrAalc9k+mrH+tJ4es8NuqFnf7JPU1eCdNTp0LsI25MGGhfTf7mUcpjgLTTevGKKfHk88pNx2xAZtOHTZbq0jw5o4dCQ+dzVKVQastBvXq0lhvdM56f80/X8m7dGYP4/b5plu2ivfrJtkvYmFpkfcV0731C3gMDc8Mkal+9sH3k03a9PVOjWX6excot9yu6mv3FVCr9xV3KXl0jo0nbCTtumyve6tegrNmXjna2fv88hHKqpttXwu1cAgBBBAAAEEbkZg57m96jX3ZV2NvGYt4w6B6TgPgtM382YwFwEEEMheAoSms9fzploEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEsozAxfAbqvjGn3bnrVUip2a9UMOhhu//OK7Xp9h3/nVWaMJQ8cfzD+mjeQdtQ0349eBHjeVh3zjZYamEweOGpXNpyrPVHMY5C00P6FBSTzcrmuhzeOrHHZq54bTd57vfb6RAPy+7e+sOXlaHTzbY3Zv7Uk1VLxaU5DOes+mMen+/3a7mQ0MbJznHWWjaWO14r6Fy+NqfK26hVyftlgk7x10da4SqWYWQVL9/jd5bqwNnwm3zk+vsHH+jtA5Nd/h0o9YduGTbIrHnn7DYhGH719uFqU+LYqk2YSICCCCAAAKuCFyOvKLuM57QiSt///uFOwWm4+onOO3Km8AYBBBAAAFC07wDCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCQKQViYqWiryyzO1uBYF+tH1Tf4bypDU33GbtTU9edsq3n5+2pcU8m3gU6buCE1Sc1cc1J27xCuf20bmA9h3M5C00n11044ZnMos5C05PWntIL43ba7Tnx6ary8kw68b3zxDWHgPnWIQ0UEuiT6HvgLDRdv1QuTevjGBRPzcu0/dhVjVt1QofORujI+es6ev66rkVGW7UUy+OvMgUCNG/zWbulqxUN0ryXa7q0XVqHpqsNXKkzV6Jse9ctGaz+bcOSPYsJ2+859XeHT3PdW7+Ahvcol+w8BiCAAAIIIHAzAr3n9tXaExutJdwxMB1nEz84XT5PaY1sM1Q5fZP+MtnNuDIXAQTSVmDP+QN6fen72nvhoB6q3E0v1OmdthuwGgKSCE3zGiCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQKYVKN53uW6Y9PQ/l+lu7KwrcmpD062Hr9emI1duun4Ttt7/4W0O6zgLTc98vrrqhAUnuqeroen/zjmgTxYevumzmwUW9aulioUCE13LWWi6c638+qJn+Zvaf8Wei3p/zgG7rs2uLphRoWlnYX5Xz5xwXNPyIRrvQkg/teszDwEEEEAAgY9W/U9jt02zINw5MB33pBMGpwc36ScToM7I63zERQ1Y9qHdEYY0eVUh/rky8ljsjUCaCZy5dk7h0RHWenn9Q5TDJyBVaz+/cICWHl5pmzutyyiVzJV5/6sslyKvaNWxv7T+5BZtOLlFBy4dsf5eFw8uoh4VOuqO4o1S5cCkWytAaPrW+rI6AggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgjchEDlASt1/uq/HX3NUjvebajgAG+7VX/dclYfzDngsNPeU+F2oeteTQrrnc7/BmcavbdWB86E38QJ/54a6OdldYNOeDkLTf/yfHXVToPQ9BtT92r08mM3fXazwPLX6qhUaOLhBmeh6afuKKqBHUumev8p607pubH2nbJTslhGhaZNB+wy/Vek5KiJjr2zUh790LtymqzFIggggAACCCQUmLl7vgYu/8i6nR0C03H1xw9Om07Tg5v0VbPijl9uS683ZvrueRq0fJjddoMav6JOZVun1xHYB4FbKvDY3Fe07sQma48hTfqpQ5lWqdov/jpmgXEdvlClfJnzv8qy8dRWPT3/NV2LSvz/S9YuWE3fth4qT4+k/0tAqcJiUqoFCE2nmo6JCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCBwqwXu+3qLTGA3/vXVQxXUsUaoS1vf8+VmLd99wTY2YWi657db9Nt2+/VdWjjBoHxBPto0uIHD1FsZmh617JjenLY3Ncd1mLN+UH0VCPZNdC1noWkTmDbB6dRcW45eUath6xOdajqK+/l46kZ0rK7fiHE6LqNC0+YwJV/9I9FzpcQjLbp1p2Q/xiKAAALuIDBl52wNWTFC9QvXUr1CNVQlXwVVCS2vQJ8c7lBemtUQPzDdrNJtal6pcZqtnRUWunD1or5YOFomQG2up2o+pKdqPJghR0/YPdccokmx+vqs5TsZch42RSCtBdIqNG06Nj+38E1dibyqlmFNNLTZwLQ+apqsN3HHTL3352curfVc7V56rNp9Lo1lUPoIEJpOH2d2QQABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBVAgMnXdQw+cfspsZli9AK16v49JqyYWm35m1X//77YjdWq+3C5OPt6dL68cNMqHprrXzO8y5laFpEwY39cW/utTOr6pFg1J0djP40dsKyTeJmp2Fpj/sXlY9GxZM8V5mwlM/7tDMDaft5lYqHKi+d5VQs4oh8ot3lphY6dDZcPX8dqv2nf63k9vNhKYTC7m7WkzLoX9p27GrtuHmvP3bhbk63TbO1NCwdK4Uz2MCAgggkJ0FvtowRl+t/9GOIJdfTt1WtJ4aF6lr/TS/Z+dr57m9enTuS7oWGa6Khcvq/kZdsiXH/tOHNGrJeFvtHcu20uDG/dLVIiY2RnV/bKvomGgF+QbKy8NLF69fkpenl9Y8NEeeHin7d850PTybIeCiQFqFps12MbGxCr8Rnqm/CPPakvc1d99vlo6fl69ervukGhetp8I5C2jr6Z0yX5Q4F/H3F3cr5S2rcR3/56Ikw9JDgNB0eiizBwIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIpEpgzf5LuvuzjQ5zf3q8ippXDEl2zeRC01PWndJzY3farZOSTtbJHeBWhqZPXY5UjbdW2R3h3voFNLxH2v8nrNM6NF2873LdMGnof67yBXNo/iu15OOV+H+6uuagVTp5KdI2JyWh6WYfrtPOE9fsrI4Oa6LU/pey+4zdqanrTtmtt/Ht+grNmXi37uTeFT5HAAEEEHBdYO2JjZq/f4n1z4Xrl+wmxg9QNy/RWP7efq4v7AYjL0de0V2T7rcC07lzBOvZO3vJ3yd7GcR/jCt2r9XcjYtstx6o1Fn96j+Tbk/adM59dM5L1n5tSjWXh4eH5uz9+zyj2g5XrQJVXT7LtahwbT+7R4WC8lv/eCjxf2+Kv2hUzA1diLio8xEXrcC2j5ePcvsFW/8E++W8pcFtExbff/GwzHtZOiRMwb4p/3KfqSVWsTpx5ZSOXjmhAjlCVSRnIXkm8y9yGVX39ehImS8ueHt4qWK+si4/J5dfhGQG3qy5eVYXIi7pfMQFRURHWl9CyeUXbP0M8PZ3unv80PTbjfvq7rJ3pVU5Lq8TGR2lPef3W+PLhJSUr5ePy3PjBrr6d8wEu19cNFB7LxzQd22GqWCg/Zdnv988QSPWjrSWzeEToBU9Z6b4LEy4dQKEpm+dLSsjgAACCCCAAAII/J+9u46yqurDOP4QEzB0d3cj3SnSSEsIYqKovBJKKBKKKLagWCBICEpId3d3d3cPE8zAu/YZ5869M3e64bvXYnHvOfvs+JwzF/547m8QQAABBBBAAAEEEEAAAQQQQAABBBBAIAYEgodlzZAFMqfQ4j7llcotWZgzhBea3n/hvhp9tcthjJzp3bSyfwWldg977IhsLTZD02b+4OHj5EmTaEmf8iqewyMiy4twn5gMTd/38VeRgRsd5h7YLJ/eaZA71PWcu+mtKp9sczgfmdD0a38c0oK91x2uXzugggplSRlhA/uOY1ee16fzA0IZga1lucwygXsaAggkXIElp1Zr3vFltgV+ULWXcqfO4bDgQzeOaezOP6xjLQo9q+fy1024G2JluuF1S0tPB4SnTTg1eMuZOpt1Dxvlq6NiGQs98WIm7Nh5QS+du33R2utbDXsoe7qQvwnjiYcItsFZ2xZo15mg58NUmzZVp+OifbX1Z/154B9rqs/qDLK+sDVg9UjrfdeSbdWvcs8Qy9hxeZ9eWdTHOm6C1p2Kt9KwDd9YAc3AZqrbDq/VP9TPKBOcnXFknv49tkSHbxwPdavdS3fQexVfs87/tGuift492Xrdu+Kr6lG6o64+uK5G0ztZxzK4p9PKTn9br4eu/0pzji22Xvet/IZeLNnOYY6jt05q4OrPHNZsOphq2/0rv+k0VFtnalsr1G3fxjz7qfKmyaW3lw3WmbtBvx3G7P/zuoNVN091h/5R2XdMPQfn7l1UnxVDdey/4G7gfruVbGdVIe48r5c1VaP8dfRF3Q9t0844PFcjN/3g9Jw5eMfnnupMDaoWv7tH0L9j9muPinng9UdvntTUg7Ot6skm9B1a2/nSEitkf+HeZTX758VI0Zl/U0fUet/hmuEbvtGsowudjhPRsPHeqwc1cO1n1prsmwkyj6o7SOWylAwxfkz8jJng9OPHj6yq8cHb6K0/acqBWdbh/Glza3ab8ZGyonPsChCajl1fRkcAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgWgK/LjyvD4JFlA1Q2ZK5aKpb5RSqZyhV6wLLzT90P+xKgzbouv3Hzqssnh2D819t6w8wgllh7e12A5N95x0WHN3X3NYhlvypFrWr3yUA8HO9hSToem7Xn4qNniTwzRv1c+lD5vnd8rp/+ixXh5/UMsO3nQ4H5nQ9OcLT+u75eccrq9ZOJ1mvBnx6o72F5+46qVao7aHWG+36tk1qt2TH8oL77nnPAIJVeCHHeP1+95ptuV1LN5SA6u+47Dc1Wc36n8rPraOmcCgCQ7SEofAlos7/wtQr7Wq2gZv9fPWsMLTjfLXDbdCbeLYcchVfrDxUy05sto6Ua9EDdUvUTOxbiVG1+390Ee/r56qy3eCfkvEwvaTlSNV1hidx9lgJlgaGOZc03mmpCS2AKwJdS7uMCXEZfaBzkrZy1lBYhNqdda+bTAsRHDY9Ou3ariWn14X7v4GVXtHHYq1tPqZELQJQ5vWuURrvV/lLW27tFuvLe5vGycwNPvm0gHadGGHdfyzOgOtcHdgM0Htj9d/Gebcpr+5zr45C02bquCzjiwMEb4OvG5Zx7+UOWVG2zBR2Xe4SBHosP/aYZlqy6EFjt9+pofG7JwQ8LOZp4a+aTDUNqp9aNqEq01Q3L5FJDQdVXMzjwnVd5n/tkzgPKxmH2KOWmi6kUbUCnqWzFxhhaZNGHlH94Bgfmjtr0P/atTmMWH2MV+Q6lT8eYc+MfEzFtqk5ssNnef2sj0LbYo01ZAaAdXmaQlDgNB0wrgPrAIBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBEIRMMHm6iO36cItH6c9+jXOqzpF06t49pRK6epY6av1mL3acvKO7bqXa+XQJ60LOoyz8fgdtftxb4ixTfj41do59GrtnMqaxjXE+Ttefjp6+YGyp3NTrvTOf+19bIemb3k+1DPDtsrH71GI9ZnKx72fza1i2Tysqob2zfQ/fsXL+lXnYYXOA6+JydC0GbPwwI3y9AkKZpgK2Sv6P6PCWR0rPz/w9ddLvx/U+mO3Q+wvX6YUmvhKCeXJ6C5zr8Jqs3Zc1dtTjoToUq1gWg1pWUClc6VSUjsj88xduB3wvOXL6PzXkI9aeFrfBwtim/7mWenzXB61rZAlxPNozl+566tjVx6ofJ7U0Q7l86GBAAKREwgemjaVStd1mSPXZC62gQhNR840IfY2lXFN5emlp9fKVCAN3gqky2OFp+vlraGiGRz/T5AQ9xPRNU05MUuj1/5kdc+WNot6Pdsjopc+Ff1ue97R2OUTZALUptXNU03fNhgeq3u//uCmGk7vaM2RO00OzWs70XrdYmZ3nbsbUA08eOjXHLMPdJrgqAmzmirPJkB98MZR27Wmb6aUGbS843SHfcw6ukjDN3xtO2Y+60y13eypssrDNaUeP36sBw+9dNP7tnqWe1ElMhWx+m65tEtvLA6oBBxYDfmfI/P1ycbvbGMtaPenTAX3trNfswWZp7YYaxvjtvcdNZje0RbANXPXz1tT7sndtP3yHoe1/97ka1XIFvQFNlPp+obXbZ28fUY7r+yz5qyWs4IVzjYVqpsWaKCL9y9r/fmttvXYf7klqvuOiYfghblvOlT0blqwgfKlyaVDN45r1dkNVjXiwFByTIemo2Nu9l5zyvO67+tpYzDPasmMRZXGLbX17+PDRw912/uu9QyaALJpJsj/w46AELhp844vtYWEy2QpoSLpC4RgrZS9bIjK6ItPrdKG80FfRPR86KmVZzZY14YXmr7udVMN/wr4+QrsXz1nRev1xgvbHULg5mfE/KwEtuj+jIX2zJjfePDq4n62uc0elnSYqkwpguaOieeNMaInQGg6en5cjQACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACcSCw/8J9NfpqV7gzmerTWdK46r63vy7e9pHfo8cO1zgLTZsOvSYf0eydQdUHg09kQr0ZU7lYIVcT9r39wM8WVH6rXi592MJ5leTYDk2bdc7YdkX/m3Y0TJv0Hi5KnzK5vB4+kqn0HBhYLpHDQ8v7PRPi2kojtlp7DGx+/o9DBLONiZuLY1i5d8PcertB7nDvU/Awe+AFJvxeMEsKXb/3UHvP39fp6162sUwY2QSOnTUTmv6rZylVKZDW6XkTEjfhchMyD6ulcU9u7TMwhP5M3tSa37uc00tMsLrqp9t06b9wdWjrMutOmjSJ7nv7WaaBz6Spcm2qXdMQQCDuBIKHps3Mw2r2U6vCz9kWQWg67u5HXMy08cI2LT211qpAbUKiwVuNnJVUN2911c9TXRkTcajths8tNfu7m7wfeltbfKthD2VPlyUuiBPVHBuPbdeiPStsa57ealysBuftA8ddSrZR/8pvWnN/uXWcJh8wVaelwdV6q32x5g6O9oFOc8IEh79v+Ilckia3+gUPMq/rMlupXYN+88qgtaO08ETAPgunz69Jzb9XiuTOvwRmP7EJcptAt2kmZP1Hs281eutPmnJglq3b2EYjZX5u7IO2G7r+Kw+XgC++jdj4rWYeWWC9NuHqyc3HKL17wP/PzJf1Rmz4VrOOLrTel8xUVFNahKwSvOTUan2wOqjasgnrznj+Z1vw9Jvtv2rivhnWGLVzV9X3DUdYr6O67+g+xObLGd0W9LYNYypFm4rRgc2EvN9eNtj2PqZD09ExDx48NhWwzfoi20yV7R2XA76EaqpJtyjUKLJDWP0ve15V4xldrNfhhaZNVXRTHd00E86f0mKsCqXPZ70PXu3Z/Dtv/r0PbNH9GXO2Ofv/PwSuf0zDT62fX1rCEiA0nbDuB6tBAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAIFQBExF6BfG7QsRhI4MWGihaRMkbvDlzlCrWYc1R6OSGfXHKyWcdomL0LSZ+LU/DmnB3uuRobD6mhD4sc+qh7gu//sbnFavDm+CXvVzaXBz5wFy+2u3n76rlt/vCW84h/NDWxXQ0H+d/2p60/HP10qqQfHQq7itOHRTL/56IFJzmhD+3uFVQ71mx5l7uvr6oQAAIABJREFUajd2b5SsRrYtpJdqZI/UeuiMAALRE3AWmi6YLp9mtv7VNnBkQtO+/g91/NYp69pC6fM7VKyO3kq5OqYFLt2/YlWeXn56rfZdOxxieBM4rZenulV92vyd2NqoHWP0195/rWWXz1tKbSo1S2xbiLP1/r5mqk5fO2fN90a5rnqzfEBIODbam0sHWFWSTfu58Reqkr289Xrrpd16fXF/67UJVP7UaJTD9MEDnYs7TFE2j6AQvPkCQPXJLW3X2Fd6Ngf/t2KIVp/dZJ03VYNNcDmtW+pwt2g+0ypPamq7zlTGNmFfE/otlrGQVUnZVBruVPx5lZvwrNXPhFW3dAsISZtmKmubCtumBV+XOebl5626U9taVYmDXxs4RvDQdFhfbimSoYBmtPrZujSq+w4XJpwOY3f+oV/3TLF6lchYWFNb/hjiChOqDqx8H9Oh6eiY2wflzaKDW0fUJj5C0/WntbeqpZv2SplOeqfCyw7LHbNjvH7bO806ZoL3Kzv9bTsf3Z+x4C7mZ7LetHa2atumMvqfzb5X/nR5IkpIvzgUIDQdh9hMhQACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC0RM4cvmBek89YlUhjkoLLTRtxvJ/9Fg/r76gUQtPRyqYXSBzCq0fGPCroIO3uApNm3mXH7yp3tOOhltNOfgaT4yqoRSujhWjYzs0bdbgzCa0e1ohXxrN6lVGefuvD/W2hxeaNheOmHfKmjcy7cJXtZQkSehX3PHy08dzTloVvyPTwnoWIzMOfRFAIOIC9qHptG5pdMfnrnWxqWBaJH0B63VEQtMm+DZw7We6cO+yw+Qm1Diq7iCrQist4QocuH5EGy5s18bz27T7asgv0xRIl1cN89VSw7y1ZAKZiaHVmNJKnr4PrKX2bdJT6Tyc/+aFxLCX2F7jqWtnNX5NQJCyW9n26vPM67Eypf8jf1X+s5nM36Zt677IVin64SM/VZrYxDpuqulufXGB9Xdgsw90hhYsNtV4TVVe035v8pUqZCtju3783r/0/Y7fHfZVJksJq0J01RzlVTpzcSVN4vh/v8DOVSY1s4KfJvS5vsscNfvnReuz7u1nemjMzgnqUKylej3zkupMbWNdYv/FE1NJuvyEgArDZt1ru8x2att5bi+rErBpG7vOVUqXFA79goemg/cxAdUzdwP+P+ee3F350wb8lpPo7Ds6D4F9heveFV9Vj9IdQwz31daf9eeBf6zjMRmajq75o8ePVWlSE9tzatZn/n2slauyquWsqCo5ytsqfIdlFNehaft9m3WZ4Hzwz+ujt06qw5w3bMve1WOpkijgP/XR/RkLbrHhwjb1WjrIOmye51mtf3P4okN0ni+ujXkBQtMxb8qICCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCAQywJrjtzShPWXZP728XsU5mzJkyZRqVypVKdIOrWtmEWFsgT8+vDQ2tV7vho+95Q2nbijS7d9wt1JzvRu2vZR0K/gtr/AWTB49QcVVCRr6Gt4e8oRzdoREIIJbGdG15RLsjCSu/91fOj/WN8uO6vZO6/p/E3vcMPfxmbj4ErKld7NYb6SH22OdPjaDDCoWT693SAguBKRtu/8fQ2Zc1JbTt5x2t1Uwjbj9aybU27JkyqsMHdEQtNmkoMXPa3w9OYTd8J9dkz//SOqKoOHS7jb2XPuvj6Zd0oHL3lGyO6FKln1dcci4Y5LBwQQiDkB+9D0W+W768ddE63BWxRqpBG1Aqq+hhea/uvQvxq1eUyYiwqsxBpzK2ek2BI4fuu01p7bLBN423F5b4hp6uapbgtQuyd3/LcyttYU2XHv+d5XrSmtrcuypc2iXs/2iOwQT13/scsm6PKdq2pasL5G1h4YK/vfdmm3XvuvmrQJoX5df6jDPIPXjrKFnn9pPFqVs5eznbcPdGb1yKwlHaaGWGNYoWkTyjYh1sDKxsEvNgHtbiXb6dWyneXh4vh/0sCQtLlmd49lqjCxsXX5hKbfqNv8d61q2f2rvKV2c16zjtfPW8O2t2sPbujZ6S9EyvPfthOUN00uh2vsQ9PGbk3nmREaMzr7jtAEoXSyryL9WZ1BalKgXoieJjBtgtOmxWRoOibMV5xZr/dXf+IQnLbfgKky3r/ymw7B/OAbjOvQdPB9m2fEPCv27Y7PPVu43xxf2nGasqTMZHWJ7s9Y8P1PPjBTX24dZx1uW7SZPqr+v+g8UlwbywKEpmMZmOERQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCB2BU5c9dLluz66cf+hHj2STOWxbGndlD2tq7KkcVVK16DKfZFdyePH0vlb3jp2xUt3vfz0wNffqsqcyj258mRwt/4Er9Ic2Tlis//1+w91/MoDXbv/UHce+FlrTeGSVDnTuytfJnelTZE8NqeP8Nj3vP119oa3Tl33kp//Y5mwdO4MbiqYJaVDWPzQRU+ZAnEmQO3uklSu//1t3kckVG6/IHNvj155oBNXH8jM75o8iXz9HiulWzKlS5FcuTO6W2FyEyyPbDPhdbMXY29C/WZcY2+882ZKodzp3ZQsCuNGdh30RwABRwH70PS3DYZZoemjN09aFV7XdZ5tVYcMKzR93eumGv4VVEHUXFc9Z8BvGth4YbtD4Gx5x+nKlDJDgrsFJmhmKrSa6qKPzR89snv9OOC1HlnnAl6bfv/1sV5bZ61jQa+DXfdfv4DzAf3s+zqd04xnZg5tTvt1RWL8gLX+t4fA14Hz2M/5+LG8/b107cEt3fC6ZatCHngDTVXeUpmLalKz7xPcPTWh6cZ/d7EqTWdPl0VvNSQ0Hd5N+mrRON32vKOvGnysBnlqhtc9Suc/3zxW0w7NidC1LxRvpQFV37b1tQ90mgr2iztMCTFOWKHpwM5bL+3W73umavuVvU4DsaYa9D/P/6rcaXLYxjdBbxP4Nm1eu4lq8U935UydTX+1/MkK55sQ95Aa79mq6r5appPervCy1d/87DT4q0OE9hzYaXGHqcrmkdnhGvvQtAlUm2B1ZFpU9h2Z8YP37Tz3LR28ccw6/FmdgWpSoH6I4aYcmKXRW3+yjkc2NB3c1YTZA1tMmZvPkT/2zdD8E8t1xfOaU442RZpa995ZS8yh6ej8jAVaTNg3Xd9t/816O7hab7Uv1jw6jxTXxrIAoelYBmZ4BBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgfgXCB6avufrqY/WfWEtbGDVd9SxeMswQ9ND13+lOccWW/1N2HBKi7EqlD6f9f7E7dPqPLeXfPx9rfetCj+nYTX7xf+m/1vBg4deOnjjqExF0WkHIxbkTDCLTyALSe3qoXVdEqbdR+tGa97xpZaUCU2b8DTNucDGY9u1aM8K6+S6LrOV2jVVrFA9N6NzqOHT4BMGryYdU6HpwHn8H/lr3/XD1pc7Vpxeb31eBbaauSprzLOf2t7bf84NrdlX5n1gnyqTmlmfcUNq9NHwDV9b13xae4CaFWxgu95UpjbzmWaqE5svo4TVmhVsKNdkjr/Rwz40XTJTUU1pEXZ1/9DGj8y+o/MQ9F81QstOr7WGCO03DUQnNH3k5gl1/LenbYn2oWlzMCbM7fd//cFNbbq4QxvOb9OyM2sdAvezWv+mAunyhuCyD01/WL232hWNWmj4sudVmS8EmGa+mLSje8C/ucGb+ZJN+QmNbIentxqnohkKOnQzX4rq8O8btmO7eixVEvPty2CVpmMiNG1+Lm57B/zmnPTu6UI809F5vrg25gUITce8KSMigAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIJDCB4KHpGrkqq+bkVlYI0FRSXdDuzzBD0/WntddN79vWrl4p00nv/FddNXCbY3aM1297p1lvM7in08pOfycYgbnHl2rIutEJZj2JdSEL209WjlRZE9zyTZXY9v++ocv3ryp/5jx6uU6nBLfGhLAgU1167PIJ8n7oowrZyuj3Jl/FyrKCBz87FG3hdJ5ZRxfavmhhqkmb8KZpMR2aDj75N9t+0cT9AZ9PwT+rftk92arCb1rzgg2tysPdSrVTn0pvqO3s16zAdesiTTT76CKrz+TmP6hU5mK2KVrPelmn7pyz3veu+Kp6lA6qzh9R7JgKTUdm3xFdm7N+32z/VRP3zbBOBQ+hB/Yft/tPjds1yXobvNL0v8eW6OP1X1rnggfozTHznAzf8I1t6uCh6ZgwD23/5rOl7rR2tuD0iFrvq0WhZ0N0H7R2lBaeCPgyQpeSbazAfFRaREPTZuyG0zvKBLxNM8+Zed7sm/2/+cGf89j+GYvK3rkm7gQITcedNTMhgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIxJNA8NB03TzV9fnmsZp2KKB68KTm3+um1y39b8XH1nv7EFbwqpYzWv2sIhkKOOzk6K2T6jDHeVXLeNqybdp157do4r7oh7j9H/tb4Tm/wL8f+csc83vk99/fwc8HHY9vg+jObyrejqw9QHnT5oruULFy/fbLe/TqooDq5vVK1FD9EjVjZZ7EOqgJSo9fM1WXbl9VKlcPLWo/OdaqTJvPFPPZYlrdPNX0bYPhTtneWzFUq85usM7ZVyiO7UCnfTXp4CHdeceX2Srwm3NXPK/JVJx+vnBjvb/6Ey09tcb6ksmFe5etdQev1v3rnikau/MP236H1HhPzxduoqRJAir8RqTFVmg6rH1HZF2h9Vl3boveWf6h7fTUFmNVIlMR23vzxZx2c17TubsXA34+89TQNw2G2s7vurJfPRa+Z3s/oek3Kp+1lPX+js9dtZ3zmi0cbI4FD03HhHloe7t4/4pazOxuC01/XnewnstfN0R3+zWY38Qwt+0fVgA8si0yoengv/1hcosxKpw+vzXl8Vun1WVe0G9/MM+veY4DW0z/jO2+ekBfbR1nOfWu9JqqZC8f2a3TPw4FCE3HITZTIYAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCMSPgLPQ9Ll7F9Xin+7Wghrlr6OmBeo7DU1fe3BDz05/wbbwNZ1nKq1bGoeN3PG5pzpT29iOLe04TVlSZoqfzTKrU4Ftl3Zr44Xt1p8jN0847VMpe1l1LdlWdXJXS3SKc48t1ZD1ARXFO1dvo+I5Cie6PcTWgsevmaZT185aw5vAqgmuxlZ7ZVFf7bi81xp+SI0+alOkidOpTLXmYRu+ts7ZV76ObqDTVC3ecnGX8qbJqcwpM1oh8UePH+u2zx1tv7THVjHfzNukQH19VmegbX32cwcenNjsW5XNUlI/7Zqon3dPtvU14dgt3RY47M2ERpv9000m/BrYzGdltRwVlC1VFj167K+rnjesitWj6g5SwXT5rG77rx3W7GOLrdenbp/Vziv7rNdm7YEhXdekLupT+Q25JE3u1DM6+47us9DsnxdtQfJkSZOpRcFnlS9tLl19cEMLTqywws+BLXhoOng1Z9PPfCknc4qM2nV1vxXENcHrwFYmSwlVyFraVlk5quZmPBOK7vDvG8qVOrv1J41rapn76uXnrZN3zmrv1YMONOY3MpjQfPBm7l/X+e84HK6Rs5Jyps5ufaHG/BvqmsxFX9YbYutz+MZx/X1kvsM19309ZULzga1t0WYO599+pofSu6e1jl33uqmGfwVVMjfu5jkzbdPFHbagt3m/vON0ZUqZwTZWdH/Ggu//uRmdrS8YmGae99Wd/1ESRfyLAtF9/rg+cgKEpiPnRW8EEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBRCjgLDRttvHi/He079pha0cjavXXR+sCQqf2laYJTSfCG/7fkjec32YLSp+6ExCaddaq56yo1kWa6Nl8tRPvZiXr+Z13fKncXdz0cp3Oyp4uS6LeT0wsftb2hdp1OiCEO7xmf7Us3CgmhnU6xsNHfqr6Z3NbYDOsL0/Yf66YwOfmF+dbgeDoBjrbz3ldx26dCnePJkT6b5sJ8nBJaet76f4VNfm7q8O1azrPUlq31FaY9YPVn9rO5U2TS/+2nRBiHlN1/62lAx2qIztbjAlNN85fzzo16+giDf8vQB7Wwld2+lsZ3NM57RKdfYeLFU6HLZd2WXs2AWZnzVhff3DTOhU8NG2Omercplqzs2YqJI/Y+K3D2CaYa768E9iiYm6u3XJxp95Y8kGEtj+o2jvqUKxlqH0HrR2lhSdWhHrePOM7ugcE400zfc01kWnmeTPPXWCbcXiuRm76IcwhBlR9Wy8Ub+XQJ7o/Y/aDPXr8SJUmNXW4P5tenKcUyd0jszX6xqEAoek4xGYqBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgfgRCC00vez0WvVfNcJaVOnMxWwBavvQ9GM9VvkJQUHL6a3GqWiGgg4bOXrzpFWxM7Dt6rGUSpPxc6tlqn4vPrlSi06u0u6rB0JdhamoWi9Pdavabp08ia+ydGgbIzgdJGMfmO5SorX6V3krVp9KUyH55YV9rDlMUNZUuA2rNZze0Ram/b3J16qQrbRDaDp3mhya13ZiiCEaz+hiq+b8e5OvrErVgc3+nLO5U7qkUKtCz6l76Q7K5pHZoYsJ/VaY2Nh2zL6atAnmdpgT9BlXN081fdtguNPt+T3y0/i9f2nKwdkOVZbtO/et/IZeLNnOOhQToeno7DsmHorjt07rvZUf69zdiw5+zQs9q/xpc+vLreOs485C06YS+Pi90zRmZ1AI3dibaz+q/j/VnPK8TBXmwBY8NG2OR9bcXGM+IweuGRnm9k1l607FW1mfk2E18+z8vneaJh34x2Gt9tcEBvDNsaiEphe1n6zsqbI6LGPvtUPWHi7cu+xw3FTEHll7gFUlPXizD01H5Wcs+Hj2ofdOxZ/XB1V7xcQjxRixJEBoOpZgGRYBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAg4QiEFpo2VSJNIO3BQy+HxdqHps0J+3Bj8HPmvP34phKqqYhKi1uBQzeOWSHARSdXylTxDa1Vyl7OCkubP8EDeHG74tib7X8rPtbqsxuf6orT9oHpnuVeVM/y3WIPPIGNfNv7ji55XpW3n49MIDdZ0qRK755W6d3TKY1rqjhdram+fe7uBd32uaukSZJa8+dMnV0mFBzTLSHs28vPW6fvnFMqFw+ZQK5pUw7M0uitP1mvnYWmAx3Mv0dn7l6Qr7+vCqcvoKRJklinrj64bn0JxzWZq1yTucgtmZvtnDPDyJibsPP5+5fk6ftAXn4+1rimSnKGFOms58VUP49su+x5zQr1m/0ks+55auVInS1W7nng2nz9HyrwtwnkT5vHcorLZu6R+VkL/kWEuFwDc0VMgNB0xJzohQACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIJCIBUILTZstfbf9N03Y51gRNngweuj6rzTn2GJLwIT9JrcYo8Lp81vvTYXRLvN6ycff13r/fOHGGlqzbyLWSlxLX3FmvRWUXn56XagLL5gur+qaoHTeGiqVqWji2mAUVnvP975eWdRXpgK6u4ubXq7TWdnTZYnCSInzEvvA9PCa/dWycFCl+MS5I1admAUiGppOzHtk7QgkFgFC04nlTrFOBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgSgLhBWaNhUiG03v5DB28ND0da+bavhXR1ufZEmTqVqOCtb7TRd3yFTrDGzLO05XppQZorxWLgxfwISCF5xYYf3Zd+2Q0wvSuae1VZSunbtq+IM+YT2CB6d7NeyhdB5pn7BdhtzO3O1Lte30LqVy9dCIWv2tyr40BOJTgNB0fOozNwKOAoSmeSIQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEnniBsELTZvNvLH5fWy7tsjkED02bEzMOz9XITT+EaTWg6tt6oXirJ94zvjZ47cENjd/3l1ad2ajLnldDLKNmrsoqm6WESmcuptKZi8vDJWV8LTVBzGsfnDaVpk3FaVN5+klsrklctHTnaq04sd4KTP/e5CsVzVDwSdwqe0pkAoSmE9kNY7lPtACh6Sf69rI5BBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAASMwbvefGrdrkoXxbYNhqpunugPM+vNb9faywbZjb5TrqjfLdw+Bt/faIQ1cM1IX7l12OJczdTaNrD1AZbOUBDwWBcbtnqRxu/60zZAvbW41yl9HVbKXt4LSrslcY3H2xDm0CU43+bur7vt66kkNTmdIlk6LdqzU/OPLrJs0vGZ/tSzcKHHeMFb9xAkQmn7ibikbSsQChKYT8c1j6QgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALxI+Dr/1Cn7py1Js+fNo9ck7nEz0KewllNaLp4xkKqnbuqkiRJ8hQKRH7LR26e0CuL+lrB6XQeadW5WhsrQP0ktLwuufTP9nmae2yptR0C00/CXX2y9rDgxAoNWT/a2tRz+etaX7ChIYBA/AgQmo4fd2ZFAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBOJMwD447e7ippfrdE70wekMydJqyc7VBKbj7CliIgQQQCBxCxCaTtz3j9UjgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAhESuOd736o4ffTmSSX24HTyJMm0YddWzT++3No7FaYj9AjQCQEEEHiqBQhNP9W3n80jgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIDA0yTwpASn1+3arKUn1li3rn/lN9WlZJun6TayVwQQQACBKAgQmo4CGpcggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAolVwASnP1z3hdac3WRVnG5atoHK5yudaLazYuc6rT650Vpvi0KNNKJW/0SzdhaKAAIIIBB/AoSm48+emRFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBeBMYuPYzLTqx0pq/TcWmiSI4vXjHSm04tc1aM4HpeHt0mBgBBBBIlAKEphPlbWPRCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIDA0yPg7eejn/dMVu8Krzw9m2anCMSRQP+1I7TsxFprNlNt2oSnE2qbv32ZtpzeaS2PwHRCvUusCwEEEEi4AoSmE+69YWUIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIBBPAuuP3dbBi54hZs+b0V3PlcoYT6tiWgQQQODpEzh686Tmn1imSfv/sTbfvGBDfVL7g6cPgh0jEMsCfdYM1cqTG6xZTHC6adkGcndxi+VZIzf87O2LtPP0XusiAtORs6M3AggggECAAKFpngQEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAgmED1kdt1+rpXCBe35El18vMaSpIEMgQQQACB2BRYe26z5h5fpuWnA6rfBrYv6g5Wo/x1Y3NqxkbgqRX4Zu+vmrhjhrX/7Omy6OU6nRNMcHrW9oXadXqftTYC00/tI8rGEUAAgWgLEJqONiEDIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIDAkyRw9Z6vyn28JdQtLfhfOZXPk/pJ2jJ7QQABBBKEgLefjxacWKGFJ1dox+WAarL2rVH+Ovqi7ocJYq0sAoEnVeC3w9M0ZtN4a3sJJThtH5juUqK1+ld560nlZ18IIIAAArEsQGg6loEZHgEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEpfAlM2X1X/GsVAX/U6D3BrYLF/i2hSrRQABBBKwwBXPa5p9dJEWnFyhc3cv2laaLEky+T/2t72f0PQblc9aKgHvhKUh8GQIjD/8l77f9Lu1mfgOTtsHpofX7K+WhRs9GcjsAgEEEEAgXgQITccLO5MigAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEBCFejyy36tOnwr1OUVyJxC6wdWTKjLZ10IIIBAohE4d++iFZaefXSxbnnfttbtlsxVLQs10vwTy+Xl52299/H3Ve3cVfV9wxGJZm8sFIHELpAQKk5HJDB97cEN/bZnqtKnSKee5V5M7OysHwEEEEAglgUITccyMMMjgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEDiEfB79FgF3t8g83dg61EzhyasD6p8ao7vG15VGVO5JJ6NsVIEEEAgAQmcuH3GCkvPObpI9x8+sFaW1i2Nupdqr9KZi6nPymG653tfKV1SyMfP16o2ParOIDUuUC8B7YKlIPDkC/x6eKrGbppgbTSuK05HJDBt1nXx/hU1/burtUbzGfJepdef/BvDDhFAAAEEoixAaDrKdFyIAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAJPmsCmE3fUduxeh22ZgHT5oVscgtTfvFBEHStnfdK2z34QQACBWBU4fON4QGXpY4vk6//QmiubRxY9X6SxVSF215X96rtymG5637YqTLcp2lTTDs5R0QwFNb3VuFhdG4MjgIBzgV8OTdaPmydaJ+MqOB3RwHTgijde2Ka3lg6y3rYv1kKDq73L7UQAAQQQQMCpAKFpHgwEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAB59SqWAAAgAElEQVQBBBBAAAEEEEDgP4GPZp/Q7+uCqkrny5RCGwdVVKef92vNkVs2p2dLZNDEV0uG63bympf2nrtv65c6RTI1KJ7Ben/1nq+mbr6s41e9dOGWjzzckqpkjlQqmdPD6uPhlizc8Z11OHL5gbafvqtT17x0/paP7nn76YHvIz30f6TU7smVwcNFGTySW5WyzetmZTI5VM328n2kJftvOAxdtWBaZUvraju26vAt3XngZ3tfJFtKlcjhYXt/+oa3dp+5Z3vvmjyJmpTOpCRJwt7S9fsPdeDCfe07f197z9/XpTu+Kpw1hUrnNC6pVCqnh1K6hu+y7MBNefr4O53MuD5bMuAemHb6upfm7Lom42a8cqV3U6mcqVSlQBpVzJcmSveAixBAwFFgz9WDmnNskWYfXWw7kT9tHj1f5Dl1L9XBOrbv2mErMH31wXUlSZJE89tO0ovz37EC1O9Ves3WD1sEEIh7gYlH/tY3G3+xJi6fr7TaVGwaa4uIbGA6cCHbL+/Rq4v6WW+bF2yoT2p/EGtrZGAEEEAAgcQrQGg68d47Vo4AAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAjEsUGnEVivAHNheq51Tw54voPHrLurD2Sdsx5MnTaLTo2sqaTgh4J9WndeIeads15nA7rHPqmvc6vMaPjfoePBtpPdw0W8vFVe1gmkjtMObng81ZsV5TVh/UT5+jyJ0TWCnpX3LWyHhwHbHy0/FB29yGGN0h8LqUjWb7VjhgRsdQskmeP3rS8Vt579ZelajF59xGGPV+xVUNFtKp2szax4487j+2nIlzLUbv59eLKaGJYJCz84uKDZok+56B4W67fuYe3f2y5rWoS8WndG3y86GOucLVbJqVNtCck2eNFKmdEYAgQCBrZd2a87RRVp4cqWNpHjGwmpdpLE6FGtpO3boxjErMH3xfsBnwI6XluiPfdP1w47xSueWRn8//4syp8wIKwIIxKPAjKNzNXLDD9YK6pWoofolAv4tjckW1cB04Bp+2zNVY3ZOsN7Wz1tTn9b+QCmSu8fkEhkLAQQQQCCRCxCaTuQ3kOUjgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEDMCFy67aMKw7c6DDbjzdKqWTidTOXk6p9uczg3552yqpw/7ErEwUPTZoDpPUur47h9EVr02K5F1fqZLGH2NVWZ2/+4L9SQcHgTHfykmtKlTO7QLU+/9fJ79Nh2rF/jvOrTKI/1/oGvvwoN2OjQ34ShTSg6sPWfcUxTNl926HNiVA2lcA0ZPj500VNdfzsg4x/R1q5iFn3ZoXCoYeawQtNmjn3Dq2r9sdt688/D4U7ZslxmjetWLNx+dEAAgSCB9ee3avbRRVpxZr3tYDaPLOpcorU6l3heyZMGfeYcu3XKCkyfvXvB6ruuy2y5JnNVx3976vSdc+pU4nl9UKUXvAggkAAEphyYpdFbf7JW8nKdTsqfOeD/BjHRAgPTqVw99H7lt9SycKMoDTvj8DyN3PS9dW21HBX0aZ0ByuCeLkpjcRECCCCAwJMnQGj6ybun7AgBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBKIgYKo0D54VVE3aDHFmdE25JAsoJ13yo8265fnQNnLPurk0pGX+MGdyFpo2IWwT2I1IM5WV94+oKrdQKh17+virwrCtUQ5M21ddtl9P+aFbdOWur+1Qt+rZNapdIeu9CTk3+HKnw/LN+k59UcN2rOuv+7Xy0C3b+8AK28H3bOYwc0Wl9W6YWx80zef00vBC0/++U1adf9nvUC07rDXsGlpFWdO4RmWZXIPAUyVw4PpRTdr/t5acWm3bt3tyt4CwdPHWypTSsUq8CUX3WTlMJ28HVKZf3GGKTLj67yPz9enG76xjfzb/QaUz88WFp+pBYrMJWuCjdaM17/hSubu4qXP1NtEOTns/9NHUjbN06tpZmcD0702+UtEMBaNlsPjUan24dpT8HvmrbJaSGln7A+VMnT1aY3IxAggggMCTIUBo+sm4j+wCAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQSiKdDhp30OYeYK+dJo3rtlbaO+PeWIZu24anufJ4O7Nn9YKcxZnYWm7S8ont1DxbJ76KH/I609cttp+Ll/47x6778qz8EnC2v8NO7JVT5vamVO7aLU7smVwiWpVT3a1++Rbnr66do9X6VNmVzje5QIsYem3+7W7rP3bMcblcyoP14J6Ddvz3W9MfFQiGuOj6qulK7JrOMNRu/UoUuetj4lcnhoeb9nQlzTa/IRzd4ZZBrYIWd6N1UpkFYpXZPqyh1frT16Wz5+jxyuN4HvHR9XVubUIcPMf266pL3n7lv9/R8/1l9brjhc26t+Lo1ded46ZgLf9Yunt+Yz1a5/XhNQ7da+vdswtwaEEtCO5mPH5Qg8EQJ3fe5p4v6/rcD0w0d+1p48XFKqacEG6lCshQqnD/kFk/P3LqnPyqE6evOk1X9m699UMF1e6/VLC9/T7iv7VT9vDX1df+gTYcQmEHiSBAKD02ZPbSo2Vfl8paO0PROUNoFpE5yOqcB04EI2Xdiuwes+102v2yqSoYA+rT3A6WdRlBbORQgggAACiVaA0HSivXUsHAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEYkrgof9jFfxggxUqDmwDm+XTOw1y297P3X1NPScddpgyvArEoYWaM6Vy0cRXS6p8ntS28R74+uu1Pw5p1eGgCs3mZKX8aWQqIztrr088pPl7rjucKpQlpca/XFzm76g2s0+z38BWLk9qLfxfOevtd8vP6fOFp0MMbULRJhxtWvCq3C3LZda4bo6VYo9eeaC6n+8IMc6w5wvo1Vo5lSSgwLfVbj/w05t/HtaaI4427Spm0fedi4a5Tf9Hj5W733qHPtnTuVkB6fQeLlrZ/xmHKtL/7rpmzWXfni2RwbpfNAQQcBR48NBL/x5bohmH5+nUnbPWyXxpc6tZwQZWYDpnqmxOyS57XrUqTB+8ftQ6/2fz71U6c3Hr9Zpzm9V7+UfW6y/rDVHDfLVgRwCBBCgweN0oLTi+wlqZCU2b8HRk2qZj27VwT8D1pn3TYKjq5Qn6rRWRGSu0vgeuH9GHa7+wPp9ypc6hkXUGqMx/nzUxMT5jIIAAAggkPgFC04nvnrFiBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCIYQETxu30836HUVf0e0bF/wsBmxMmuFviw00Ofb5oX1hdqzkPBZqOoYWmV71fQUWzhQw1mznKDNnsEN42Aeu9w6s63XH1kdt1+rqXw7nVH1RQkaxRD0ybwT6Zf0o//leJ2bw3IeMdQypb89hX3DZVmgMrQJtQtAlHm5ajzzqHNfV9Lq/6PpfH4djLEw5q8b4bDsdGti2kl2pkd7pXk2evNGKrFXYObGb+U1+EHbByFpo215tK1ZsGV5Kpam3fTP9CAzY6VLYulTOVlvYtH8NPHcMhkPgFXl3UT9sv77E2UjJTEbUr2lzNCzWUS1KXUDd33eum+q4cpj1XD1p9fn7uc1XJEVSJ/oPVn2rJqdXWeFNajE38SOwAgSdYoM+aoVp5coO1w/yZ81jB6XQeacPcsakqbcLSu07vs/UbXrO/WhZuFCtS5+5dtILTe64eUMYU6TWy9gCHz5xYmZRBEUAAAQQSrACh6QR7a1gYAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgjElcCgmcf1x4ZLtulCC+PW/Gy7Tl4LCinXKZpe094oFeoynYWm6xdPr8mvhX5N0293a/fZew5jnv+qlpLaVV4OPFn1k206e9Pboe+QlvnVs26uaNFN2nhJA/457tTDfn3Ny2ayVbp+v0le/e/ZPLrr5adigx3D5faB6sBBKwx3DECbythrPqjgUGE6+CYW7r2uV/845HD46GfVlcotWaj7DS003fqZLBrb1XmV6uBh9LCC69GC5mIEErnAstNrNf3QXLUv1lzP5a8b7m5u+9y1AtM7Lu+1+n7bYLjq5qlmu+7wjeN6Ye6b1vsBVd/WC8VbhTsmHRBAIH4Fvjn4qyZumWEtwt3FTW0qNVPxHIWdLurS7auavX2BzN+BLTYD04Fz3PW5pw/XfaG15zYrRfIUVsXpenmqxy8csyOAAAIIxIsAoel4YWdSBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBISALlh27Rlbu+tiU1Lp1R43uUCLHEYf+e1M9rLtiOm2rFJ7+oYVUtdtachaY/apFfb9YLPdTcc9Jhzd19zWG4Y59Vl4eTYPArEw5qUbBqzebCAplTWBWwGxTPoMJRqDq98tAtdf3VsfJ2YHC72KBNuuvtZ63PhI57TT5ivW5TIYvGdCmqo1ceqO7nOxzWv6zvMyqZ08PhWPBq1AOb5dM7DXKH+Vjc9/FXkYEbHfos6VNepXOlCvW60ELTf79VWjUKpXN63a9rL+jgRU/buRI5PPRa7ZwJ6ZFlLQgkOoH7vp7qu2qYtlzcZa19VJ1BalygnsM+vt72iybt/1u50+TQ9JbjlNIlRaLbJwtG4GkTuOF/S2MP/mFVjzZVpE0rn6+0mpZtYIWoA9umY9u18uB6Wx9zPC4C0/b3Y9Caz7Tw5Err0LCa/dSq8HNP2+1ivwgggMBTL0Bo+ql/BABAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBA4OkWMJWaTcVm+/ZVx8LqVCVbCJgNx2+r/Y9Bv07edAgrfOssNP3bS8XVtEymUNHfnnJEs3YEVWA0HUMLTc/bc11vTHSsvBx8YFM1u2rBtKpXLL0alshgBarDa86Cz3uHV1XaFMmVt/966/I07sk1r3dZ1fkvIF0mVyot7lNea47cUqefHQPXwddvAuomqG7f+jfOa60zvNZ2bECF2sDmrIq1/XlnoWkTcj8zumaYVa3DWwfnEUAg4gLefj5WYHrD+YDP2o9r9FHrIk0cBrjlfUft57yu61431euZl/Ra2S4Rn4CeCCAQrwJHfE9qzZXNGr9mqi0UbQLT1QtXUjqPtNp1ep9OXTtrW2MqVw+NqNVf9fLUiPN1D9/wjWYdXWjN+06Fl/VKmU5xvgYmRAABBBCIPwFC0/Fnz8wIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIJAABExV4Y/nnHRYybaPKitrWtcQq/P1e6RCAxwrHb9cK4c+aV3Q6U6chabnvltWFfOlCXXnkQlNm0H6TD+qv7ZcibBkngzu6lkvl1WJOrQK2Z4+/iocrKLzin7PyDV5UtUatd2ay4Sk5/Uu5xCiPjyymqZvvaL3/jpqW48JbZ/6wjEUtfnEHbUJFn6O8AaCdfy4VQG9USf0KtDOQtOZUrnIhMBpCCAQ+wJ+j/zVd+UwrTm3yZrsgyq91KnE8yEmnnJglkZv/Unp3NNaVaazeoT+5ZLYXzUzIIBAZAW2ee3Vaa9zmrVtgQ5dPBbq5eWyltLI2gOUI1XWyE4RY/1Hb/lRUw7OtsbrWb6bepZ7McbGZiAEEEAAgYQtQGg6Yd8fVocAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAArEs0HrMXm05eSfKs2RP56YdQyo7vd5ZaHreu2VVIQZD02bif3dd0yfzT+nCLZ8I7yNnejf99UZpFczivPJ0nn7r5ffosW28aW+Ukq//Y3X/7YB1rH3FLPquc1GV/Gizbnk+tI6Z6s1jVpzT6MVnbNcVz+6hFf2fcVjXkv031GP8wQivNayOn7YpqB41c4TaxVlo2tmaYmQxDIIAAiEE+q4crhVn1lnH363wil4u84JTpS7zeunA9aPqVqq9+lR6HUkEEEiEAsd9z+iQzzGrsvTKg+t1+8FdmYrTppXOWlydiz4fL9WlnVH+sGO8ft87zTpFcDoRPmwsGQEEEIiiAKHpKMJxGQIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIJH4BH79Hyv/+hmhvZPuQysqRLiAUZN/iKjQdOOeO03c1d/d1mVDy2Zve4e7LBKc3Dqokl2RJQvStMHyrLt0OCmF/26mIbno+1PC5p6y+g5vnV6/6udTi+z0y85q2dkAF/bz6gqZsvmwbr3nZTPqle3GH8Y9deaA6n+8Id30R6WDW1aFS6NUqnYWmaxVJp+k9S0dkePoggEA0BD5Y/amWnFptjfB6ua56q3x3p6MtO71W/VeNULKkyawq04XS54vGrFyKAALxKXDh4WVd9rumW/53lCxJMmVLnlk5XLIqbdLU8bksp3P/tmeqxuycYJ1rXaSJPq7RJ8GtkQUhgAACCMSsAKHpmPVkNAQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQSEQCyw/eVLf/KidHZ9mhVTuO69C0/R5uP/DT+mO3terwLa0+csshAG3fb2irAnq9Ts4Q22/1wx5tOxUQhjZtULN8OnPD2xaInvByCT1XKqP6Tj+maVsCQtITXympiRsvauWhW7br3muUR/0b53UY31SwNpWs7Zup/tyhcujh59Duj1lDvozuod4+Z6HpJqUz6vceJaJzy7kWAQTCEfhw7eeaf2K51at7qQ56r9JroV7x3oqPtersRkKLPFUIIBDnApMPzNSXW8dZ81bPWVE/NvosztfAhAgggAACcSdAaDrurJkJAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQSmECf6Uf115Yr0V5VjULp9PdbISsXx2doOvim9py7rz5/HdWhS54Op1qWy6xx3YqFMHh7yhHN2nHVdvzlWjl04IKntpy8Yx1bP7CiCmROoV/WXNDQf09axz5qkV//bL/qMMfYrkXV+pksIcYvM2Szrt9/aDuePZ2bdgypHO17EXwAQtMxTsqACIQrMHT9V5pzbLHV74XirTSg6tuhXrP36kF1W9DbOj+h6Tcqn7VUuOPTAQEEEIhJgZlHFmjExm+tIQumz6eZz/8ak8MzFgIIIIBAAhIgNJ2AbgZLQQABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBuBUo+dFm3fIMCu4WypJSJuQbXhsx75RVxTmwJU+aRCc+ryGXZEkcLk1IoWmzsI3H76jdj3sd1mgqPK/o/0yILX++8LS+W37OdtyEq01g+spdX+vY2S9ryuzbVJXu+ut+61inKtm0eP8NB9NF75VX2dypQozf/sd92nA8yNB0+KpjYWuMmGyEpmNSk7EQCF/gk43f6p8jC6yOrYs00cc1+oR50Webf9D0Q3PVMF8tfVlvSPgT0AMBBBCIBYEFJ1Zo8NpR1sipXT20rsucWJiFIRFAAAEE4luA0HR83wHmRwABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBeBE4cdVLtUZtd5i7d8Pc+qBpvnDXY6pTmyrV9m3aG6VUp2h6h2MJLTQ9d/c19Zx02GGNtYqk0/SeIatkT918Wf1mHLP1rZAvjXacvmu9z5TKRXuHV7Ven7vprSqfbLNeV8qfRttOBfQJbEc/q65UbslCmE7bcll9pweNH9jBVL02Ae2YaoSmY0qScRAIX2DU5jH669C/VsemBRtoZO0BYV502fOq2s95Q/d87+v7hiNUO3fA5woNAQQQiA+BFWfWq+/KYbapl3acpiwpM8XHUpgTAQQQQCCWBAhNxxIswyKAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQMIW+HHleX0y/5TDIue9W1YmHBxeM9WWyw/d4tDtxWrZ9Xn7Qg7HYjs0PW/PdXk/fKQCmVMofyZ3ZfBwCbH0x4+lw5c9tXDvDX215EyI8/0b59V7jfKEOL7u6G11HLfPdtwteVL5+D2y3lcpkFaz3y5jvX70WMrVd5312r5P4PtTX9QIlbPx17u09/z9EOefyZtafZ/LKxPoNtWs7ZsJQZ+54a2Lt31Us3C68G6VCE2HS0QHBGJE4Mut4zT5wExrrAZ5a+qr+h+HO+7E/TP0zbZfVTJTUU1pMSbc/nRAAAEEYltg44XtemvpQNs0fzb/QaUzF4vtaRkfAQQQQCCOBAhNxxE00yCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQMISaP7dbu08c8+2KBPOPT26poJldENddIXhW3Xpto/tvH315cCDsR2arjRiqy7cClqDmdcEl1O6JVOyJNI9b39b0Dm0jfz9VmnVKBQyfHzympdqfuZYiTtwjOABcRMgN0Hy4K1otpRa9X6FUA1P3/BW9U8DqlSH1jzckilrGlf5+T/WPR9/3fJ8aOt69suaIULV7049okX7bjgM5+njH2J4M659q5w/jaa8XiphPaSsBoFEIvDt9t/0x77p1mpr5qqsMc9+GqGVd5n3tg5cP6KBVd9Rx+ItI3QNnRBAAIHYFth5ZZ9eXtjHNs1X9YeoQd5asT0t4yOAAAIIxIEAoek4QGYKBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBIWAIPfP1VaMBGh0WZqsbTe5aO8ELf//uYJm+67NB/84eVlCeDu+1YbIem8/RbLz9T6jmKrU2FLBrTpajTq01V6fzvb3B6bvjzBfRq7Zy2cx1+2qf1x26H6NukdEb93qNEmKubsP6iBs86EaUdbBxUUfkypXC4tuuv+7Xy0K1Ij1cqZyot7Vs+0tdxAQJPu8DYnRP0656pFkOl7OX0a+PRESJZd36L3ln2ofKmyaXprX6Se/Kgz84IDUAnBBBAIBYFDt04pk5z37LN8H6Vt9S5ROtYnJGhEUAAAQTiQoDQdFwoMwcCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCQogcX7bujlCQcd1hQ8CBzegpcduKnuvx9w6Da0VQG9XicoTByboem7Xn4qNnhTeMsM9Xyjkhk1/uUSYVbWNqFpE54O3qa9UUp1iqa3Hf5w9gmNX3cxRL93G+bWgKb5wl3jsSsP1HvaUe0+G1T5O9yLJE16taQalsjg0JXQdETk6INAzAj8vPtP/bRrkjVY2SwlNLHZdxEeeMi60Zp7fKnerfCKXi7zQoSvoyMCCCAQVwKn7pxTx397ytc/4LdpdC/VXu9Vej2upmceBBBAAIFYECA0HQuoDIkAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAglb4JN5p/TjqvMOi3RWtTisXXj6+KvwQMdq1c3LZtIv3YvbLnMWml79QQUVyZoy1KHfnnJEs3ZcdTh/ZnRNuSRL4nDs7E1v1Rm1w2moObTB07gn1wtVsqpTlWwqmi30NQReX/WTbTLzBG/bh1RWjnRutsOTNl7SgH+Oh+j3Xeeial8xS4Qfhjk7r2nMynM6cdUrQvv6oUtRta3gOH5UQ9PVCqbVzF5lIrxWOiLwtAv8vneaftgx3mIonrGwprX8MVIkbWe/avX/ufEXypTC8csPkRqIzggggEAsClz2vGoFp+/4BHyxq3GBehpVZ1AszsjQCCCAAAKxKUBoOjZ1GRsBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCIZQET3j5+1UuX7/jIy/eRfP0fWYFjE5A2QetMqV2VKZWLMqZyUdoUyWN5NTE3fOC+Tl/3kq//Yz1+/FgpXJMpcyoX5c+cQlnTuMbcZIyEAAKREpi4/299s+0X65qC6fJqZuvfInU9nRFAAIHEJHDb564VnL7iec1adoVsZfR7k68S0xZYKwIIIIDAfwKEpnkUEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBCIkMOXgLI3e8pPVN3fqHJrXbmKErqMTAgggkJgFfPx9reD06TvnrG0Uz1hI01oGfBbSEEAAAQQSjwCh6cRzr1gpAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCAQbwLTD8/VZ5t+sObP6pFJSzpMi7e1MDECCCAQHwIvzH1Th28ct6bOkyan5rb9Iz6WwZwIIIAAAlEUIDQdRTguQwABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEnhaBmUcWasTGb6ztpndPq1Wd/nlats4+EUAAAQeBHgvf064r+61jmVJk0PIXpiOEAAIIIJBIBAhNJ5IbxTIRQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAIH4EPj32BJ9vP5La2oPlxTa0HVufCyDORFAAIEEI/Dm0oHadGG7tZ6ULim0kc/FBHNvWAgCCCAQlgChaZ4PBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAacCC06s0OC1o6xzLkmTa1v3RUghgAACCEjqs3KoVp7ZYLPY1n2hXJK6YIMAAgggkIAFCE0n4JvD0hBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgfgSWHJqjT5Y/Ylt+t09lsXXUpgXAQQQSJACg9aO0sITK2xrW9N5ptK6pUmQa2VRCCCAAAISoWmeAgQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEHgZVn1qvvymF6/N/RzS/Ol3tyN5QQQAABBIIJjNj4jWYeWWg7uqj9ZGVPlRUnBBBAAIEEKEBoOgHeFJaEAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgjEl8Dac5utwPTDR37WEla8MEMZU6SPr+UwLwIIIJDgBUZv/UlTDsyyrfOf539VofT5Evy6WSACCCDwtAkQmn7a7jj7RQABBNAo0EsAACAASURBVBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEQhHYeGG7FZj28vO2esxt+4fypMmJFwIIIIBAOAJjdk7Qb3um2npNav69ymQujhsCCCCAQAISIDSdgG4GS0EAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBOJLYOulXeq7crju+d63ljCt5Y8qnrFwfC2HeRFAAIFEJ/D73mn6Ycd427rHPPupauaqnOj2wYIRQACBJ1WA0PSTemfZFwIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgggEEGBnVf2WRWmb3nfsa74rcmXqpitbASvphsCCCCAQKDA1IOz9cWWH20gH1X/n9oWbQYQAggggEACECA0nQBuAktAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQTiS2DvtUNWYPragxvWEr5rOFx1cleLr+UwLwIIIJDoBWYfXaRhG7627eONcl31ZvnuUdrXwRvHVIKq/1Gy4yIEEEAguAChaZ4JBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDgKRU4eP2o+q4arkv3r1gCI+sMVNMC9Z9SDbaNAAIIxJzA4pOrNGDNSNuAzxdurKE1+0ZqgnG7Jmnc7j/Vs/yL6lmuW6SupTMCCCCAQEgBQtM8FQgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAwFMocPTmSfVbNVxn716wdj+4Wm+1L9b8KZRgywgggEDsCKw5t0m9lw+xDV49Z0X92OizCE829/hSDVk3WrnT5NDfrX6Re3K3CF9LRwQQQACBkAKEpnkqEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBp0zg5O2z6rdqmMzfpv2v4mt6qXSHp0yB7SKAAAKxL7D10m69vri/baKC6fJpZutfIzxx07+76uL9K/qo+ntqW7RphK+jIwIIIIBASAFC0zwVCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIDAUyRgKkubCtOm0rRpr5ftoreeeekpEmCrCCCAQNwK7Lt2WC/Of8c2aWpXD63rMidCi5h2cI4+3zJWFbKV0e9NvorQNXRCAAEEEHAuQGiaJwMBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEHhKBEy1UhOYPnj9qLXjriXbql/lnk/J7tkmAgggEH8Cx2+dVrs5rzksYFnHv5Q5ZcYwF3XF85qem9HZ6vN9wxGqnbtq/G2CmRFAAIFELkBoOpHfQJaPAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAghERODqgxvqt2qY9l49ZHVvW7SpPqr+XkQupQ8CCCCAQAwIXLh/Wa1m9pDfIz/baJNbjFGpTEXDHP3DtZ9r/onlqp+3hr6uPzQGVsIQCCCAwNMpQGj66bzv7BoBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEniKBm9631W/lcO28ss/adZMC9fVZnYFPkQBbRQABBBKGwC3v22ox8yXd9/W0LWjMs5+qZq7KoS5w08UdenPJAOv8L42/UOXs5RPGZlgFAgggkMgECE0nshvGchFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgcgI3PO9r74rh2vrpV3WZXXzVNO3DYZHZgj6IoAAAgjEoIC3n7ea/9Nd171u2kYdVWeQGheoF+osJjRtwtNNCzbQyNoBAWoaAggggEDkBAhNR86L3ggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQKIR8PLztipMb7iwzVqzqU5qqpTSEEAAAQTiX6D5P910/t4l20IGV+ut9sWaO13Y/BPL9eHaz61zk5p/rzKZi8f/BlgBAgggkMgECE0nshvGchFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgYgIPHz00ApMrzm32epeOnMx/dn8h4hcSh8EEEAAgTgSaDfndR2/dco227sVXtHLZV5wOnu3Bb219+pBtSnSVENqvBdHK2QaBBBA4MkRIDT95NxLdoIAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCFgCjx8/Vr9Vw7XizHrrfcH0+TTz+V/RQQABBBBIgAJd57+j/dcO21ZmQtMmPB28LTu9Vv1XjbAOj2/6tZ7JWjoB7oYlIYAAAglXgNB0wr03rAwBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEIiSwAerP9WSU6uta3OkyqqF7SdHaRwuQgABBBCIG4HXFvXTtst7bJO1L9Zcg6v1DjG5CU2b8HTDfLX0Zb0hcbM4ZkEAAQSeEAFC00/IjWQbCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIBB7AnOOLdbs44t14uZpdS7RWi+WbKvUrqlib8JojDx47SgtOLHCGiG9e1qt6vRPNEbjUgQQQACBuBJ4Z9lgrTu/1TZd4wL1NKrOIIfp9149qG4LAsLUvzX5UhWzlY2r5TEPAgggkOgFCE0n+lvIBhBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAgtgSWXVirURvG6IbnLYcpUrqm0Gf1BqpOjmqxNXWUxv14/Zf699gS61q3ZK7a0m1BlMbhIgQQQACB+BEIrCQdOHvNXJU15tlPHRbz5dZxmnxgphWYNsFpGgIIIIBAxAQITUfMiV4IIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg8JQJ/Hxosn7aPDHMXb9UoYP+V+a1BCEzYuO3mnkkKCS9u8eyBLEuFoEAAgggIJ24fUYF0+WNEMVH677QvONBn+Hls5bShKbf2K694nlNXee/o2sPbmjcc6NUNUeFCI1LJwQQQOBpFyA0/bQ/AewfAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBEII9Fo9SBtObbMdz5c5t/JnziPvhz7adXqf9Xdgq1ugur6tMyxeFT/b/IOmH5prW8PmF+fLPblbvK6JyRFAAAEEAgTG7fpT43ZPUu3cVdWuaDPr7/Dap5u+09+H59u6lchURFNbjLW9/2PfDH27/VcVzVBQ01uNC284ziOAAAIISCI0zWOAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIPIUCv669oMX7b+ilGjnUomwmpwLv/31MD3wf6Zm8qa0/5XKnfgql2PLTKGAfmHZ3cVObSs1UPEdhG4UJTC/cvVy7zuz/P3v3AdbU1YcB/BWZslFBUcEFKu4tbqt1V+uuo8vaYbWtddUuta4OtXZq+2mddVZt3XsrbnErLoYCgguQPb/nXEzIzYAAARJ4T58+SnLuGb97E1Df+4/yseZVGuHH9tNgb2lX6GRzzyzC6mublfMeHPoPXKydCn0dnJACFKAABbQLnHt4CZMOzcKzxCipg77h6fln/sSqaxuVg4pK1Zv6LVF+/dqW0bj59A6+aTsRfb26kZ8CFKAABXIQYGialwgFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKECBEiSw88oT/O9IKM7ci5Z2XdHJCuFRWRVzs6NwtrVAYw976f8RvhXg5mBZguS41ZIg8Dw5FlP85igrTIugtAhMi+C0thb4KARr/DYrq05Xc66ClT1/KdTg9IKzi7Hi6gbl8rYPXInK9hVLwuniHilAAQqYlEB0UgxWXduEJZfWKNfdrkpL9KrRBd2rddS5l98vLMfiS6uVz1eyr4gdA1dKX2++tRMzTiyAs7Ujtg1YATtLW5My4WIpQAEKFLYAQ9OFLc75KEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABChSiQHI6EBkP/LgnBDfCY3Hp3hODzC4C0yN8KzI8bRBNDmIMAiIw/cbOTxD4LERaTiefNnjJp22OSxNVp1f7bULQo/tS3xouVbG8x4JCCU7/en4p/rq8VrnGDX3/hLdL9RzXzA4UoAAFKFB0AmGxEVh2ZR223dmPxNREaSF1ynqhV43OUoBaBKDVm3ivF+/5ilbOxgX7X1svffnOrgk4//Ay3m04HGOavFV0G+PMFKAABUxAgKFpEzhJXCIFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKECB3AosPvkYfneiEfgoHpFRCYiK1V1NupyjDco5WEP8WsbKQprK0sIMFVxsERL5HDFxSXj2PAl3wqKQlp4hWwrD07k9M+xvjAIiwPbJga9x+2mgVFVaVJcWVaZz0w5eP45D109Ih4jg8l895hdocHqR/0r8eXGVconLe/2ERq51c7Nk9qUABShAgSIUCIkJxfa7+7Htzj6Ex0ZIKylXxkUKTosAtbez/CaY1dc3Y+7pRcoV21va4tjw/7A/6BgmHpohPb6mz0L4lM3d968iJODUFKAABQpdgKHpQifnhBSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUKRuDWw3isOvMIu688RuiTeI1J3JzLwNPNAe5lbaWAtCIonZvVBEc8R+DDaNwLj0ZgeAwSklOlw8vZW2JIy4oY/3Jl2FiY5WZI9qVAkQoEPL2LUbsmQlSadirjgGGtB6Cik2ue1nQj7DY2n90BUX26lksNzGg3SfrV0G3xpTX4/cIy5bBLesxDswoNDT0Nx6MABShAgUIQeJ4ch+139kkB6muPA5Qzvly1PbpW6wDxq6JtCtiJmX4LlF9blLbA2Td24tMD03AoxA/dqnXE9x2/LIRVcwoKUIACpinA0LRpnjeumgIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoIBS4GlcCr7f8wCrjj/QqtKidgV0bFgZrk42BlV7Hp+Ms7cicC4gEpFRmSHtKuXtMLCZGz5o5wZ769IGnY+DUcDQAqqB6QqOrnin4zCp0nR+WnhUpBScfhgdKQ3zQeM38EGj1/MzpOzY5VfW46dzS5SPMTBtMFoORAEKUKDIBQ4Gn8CuewexL+ioci01nDzxcrUOeMmjjfRJBjvuHsCXR7+TrfW3l2dh7L6vpMd+6TIT7au0KvK9cAEUoAAFjFGAoWljPCtcEwUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAE9BdacjsCCffcR+jRB44jqFR2lsLSPp4ueo+W92+3QKOw4HYgHj2KlQdydrTG0hRsmdPPI+6A8kgIFKHAo5ASmHpsnVZg2VGBasVxRafqvw2uUwWlRBVpUnXa3c8vXjv6+tgnzzvyhHIOB6Xxx8mAKUIACRitw8+kd7L53CLvuHUJE3CPlOkUY+iXPNjA3M8csv5+QmJqkfE5Umd4TeFgKTIvgNBsFKEABCmgKMDTNq4ICFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKCACQqcDYzBT/sf4NCNJxqrd7S1lMLS7epXKvSdnb75EFv97iEpJU2a27emIyZ080TrGo6FvhZOSAFdAltv78XU43Olp0Vl6TFd3oaTrWGvUfXgtL2lHSa1GI0+Xl3zdGLW3diC7079pjyWgek8MfIgClCAAiYlIG7sEcHpHXf341LkdeXay9m4wKecFy5EXEVscpzGnv7o9j1auTcxqb1ysRSgAAUKQ4Ch6cJQ5hwUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFDCgwa3sgFh58oHXENnXdpcC0s72VAWfM3VDRcUnYf+E+Tl4PVx4ogtOsOp07R/YuGAHVwLSY4cMub6Oik2uBTCaC07/vW4qo+Bjl+DPaTsp1cHpjwHbM8vtZOYaoICoqibJRgAIUoEDJEQiPjcCWO3uwKWAnHsVn3TRnYWaBlPQUGQSrTZec64I7pQAFcifA0HTuvNibAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgQJEKjPn7Jv69kPUx7YrF1HR3QqfGlVGrsnORrk918sv3HmPDkdtITE6VHmbVaaM5NSV2IeqB6f7NeqJx1foF6hEeFYmlR9ZABKgVbXjd/lLVaX3af7d3Y/rx+cqu37SdiL5e3fQ5lH0oQAEKUKAYCjxLjMb+oKPYH3wMp8P8de5wXZ9FqF22ZjEU4JYoQAEK5F2Aoem82/FIClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAoUqMGH9Law9HSGb087GAp0aVUGHBpUKdS36TnY3LBobjtzCk5hE5SGi4rSoPM1GgcIUKIrAtGJ/2oLTfby6QlSdzq5tv7sfXx39XtllXLN38Vb9wYXJxrkoQAEKUMCIBa4/uY099w5h570DeBT/VLbSGk5VsanfYiNePZdGAQpQoPAFGJoufHPOSAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUCDXAl9uvotlx8NkxzXzdkXnJh4o72iT6/EK+4Bf/r2IkMjnymnnDvbC8FYVCnsZnK+EChRlYFpBri043cmjDWa0mwh7SzuNM7P73iFMOTJH+fjklh9imE+/EnoGuW0KUIACFMhJYGPAdvxyfilikrJ+3rr49r6cDuPzFKAABUqUAEPTJep0c7MUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoIApCszcFohFhx4ol25lURo9W1ZFm7ruJrWddYdv4VxAVqXsVe/WRec6Lia1By7W9ATUA9O+Xs3Qs2HnItmIvhWnw2Ij0POfEco1ssJ0kZwuTkoBClDA5ASeJkZh4sEZuBBxBTbm1vj7ld9Qw4mf7mFyJ5ILpgAFCkyAoekCo+XAFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABfIvMHd3MBbsDVEO5FXJCT1bVEUVV/v8D14EI+w8E4SD/velmd0cLLH6vXrwcbctgpVwypIgoB6YbuxZD/2b9yrSrYvg9ML9y2Rr6OPVFTPaTlI+JkLTo3ZNQN1ytTCkTh80q9CwSNfMySlAAQpQwHQE4lMS8OXR73AoxE/6/rGkxzzTWTxXSgEKUKCABRiaLmBgDk8BClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClAgrwI7Lz/GqOU3lIc3r+WGIR298zqc0Rz374m7OHE1TFpPYw97iIrTLrYWRrM+LqR4CBhjYFoh6x90BZvP7ZRBqweni8dZ4C4oQAEKUKAoBNIz0rHl9h40casPT8fKRbEEzkkBClDAKAUYmjbK08JFUYACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACJV3gSWwKBiy8jFsP4yWKjg0ro3erasWGZd2hWzh3K0LaT68G5bD4rTrFZm/cSNELGHNgWqGjLTg9qcVoDK/bv+gBuQIKUIACFKAABShAAQoUQwGGpovhSeWWKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEAB0xeYsvEOVvqFSxtpVssNrxWDCtPqZ2XZnuu4FvREenj/xCbwcbc1/RPHHRS5gHpg2terGXo27Fzk69K2AG3B6RltJ0FUnWajAAUoQAEKUIACFKAABQwrwNC0YT05GgUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQIF8C2w+H4mxqwOkcZztrTH6lfpwsbfO97jGNkBcYgqW7rqG4MjnGNzcDT8N9Ta2JXI9JiagHpju36wnGletb9S7YHDaqE8PF0cBClCAAhSgAAUoUIwEGJouRieTW6EABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABUxfIPRZEgYuvIzgJ4nSZoZ09EbzWm6mvzEdOwiOiMGv/12Snv15qDcGNS++ey22J9FINnbu4SWM2jVRuRpTCEwrFqsenLa3tMOSHvNQy6WGkehyGRSgAAUoQAEKUIACFDB9AYamTf8ccgcUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoEAxEvh80x2sOBEu7ahZLTe81rH4V19ete8GLt17jDoVbbH144awtSpdjM4ot1IYAgFP70qB6efJsdJ0phSYVvioB6fd7dywvu8fEAFqNgpQgAIUoAAFKEABClAg/wIMTeffkCNQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUMIhAREwyOv1wHlHxqXC2t8boV+rDxd7aIGMb8yB3w6KxaNtlaYkTunlgQjdPY14u12ZkAsUhMK0gVQ9ON6vQUKo4zUYBClCAAhSgAAUoQAEK5F+Aoen8G3IEClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAgYR+N+RUEzfck8aa0hHbzSv5WaQcU1hkBV7b+BK4GOpyrSoNi2qTrNRICcB1cC0tYUVhrXuj2rlPXI6zKifVw9OD/fph0ktPzTqNXNxFKAABShAAQpQgAIUMAUBhqZN4SxxjRSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgQIkQ6L7gIi7ff46alZzwQe/6JWLPik1eD36KpbuvSV8Oau6Gn4d6l6j9c7O5F1APTI/sMAwVnVxzP5ARHqEenJ7RdhL6eHU1wpVySRSgAAUoQAEKUIACFDAdAYamTedccaUUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoEAxFth37Sne/CszNDywvRda1alQjHerubXYhBR8s+oUMjIyn1vzXj10rO1cogxyu9mpx+YiLDYC/b17oE3lFnC0ss/tECbbXzUw7VTGAcNaDyg2gWnFSVENTttb2mFJj3mo5VLDZM+ZPgtPz8jA+3sm6+z6S5eZsDG31vn8tjt7sfXOPq3Pd63aAYNq99ZnGUbTJyE1EZ8dno0ToWfRulIzfN/hS5SxsDGa9RXkQn49vxR/X9sET8fKmN1+CrycqxXkdBybAhSgAAUoQIESIsDQdAk50dwmBShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShg3AKjVwVgi38knO2s8OXwFsa92AJa3YJN/gh9HCuN/mlXD0zq7llAMxWPYRste1m5ERGYFsHptpWaF/sAtWpguoKjK97pOAzWFlbF46Sq7eJG2G1sPrsDiSlJcLdzw/q+f0AEqAu6+UdcxV+X10rT9PXqhpertteYMjktBeMPTpcer2Driq9af5LvZaWlp6Hpiu46xzk2/N9s9z/vzB9S0FZb6+/dE1PbfJrvNRbmAOtubMF3p35TTjml1Vi8VqdvYS4hV3OlpKfiYVykdIyFmbl0XeSlBUbfR7/NI5WHtq/SCiIwX1xbTHIsToddgHjdXYy4iqCYB3C2doSHQyUMqd0HHT1aF9etc18UoAAFKECBQhdgaLrQyTkhBShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAbnAnch4dPrhAtLSM9CliQe6Ny+ZYeENh2/hTECEhNPWywkbRtfnpZKNwK2n97A78BAOBB1HcMwDZc/iHKAuSYFpxQkNj4rE0iNrpOC0qDQ9o92kAq84ve3OPnx97AdpCYNr98EXvh9pXImiCrLvqlekx12snXBw6D/5fr2KStM9/hmOuJR45VixyXHK3+cUml574z/8fmG51mNNMTS99PI6/HL+L+V+Pmo6Eu80GJpv54Ia4PrjWxi2bYw0vKOVA44M0x5gz2n+G09uY+jWD5XdmlZogL96zM/pMJN8/lLkNYze+zniUxJ0rl/sf3H3eTArVcok98hFU4ACFKAABYxJgKFpYzobXAsFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKFAiBebuDsaCvSHS3ue9365EGohN+10Lx+bjd5T7vzKjFcraWZRYD303np6RjgPBx6X/DwYfh6gArGiqAWpRrdXO0lbfYY2u39bbezH1+FxpXcW9wrQ6flRcNNac3AwRoBaVpie1GI0+Xl0L7BwVVWha24ZUK6rnFJpWP/6380ux5EXFbFMMTUcnPccb2z+WborwdKiMlb1/lsLIxtoMFZoW+5t8eBb2Bh6R3rN+7jwTTSsUv5toNtzcijknf9XrdBp7YF6vTbATBShAAQpQwAgEGJo2gpPAJVCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAiVb4K1lN7H3yiO0q18JfVtXL7EYoY9jsWCTv3L/S0f6oHu9siXWIy8bf/A8XApOiwD1pcjrsiHK2jijo4cvOlRpjfZVWuZl+CI7Zu6ZRVh9bbM0v1MZB4x5eSSsLayKbD1FMbGoNL3syFqERWVWYx9et78Uni6IxtB0QajmfcznybFSWN7YmyFD02KvouK4jblNsa2w/PmRb7Hr3kHptFqVtsT45u+jbeUWcLd3w7VHAfh4/9d4mhglPe9T1gtr+iw09kuA66MABShAAQoYvQBD00Z/irhAClCAAhSgAAUoQAEKUIACFKAABShgHALHb0fBP+Q5XO0t0b1+WTjamBvHwrgKClDA5AUinyej0bTTsn283dYds/vXMPm9cQMUoAAFKEABfQXafu+PexGxmDS4Kdycy+h7WLHs9/lfJ5CSmi7tjT8T5O8Unw2/qKxACnQklQAAIABJREFU/Sj+iWywak4e6FjFFx08fNHItW7+Jirgo0V1aVFlWjQRlB7ZYRgqOrkW8KzGObwITi8/sh6hUeHSAkW16RltJxl8sYYITcckxyI2OQ6lS5mhtFlp2FqUgY25da7XauqVpkXl96CY+0hISUDtsl5SOLYw2+P4pwiOCUV1Jw84WztmO7Xom5KeIp0vczNzKahtYab/3/8YOjSdV6fI+McIiQlFFXt3uNmWz9Mw96KCEZ+SgOpOnihjYaP3GCLgHpUYg2eJUUhMS4ao9i+qg4tf1a//9IwMjDswFXejgvBXj/moYCt/X1t+ZT1+OrdEmluswW/EVr3XwY4UoAAFKEABCmgXYGiaVwYFKEABClCAAhSgAAUoQAEKUIACFKBAjgIfrLyJrRcfKfvZWpXGvolNULVs7v+xM8fJ2IECJi6QkJyOo7ee4eCNZ3jwLBGPnqcgJiEVdtalUd7eEh4u1nipjjPaezvDxtLMxHdrmOVHxCSj8XR5aPqtNhUxZ0BNw0zAUShAAQpQgAJGLpCSloEaU/yQmpaO799ti9JmpYx8xQW7vEXbLuNuWLQ0SdVyNvD7olnBTlgCRhfBVVF5WlSgPnL/lMaORWhahKdFiFqEqY2p/eG/En9cXKVcUv9mPdG4an1jWmKRrGXV0X9wK/KeNHdBBKcNEZr+9tSvWH9DHvK0s7RFw/I+GOrzqlRRV59WmKHp+zFheGXTm9KyRND1yLBNGkvstmEYIuIy/45kac8f0cRN83rMQIZUGX3xpTWIToqRjVHJvgKaV2iEsU3fRjkbF9lz+4KOYtKhmTpZtg1YgSoO7lqf/+3CMiy5tEZ6bma7SRBh7UUXV0IEoRVNzL3gpW/g7aL9Ew3E3oWBahOhY7HH9xoO1/r+MP7gdBwMPqHPqVT2OTj0H7hYOym/FkHjdqv76RzjS99PMKh272znSElPxfenfse2O3uRlJas7CsC4H1rdsOUVmNhWdpCY4xhWz/E9Se3pcf3DVmHRf4rsTvwkBSYVrQ2lZpjdvvP4KQjdH7r6T2suf6vVDVadW71yS68tQdmpeR/DyCC0xkZ6VJQXb2pVtiv5lgF//ZfmitndqYABShAAQpQQFOAoWleFRSgAAUoQAEKUIACFKAABShAAQoYjYAIzW3xzwrmZrewCo6WqFG+DKqVt0YZS81/VDCaTRWDhfjdicbAhZc1dvKyjwtWjDLuamTFgJ9bMCGBp3EpmL09CGtPP9R71SPbuWNKz6qwsyrZ72MMTet9ybAjBShAAQoUUwG/wHgM/PW8VGFaVJou6W3xzqsIuP9MYjAvXQohc9uWdBKD7v/2s3s4EHQcJ0LP4sqjmxpjt6/SEh2qtEZHD1+UtXE26Ny5HUxUlxZVphXN16sZejbsnNthimV/UXF65ZF/cD8qVNqfoYPThghNf7T/Kxy7L785UvVkiND0ry/PQilkf6NIUYWmRUXo02/s0Lh+cgpNP0l4hlG7JiAw+n62154Yf+Ori2Uh6JxC0xv6/qkz8Kwamn6r/hCsurYRaelpGmsQ4dw9g9agXBl5YFt0bLmyV7ah30+bv4c36w2SjZmX0PShoRtlVa9zCk1rm1d1EVGJ0Ri+fSxCn+v+87io4ry2z0KNatuqoelPmo3Czy8qO6vDibD61gHLNa7Xm0/uSHNrs1YdI7eVokX16WFbxyjPR3/vnpja5tNi+X7GTZmGgKi+/uPZ/ykXO6BWT3TyaCNbvLhZQ7wniFbbpQbGNh1pGpvjKilAgRIlwNB0iTrd3CwFKEABClCAAhSgAAUoQAEKUMC4BU7fi0a/3zTDuTmt2sHaHJN7eOKttu4wloJs/iHP8dsB+T+Oje1cBY097HPajtE9v+jQA8zcFqixLjcHS/hPb2l06+WCKFAUAtsvPcaHq24iNT0j19NLldsnNJaqKJbUxtB0ST3z3DcFKEABCigE/j7zGJPX3UDdqmXxdjefEg+zfM91XA16IjnYW5sjYI5viTcpKAARmj7x4IzWALWoCNzZsy26V+sI30qFX+074OldDNnygXLrFRxdMebltwuKwiTHFcHpBTv/VFbEndRiNIbX7W+QvRgiNL3k8lpcfXRDCn0mpSYjLDYCD+MiZev7qOlIvNNgaLZrNrXQ9LgDU3E45KRyT6JitajUnJqRijNh/rJQsqjyvrzXT8q+4rr/+9pmmcfOeweUgVx9Q9MiGC1CvF7O1VDDqSqOh56BqDivaK/U7CpVo1ZvUw7PQXxqgrRG0T8i/pGsUrXov7L3L2hQvo7yUHGtXIq8Ln19PyYUp8P9pd+LNfTz6qH13I5v/h5EiFjRElOTMPvkL7K+J8POKefOKTQ9++TP+OfmduXxIuBcr1xtXH18U1Y5e0CtXvi69TjZPKqhaYVby4qNUcaiDI4+OCULQ3/b4Qv0qN5Jdnzb1a/KbMXcdcvWgoOVvVTZOiU9BVGJMVJl7c9ajdHr9ekfcRWjdk9Uzi0F3Qev0ahMrtdg7EQBAwmce3gJo3ZNlL3ORPV71ZaQmgjfVa9ID9UuWxPr+iwy0OwchgIUoIDhBBiaNpwlR6IABShAAQpQgAIUoAAFKEABClAgnwJ5DU0rpq3kbIXfhtdCy+qO+VxJ/g/fevERPlgpr9j1xxu10adR+fwPXsgj/HshEmP+DtCYtUFlO+we37iQV8PpKGB8AqtOhuOzf+7ka2HOthY481VziAB1SWwMTZfEs849U4ACFKCAqsC0bSFYfCgY7epXQt/W1Us8zt/7b+Li3cxPIXJ1sMRF3qyp9ZpISUtBcnoKNH9NzXpchFVf/J+s8nsRYBVfp2ekK8d+nPAUQdH3ERIThsj4x7I5RfhPBD8dLO1gY26NGk6eGNVwGMqXKVtg16sIZomAlqJ92OVtVHRyLbD5THXgG2G3scYvM2Rrb2mH9X3/gLudW763oxqaFuHWhq6aN3Skp6crA7IiEHpw6D85zhuXEo+fzi1WBlxFGPT8m7uzPc6UQtPqocK+Xt3wVetxsDAzl/aYgQxMPz4fW27vkUK0f3T/Ht7O2b/vd1gzANFJMdLx+oamRd+Pm76DkQ1eU8476dBM7A86Jn0tqi7vHrw6x/MlOojqzZMOzcD1J7el/k0rNMBfPeZrPfb641sYti0zGCzeM44M26TXHNo6TT48C3sDj0hPZReaFu9XXddnBe+F+fS2E6SK0ML7m+M/4r/bWdeYCB+72Wb9/ZxqaFrMtbLXz2jw4noX7589NgzH08QoaR3qoWvxvtll3RDl8hd0nq5ReTe3AIdD/DDuwDTlYeI18luX2fCtxE+iyK0l+xtWQP39TYz+d+9fUa98beVEDE0b1pyjUYACBSPA0HTBuHJUClCAAhSgAAUoQAEKUIACFKAABfIgkN/QtJjS3KwUznzdAhUcLfOwAsMdUpxC03FJaWgx6yyexaXIgEw1BG64s8yRKAD43YnGwIXaK+SL9yMvtzJo5GGPuu62CHycgCMBUbgTGa9BV6+SHXaMawSL0tl/LHVxNWdourieWe6LAhSgAAX0FRi+5CYOXX+EV9vUQNt67voeVmz7rT98C2cDIqT9eZa1xskvmxt8r3ejgvE04ZkUGk7PyEC6+C8jAxnS1y8eE78iHRniedXHMl48Jh2j0ldx/IuxMp9TOR4ZSE7NDDErAsxZv6ZIj4uKpKnpqUgR/6eJX1W+lh7L+trgKLkccE2fhfAp65XLo/TrfijkBD49MF3ZuZNPG7zk01a/g0tgr1VH/8GtyHvSzof79MOklh/mW0E1NK3PYPqGphVjddswDBFxmTdHiPCuCPHqaqYUmp5z8ldsuLlV2kq5Mi7YO3gtzEqZaWzt31u70K1aR1m1ZV37z0toWgSWDw/bKAWHFe102AW8v+cz6Ut9wuqq63mWGI1OawdKD2UXhi6K0PSOuwfw5dHvlPs6NuxfmWt8SgLaremnrNo8s91kvFLzZeX2VEPTXat1wA8dv5KdigVn/4cVVzNvCFAPjN+PCcMrm95U9v+m7USI0HZem1ircBbfD0QTFf9X9foF1Zw88jokj6OAwQS0habF+9j3Hb9UzsHQtMG4ORAFKFCAAgxNFyAuh6YABShAAQpQgAIUoAAFKEABClAgdwLaQtMVnaxgZS7/x6WnsSmISUzVOXjzag7Y8lHD3E1u4N7FKTQtaB5GJ2P6lns4HxyD8vaWGN2pMl5pWM7AahyOAqYlIG4oaPrNGa3vR741HLFspA8cbDKriam2e48S0HPBReVxomr7v2MbwsZS8x/STUsk76tlaDrvdjySAhSgAAWKh0CPny/jUnA0RvWsh9pVnIvHpvKxi03H7uDk9XBphNoVbXFwUpN8jKZ5qKiWKkJybHkXeK1OX0xpNTbvA+Rw5NzTC7H6+r9SL2sLK0zoOVr6lU27gN/tc9h16YD0ZC2XGlK16fw2Q4WmRfjzYuQ1qVrxw7hIKcLr4VAZ/7v4N4JjHkjL/L3rHLSppPvmCFMKTY/cOR4XIq5I+xrd+E2832hEfk8F8hKaFp7CVbU9T45Fu9X9lA9dfHuf1rWJqspXH91EWGwEHsc/gbWFDao5VsGEg98o+599c5eyerbqIEURml7kvwJ/XvxbWkbLio3xZ/cfNPYlwuIiNC7ae41G4MPGWUFn1dD0pBajMbxuf9nx+4KOQlTpFk3cKCJuGFE0cbNN85U9lIFs8bgIlber3AK+lZqhpXtjlLNx0fsaOBF6FmP2fiH1FxXeN/dbku0NBXoPzI4UMICAttC0GPbEiC2wtSgjzZCb0LR4/YjvA88So1DTuZr0aRZsFKAABQpDgKHpwlDmHBSgAAUoQAEKUIACFKAABShAAQroJaAtNL1/YhP4uNtqHJ+eAVx5EIuPVgdordq65K066Nmg6EK9xS00rdcJZCcKlDCBebuD8ePeEI1dj+/qgQndPFEqm6LRIiTc++eL0k0Imz5sUKID0wKQoekS9uLhdilAAQpQQEOg5y9XcDEoCu/1qgfvygxNb/G7h2NXQiWnxh720idyGLI9in+CJZfWYP2LarB5HduytCUsS1tA+tXMAlbKrzMfszG3lkJEolKo9OuLQJFe85UqBTNRI7ZU5v9m4ivxe5SC2YvHpEdfPCb1kX6f2U/2uOJY2ZjSiGpjmkFUOb36+CauPLophSZVm9hDRw9flLVxwfjm7+m1jbx2emfXBJx/mPmJLo0966F/8155HapEHOcfdAWbz+2U9urtUh0b+v6Z732rhqZ71+iCcc3f1RhThONe2ZgZPlWvNC2qrC/yX4mVV/9RVs3VtaifOn+Djh6tda7ZlELTL60dBBE6Fu3bDl+gR/VO+T4XeQlNv+rVHdPbTpDNnVNoWqx77ulF2HXvYI5rPvn6Nuk9Tr0VRWj68yPfKtesbd9ijdOPz8d/t3dLy+1erRO+65gZTBZNNTQtqkyLatOqLbvQtOh3IPg4Jh+eJQtOqx5fu2xNiDC2qFKdU/v72ibMO5N508OAWr3wdetxOR3C5ylQaAKqoWlxc0B0Uow094QW7+P1upmV6PUJTT+Of4rPjsxWfp9XbED8vPRJ01EYVLt3oe2JE1GAAiVTgKHpknneuWsKUIACFKAABShAAQpQgAIUoIBRCuQmNK3YgAhPd//RH1dDY2V7eq9DJUzvW73I9snQdJHRc2IKFIqAeO/x+fKkRpXp130r4vtBNfVaQ0pahhSsNjeTp6vjk9Ow9+pTnWN4lrOWwkOK5ncnGkcCnuFmeByiElLh7VZGutmkUx0XVC2r+Y/YqgN/tzMISSnpKG1WCualS0lrERX+q5azRrVyNqjoaJVt+FufjSanpks3uZwLfo5zgTEIj06Cm4Ml6le2Q/+mrvBwsc53aFpY3omIx5XQWFx+EIsbYXGwtSqNuu62qFvJDvUq2+Vooc9e2IcCFKAABShQUAJD/ryGYwFPMeylWmji5VpQ05jMuDtPB+HgxfvSelvXcMTGMTkHzfKyORH+yamZm5nDurQVrM0V/1srv87pWFN6/nSYP/YHH8X+oGN4lhgtW3r36p3wRr1BUoXVwmrjDkzD4RA/aboeDTujtVezwpraJOdZ47cZN8JuS2sf7tMPk1rmv5K6amh6cO0++ML3Iw0b1XCcemhaNciaE6qphaa7rB8CEfoTbWnPH9HErb5yi6rPTW0zHv29e+S0/Ryfz0toup93D0xrM142dnah6cTUJLy6eaRUDVyfVlxD03M7fY2Xq7aXEeQUmhadhe3yKxuw/e5+RMQ90krY37snprb5NFveZVfW4+dzS6Q+X/p+wvCoPhcj+xSagGpoWlzPuwMPSTdbudmWx57Ba6R15BSavhx5He/unpTtzTSdPNpgQefphbYvTkQBCpQ8AYamS945544pQAEKUIACFKAABShAAQpQgAJGK5CX0LTYzO2IeHT4/rxsX229nLBhdNY/WunatCGCdtdC46Q1qLYzgdFYfiLz46QV7Y3WFdGqumO2/o5lzNGptmZluX3XniIuKU3rsSIY+HLdrI/6DHqcgP/8HyHgYTwePEtCZWcr1Ktkh5bVHdCsqoPO+WOT0rD/mu6gpvqBbb2dUM7OIs/X0+PYFFwLjZXClCLkGB6dDC83G9SvZJcZcqxkizKWpbMd/1xQDB48TZL1aVXDERUcLfVal7jmwqOSlX1Lly6FXg3KQS3DqjGWOBc3wuOktYuQ5u2IBFRyEs6ZAU0RBs2PjV6LZ6ciFTh44xlGLL6qsYaL37SEq71+15+uDdx/moiWs87q3F/L6o74d2wDiNfsmFU3se+67tftnAE18VabilrHSkvPQJWJx3N0FAFscROKeF/NbTt1NxpD/7yKpNR0nYdO7O6J4a0qoPH007I+Yt1i/Tk18Tp+c8l1jQC7+nG9G5bD/CHesLfO/n0lp/n4PAUoQAEKUKAgBEYtv4mdlx+hj291tG9QqSCmMKkx954Pwd5zwdKaxSd4TOjmYVLrN5XFhsdFSiFp8f+lyGuyZddyqYHhdfujT82uRbKdrbf3YurxudLcdd1r4bXWrxbJOkxh0vCoSCzcv0y51J2D/oa7nVu+l56f0PT952HKCtRiIe81GoFXar6M8jZlYVbKDKLa+5Qjs6WK5qLlJjR9cOg/UlVrfdtv55diyeW1UnexhpntJmd76P2YMLyyKbN6tqgef/qNHRr9m67orqwqrB6aVq2SPsynHyYbIMBeGKHpTQE7MNPvJ+W+v2r9CXzdm8HR2gGp6akIff4Qw7eNUQYddYWmbzy5jaFbM0P7omrs8eH/6XuqNPqJ6s17A49Ij3/a/D28WW+Q1rEW+a/Anxf/lp5rWbEx/uz+g0a/93dPxulwf+lxcT1+2DjzHIumWmk6r6Fp1QlFoP5k2HmceHAW+4KPyipQb+63BNWdPHWaJKUlI+rFjSvO1k7SpxmwUcBYBFRD0+JmGhsLa6y4skFa3l895kvV1LMLTWcgA302vQXxPqtoDVx9UMG2PPxCzyE2OU75+PyXpqGzZ1tj2TrXQQEKFDMBhqaL2QnldihAAQpQgAIUoAAFKEABClCAAqYskNfQtNhz7S/kFV9FYPXyjFbZchgqaPfZP3ew6qQ8IJ3X81DJ2Qpnv26hcbj6/lQ7iMqwIfMy/xL5h13B+GlfiM7pX2vphu8G1ISluZlGH23h8+z2sf6D+mjnrf8/VCrGEuHJzzfdwbrTEdkyiTD4otdro4tPViBc/YBp/93D4qOZH9utaCPbuWNWvxo5noKMDMD7Cz+NMLrfF81QtZyNzuOXHQ/Dl5vv5jj+lJ5VMbZzlRwD2DkOxA5GKTBzWyAWHXogW9sI3wr4YVD+q+/lFJoWVZr9p7fEp+tuYf2Z7F9HYoHzh3hhaMsKGo7hUUloOuOM3r4NKtth7mAv6aYAfdpKv3BM2XhHn66Y3b+Gxusqp9C0CH1/uzMICw/Kz0N2EzpYm2PpSB+0rpn9DSx6LZqdKEABClCAAgYUGLf2FjacjUCnRlXQq2VVA45smkOtP3wLZwMyf87Z+GEDfu828Gk8GXpeqg65N/AoElITlKOXsbDBwFq98Ua9gShno/vPYQZejtbhRNXWwVs+QHhs5nUwqsMweJavUhhTm9wcS4+sReCjzL8H+KDR6/ig8RsG2UN+QtP/3d6N6cfnS+t4ybMNfnxJs2KoakXmnELTLVf2UoZ11/ZZiDq5qHq++tpmzD2zSFqLCPSJYF92TVQJ7rZhmLLL/tfWy14PYbER6PnPCOXz6qHp7079hnU3tkjPi9D1vtfWw8FSvz9D6VpXYYSmVQPKX7cehwG1esmW8yThGTqvG6x8TFdoWgSGxblVNBE6Fw55afqGpnfePYAvjn4nTVHarDSODfsX4v1M0UQl3HZr+inDyyI4LwL0imbo0LTqXsV7Wce1A3XOnRcXHkOBohJQD02PbPAaur94v2xfpRV+6TIz29D0nsDD+OzwbOXyv+3wOXpUf0n6Oi4lHiO2jUVgdOYnjahWry6q/XJeClCg+AowNF18zy13RgEKUIACFKAABShAAQpQgAIUMDmB/ISmu8y7gOthWdUoVIPE6hCGDtoVdWha7O/KjFY4fjsKo1dlVmnKrvVpVB5/vFFbo0thhKZvhMVhxJJrEGFNfdvAZq6YN9hLa9D7ZngcXpp7QTaUs60Frs3MPjAvDjgbGIO+v8o/jtvDxRqnvmqudWnP4lLwwaqbOHYrSt+lQ1ToXfFOXYgwPFvxEuj980VcCH4u21ROgXt9BXIKTYtxDk1uik4/yCvs6xq/opMVzk/VvBnjfFAMXvkl54+kVx3XytwMZ75ujvI5VNPefukx3ltxQ98tQ7z2Qp4myvrnFJrO63uv2MOlb1rCwcZc7/WxIwUoQAEKUKCgBcRNhStOhKN5LTcM6ehd0NMZ/fgLt17GvfBoaZ13vmud4yfQGP2GjGCBonrjrsBD2H3vEM4/vCxbUSeP1hhSpy9auTcxgpVmLUE1nFXN2QMjOw81qvUZw2L8g65g87md0lK8XapLgWD7fAZ0FfvKT2h6861dmHHiR2moCrau2D14tYxr862dmHFigfKxn7vMRIcquv8c333DcDyMi5T6i+qmX/h+pDf/geDjmHDwG6m/CNRuH7ACFbOpxC0qoTZellVhXbUqcXpGOiYcnIFDISeU86uHps8/vIJ3do1XPl+3XC0s6DwdrmXK6b1m9Y6FEZqedGgm9gUdlaYWgWkRnFa09IwMqTK4ouqzePzU69thba797zoaLcsKJIuK9ZNajM7T3vUNTUfGP0bX9VnvDyIQPaPdJJRCKYjzOfXYPGy7s1e5hr1D1srOR0GGpkXIXlQuT0vP/PS47zt+iW7VOur0uBh5DfPP/CH1/6T5u1LlbDYKGIuAemhavBerVtc/NHSj9L7gu+oVacm1y9bEuj6ZN62IJm5uEDc5aHtOPCZ+PhHjKdr+IetRrkzR3sRlLPZcBwUoYFgBhqYN68nRKEABClCAAhSgAAUoQAEKUIACFMiHQH5C042nn0ZETLJydkUlVm3LMXTQLq/jaVtbXipNi3G2fNQQw/53VaNqsq7TIarUCiPVVtChaXF+xHnKS/ukSxV81lN7xb1Ws85qhC33jG+cYzVcUS1aVI1WbZ/3qoqPOmtWLxNVqX1na86jz15Edd7d4/mPXPpYmVIfr881q5Tfn9cWpc1K5XsbqekZ+G5HEKITUqWxgp8kSjdFqLYxL1XG7y8qLIvXcqc6zqjnboeTd6Ox4/JjjTVsGtMAvjXk1ZWvhcZhzo5AJKakQ1SAT07NkH4fGZOMmMTMubW15tUcpPec7FrrOecQ9DiraqGi70t1nNG6RmaF+uvhcdjq/whiv9padqFpEbAWr331Jm6YaVndEVXKWkn/QH7p/nPZDTWK/vpWpM/3yeQAFKAABShAAT0FZmwNxB+HH6COhwve6VFXz6OKb7cvl/ohKSUNNdwdcWxig+K70ULY2c0nd6Sq0iIs/TDukXLGms5VparSr9XpWwiryPsUc08vxOrr/0oDtKveEl2b6A4b5n0W0zwyPCoSS4+sQWJKEuwsbaXAdC2XnD91Sd/d5ic0ff3xLQzbNkY5lW+lpvB1byaFlk+HXcDR+6ek3yuCpKKiaJ2yNaUwqaLqqOo6pxyeI13HiiYC4h4OlSBCzJcf3YBPWW+pwqm2JqqV91CpDC0qEDdxqw9bizK4HxOKgGf3sLHv/1DNyUN5+KD/3sPtZ4HKr0Vor5lbQ4hAqwhvi8rJSWmZfwdVxcEd9cvXweetxioD66oBZNFH7FWEp72cq8GsVCk8T4pDaGw4SpcqjeW9flLOIyoizz/7p8Y2ROVuhVVL9yaobF9R2adPzZfR0DXz+8ZvF5ZhyaU10u/7effAtDZZ4W3xmKh63G51P+WxF9/ep/z9X5fX4tfzS5Vf967RRRr3WWIU9gcfw62n92TnTIQhxTl4q95g+JST3+yjGqIUA4rgfJMK9eFoaY/o5Od48DwcYxq/CbEXRfvz4t8Q4WfVduT+SYjK1QrnFioB4gbl66CvVzdl9zknf8WGm1uVX4vzUq9cbVx9fBP3Y7L+7ke8733V+hPZPPkJTYtQ9OAt70vnRPzvYGkvXR8JqYm4Fx2Cy5HXZXPtGLgKlew1PwlK0UlUORfVzkVztHLA4WEbpT/bslHAGAS0haaP3T+Nj/Z/JS3vw8Zv4vV6A3WGpl/f/hGuPMos+PFp8/fwZr1BGttquqK78v1uWc8FaOxWzxi2zjVQgALFTICh6WJ2QrkdClCAAhSgAAUoQAEKUIACFKCAKQvkNTStLYzbxNMe2z9ppMFREEE7/5DnWHPqoWwuUY1YvWqqCBp6u5XJ9hSJ0PS4l7P+oU7RedXJcFy+Hyt9mZaRgXWnMz+iWNFUA5SiiqoIJorgoKjo/OeRUI05P+5SBVPUQsiRz5PxzZasfxRUPSjgYZxG8HD9B/XRzjtJajjlAAAgAElEQVQz/KhPG/N3AP69kFkZSrWJPYu1lrE0Q0R0Mo7eipICnKpNBCHPT2uhtbrtbwfuY86OIFn/d9tXwjevVs92WXW/PgVRPVq1aQuTi+fFR6WLj0xXb7ZWpdG6hiPcHC3xPDENp+5Gy8L7iv4rRtXFyz6sjKLPdWIqfdzHH5Mt1cHaHDfn+BbI8o8EPMPQP6/KxhbVo8XrWwSh14+uD/EaUbRP193C+jPy94jZ/Wvg7bbueq9P5JivPIjF4qOh2Hxe83W76PXa6Nu4vNbxtK1XdFz6tg+61y8rOyb0WRLe/Oua1mBzdqHpN5dcw77rmf94rmiisvvSkT5S1WrVtu3SY4xZdVMjnH3m6xaozCrwel8T7EgBClCAAgUrMHd3MBbsDYGNlTlmvlUwP1MU7A4MO/rEPzN/1urVvAoWD9V+86RhZyxeo6WkpeBgyAmpauz+oKyfW0WQTwQGRRXZ6ioBUWPf/bgD03A4xE9a5ogWA1HLw3DBYGPfe3brW7h/GURwWjRRybiTRxuDbic/oWlR3ffNHeM0AqOqC5zedgKmH58vW3PXah3wQ8fM8J1qC4wKQb9/39G5P0+HytgyYJnO5z/e/7UU1NbVROC6vUql6wsRVzBypzxwrDhWrFEEeUUf1SaqaYtwsGhRidF4d/ckWfBa19z+b+9VhmKfJDxD53WDc3UeP2/1EYbU6SMdk5/QtFhzz42vQwS3tTURNhcB6YX+K2RPi2C2CGirtvvPw/DKxjez3cfYJm9jVMNhyj5d1g9RBqT1ARDnSzUoH50UgxHbP5IFpNXHEWHl1b1/g5O1/Ibi/ISmxU0A7+/5TJ8lSxXSRaV0XU3cBNB8ZU9lYFT0O/n6NtiYy/+Mq9dk7ESBAhDQFpoW7/cd1wyEeA2KoP+uQX+j9d+Z17l6pemX1g7C08TMogC6qq6r3jgws90kvFIzq/J/AWyJQ1KAAiVUgKHpEnriuW0KUIACFKAABShAAQpQgAIUoIAxCuQ1ND3g98tSdVXVJsKBIiSo3goraLf14iN8sDKzcoai/fFGbfRppD1kmJvzkZaegSoTj8sOUQQonW0tcHBSE1kV6S3+jzB6lXwtIsArgrz6tpV+4Ziy8Y6se25C07ci4tHx+/Ma04lg86h2lVBKpWhOVHyqtF4RvFRtA5u54pdhtTTG0BaaL2dngcszdH+0742wOHSed0E2Vr1Kdtg7QbMidEpaBup/fUqj8u6gZq6YM6AmRHBa0cS5+WFXMH49cF/j/Jz9ugUMUIRY31PGfgUokJyajqqTsz6OWUyl6/oxxDJ0hZBrupbBgUlNYFFaXnXqelgcuqhd3+LGii97V8vTcv45F4lP1gTIjs0u0PzKL5dwPihG1n9a3+p4v0MlrfM/ep6Mpt+c0Qg165rj3qMEtP32nGws8d53YVoLiJtGtDVxw4a4cUO1ZbemPEHxIApQgAIUoEA+BA7eeIYRizNvknqrmw/qVZXfaJSPoU3u0Jj4ZMxYlfkJNV/098HYtiXXIrcnzz/iKg4Gn5AC06HPw5WHN3T1kSpKa6vgm9s5iqK/qI4rKteKSreifdjlbVR0ygynltS2+dxO+AdlhnZntJ2EPl6GD5XtCTyMzw7PluYQQU8R+FRvotpyy5W9pIdFteg9gzOrHIsWkxyLH8/8CVElWbWJassi5Nu2ckt035AVmhV9dIWmxXPHH5zB5MOztIZ665evjVW9f9V5OYgg8LTj86QbCdSbuJlAhPc6erSWPSVC1lOOzJHNJyojf9vhc8zy+0l6rak21dC0eFwECTfe3IFF/iuUIUH1uUUF6v1D1sP5RYg3L6HpqW3Go/+L0LJqaFoYi0C1asuu0rTodzcqCLP9fpEFwsUaG7nWlSrIijC0etBdW2hajBUUfR/zzvwhnTdtrb93T0xt86nyqdyGpl+u2h5zO30tGzolPRVzTy/Cltu7lZXARQdxjkXw8rNWY2BhZq6xHNXQ9E+dv9G4FsR1I6qHi+ZT1gtr+ixUjrHr3iF8fmSOzmtPPNHA1QdD9XwP/v3Cciy+tFoab2idV6U1s1HAWAS0habF2v64uAp/+K+Ulilel4rXC0PTxnLmuA4KUEBdgKFpXhMUoAAFKEABClCAAhSgAAUoQAEKGI1AbkLTIsh66f5zfPbPHdwIj9PYw9EpTSEChaqtMIN2hR2aFvsUlWZPftkconKzahNB3ppT/GTVm3Mb8MxvaHrksuvYfeWJbF0icCxCkdqaqHLbfOYZqZKuookwZOAP2itn9f75Ii4EP5cNtX9iE4jKs9ra7O2B+P3gA9lT3w2siTdaa65H29671i2L5e/46HztiICpCJqqNhFurVNR+3qM5kXIheglIEK+DadlBnkUrYuPC1ZquRFBvD/tV6uIrG2S5lUd0KqGvNqVop+u0PRPQ70xuLmbxnAJyemoMUX+D/j9m7rit+GaNx3otWERXpjvj6uhmdXuRdNVzV88V23yCdn7jXhvuj7bF3YqNxioz/vVv3ex9FjWRyaL53WFprXdCCLsxTnIrrWadVb2CQCv+1bE94Nq6kvAfhSgAAUoQIECFRCftCK+h4rWoHo5vPFynQKdz5gHv/UgCv/bkRkG3TqxJZq5Wxrzcot8bQmpCdgYsEMKcIrQtKK527mhQxVfdPJsgxYVNT+FqcgXnssFqAanbS1t8Gb710pscNrv9jnsunRAEvyg0ev4oPEbudQs3O7JaSkIjX0IcQ5rOlWFqFgsWnpGBiLiImFR2kIKtVpK/1soqy7rWmVk/GOExUZA3DrqYGmPSvYVpeP0aSLkLapWJ6Ylwbq0FcrZuKBcmez/HPEw7hHEnN7O1WFtnvn3LaIqsxhLsW7xqwgX62qp6akIiQlFVFKMtD8xjmuZcihr46zPsgu9T1xKPB48D0dGRgZEyF2xNxE+j0l+Lp0rxd61hZBVFyyqJws/UZ1bmImqya625ST7gmxizoexkahg5ypZF2RLS0/Dg9hwxCXHIyE1CWalSkn7dLFxgrO1k9agdnbrEWsXr48KtvkvvFCQ++bYJU9AV2haVI8WVaRFEzexXHmUWbxDPTT9+vaPlM+NazYKb9UfooHYdEV3ZbX1ZT0XoLFbvZIHzR1TgAIFLsDQdIETcwIKUIACFKAABShAAQpQgAIUoAAF9BXQFpoW1UMt1aqoJqdl4Flcis5hG3nYY+c4zX8ULsygXVGEpvs1ccXvI7SHIlvPOYegx1kfsZpTJWZ13PyGppvOkAegRaD9yGdNZRWm1efcefkxRi2/IXv41rettQYvN5yNwLi1t2R9P+hYGVP7aK+sKwLZoc+yAtniwJuzfeFgo1lx6NN1t7D+TIRybBEAPT+tBcrb6w5vaAvVLnmrDno2KNh/qNP3tcZ++ROITkhFnS9PygbRFSJefiIcX2ySV2nXNvvIdu6Y1U/7x4xrC02L6/DmHF+UsdT+D/OT/7kNcXOJookq951q5/0f5L/cfBfLjmeFmkWF9dvfyiuxibnEnJ6T5JXwe9Qvi7/e1n2TgThO23umrtD0gr0hmLs7WPaaDJ7bNtv3E9FZ3GSz6mRWxUXfGo7YNKZB/i4GHk0BClCAAhQwoICoNC0qTluUNsPUN1rCxlLzZ1MDTme0Qy3dfQ3Xg5/Cu7Izdn5cD2VKJoPe52fUrokQISblz301u0pB6faVW2Yb4tR7AiPqqBqctrGwxodd3oKTrfYbD41o2QZdiqguLapMiyYq585sN8mg43MwClCAAhSggC4BXaFp0f+j/V/h2H15gQH10PRXR7/H9rv7peHVnxOPqY4vvhaV+HO6sYVniwIUoEBeBBiazosaj6EABShAAQpQgAIUoAAFKEABClCgQAS0haZzO5GDtTkOTm4Cdyd5tWUxTmEG7YoiNP3Ph/XRpqaTVrLFR0NxPSyrIreowPxu+0p68+Y3NO0+/phsrs97VcVHnatkO39sUhq8P/eT9dkzvjHqV7bTOC4+OQ21vziJVFGi+kVzc7CE//SWGn2DniSi9eyzssfbejlhw+j6Wtfzyi+XcD4oRvmcvkFL9aq2X/SqirE57FnvE8KORSogLrPKE+TXtK7rraBC0y2rO+LfsYYJ/IqQ/0q/hwh4GIeQJ4kIi0pCVHyqZOzmaClV7T8XFIO4pDSZe9iP7fR6fY172QOTe3hme84u3Y9FjwX+sj66QtNjVwdg8/msSu6iCv2a93OuPCRufhA3WChaRScrnJ/aokivJU5OAQpQgAIUUBVYfCQU07bckx4a0K4mfH20fypLcVaLjkvGzL8zAzevtvLEwsEexXm7BtnbyqsbcfT+KfTx6oounu2UVXwNMrgRDqIanK7o5IqRHYbB2kLzz/9GuPR8Lyk8KhJLj6xBYkoSA9P51uQAFKAABSiQW4HsQtNnwy/i3d3yG3nUg9F7A49g8uFZymlnt5+CXjU6S1+LCvcjto1FYPR96Ws32/LYM3hNbpfI/hSgAAX0EmBoWi8mdqIABShAAQpQgAIUoAAFKEABClCgMATyG5oWAbgNH9RHDdfMj3pVb4UZtCvs0LSoOqtPpdW8nsf8hKYjYpLReLq80sik7p5oVSPnimADfr8sW/Ifb9SGqJirrb2z7Dp2XXkie+rgpCaoXdFW9tgv++/ju51BsscWvV4bfRtrH7fB1FN4HJtV2bx5NQdM6Vk1R0pR1fZOZLyy32st3fDjEO8cj2MH0xDwmHhcFtIXr8GQeW01Fl9QoensKsvrKyhuIJi7Kxj/XsgKIOt7rOinLTR9/HYUBi+6Ihvm+0E18bpv9qEvbe8TukLT3X/0x+UHsblZqta+Imwd+EObfI/DAShAAQqUFAFxg8vD6KxP6hCfYGBpbqax/SsPYqUbcEQTN5tp+ySPkmKW231euv8cPRZclA6r4+GCd3rUze0QJt9/9YEA+N/J/Nlkwyct0NazZIRhTf7EFfIGSmJwmoHpQr7IOB0FKEABCmgIZBeaFp27rB+Cx/FPlcdpqyb9yqY3cT8m65PMGrj6oIJtefiFnkNsclbBj/kvTUNnT82/Z+NpoQAFKGAIAYamDaHIMShAAQpQgAIUoAAFKEABClCAAhQwiEB+QtOiUvDa9+uhtFkpnWspzKBdYYemy9lZ4PKMVgY5D9oGyU9o+tTdaPRXCz/ndaHT+lbH+x20V8g+dPMZhv/vqmzoD1+qjK96V5M91umH8wh4mBVmFmHX29+1hghQqjdtFYXzuvYOtZyla5SteAjU/foUnsVlhenFrm7O9tUIhu25+kQjpC/63o1MkIWuR7Zzx6x+NbTiHAl4hqF/yq/tDzpWxtQ+8ms7N7LiNSDeE5NS03NzmKyvttC0qAAtblBRbfOHeGFoywrZzpOb0HTrOecQ9Dghz+tWHGhrVRq3v22d73E4AAUoQIGSIiBughPv14qm6/296YwzCH8Rmt4xrhEae9iXFCKD7FP1+9wn/RuhSvmS45eQlIqvl5+UHOtUccLecfVRWvcf7wzizUFMV0AEp0fuGo/bTwPhZOuIYb79ISpPF8emGpju6NEaP3X+pjhuk3uiAAUoQAEjF8gpNL38ynr8dG6JchfaQtOXH93Au7smIikt688V6tt+ybMNfnxpupFrcHkUoIApCzA0bcpnj2unAAUoQAEKUIACFKAABShAAQoUMwFtoelPulRBZRdr2U4jY5Ixd3ew7DFnWwtc+qYlRABWVyvMoF1hh6brVLTFgUlNCuyKyE9oWoRG31563SBrm92/Bt5u6651LBFwrvWFH+KS0pTPi+rj56e2UH4d+TwZjabJq173alAOi9+qo3XM+OQ01JziZ5C1v+zjghWjSl61QIPgGeEgIsQswsyqLbtK6OpbENWYRVVmRcttaFoEpkVwOi8tOTUdIvSt+lpRH0cEikVLSkmXhbtV+2kLTR+7FYUhf8grTc8d7IXhrQwXmh6x+CoO3pDb58WhoG82ycuaeAwFKEABYxZQD03XdC2Do1OaaiyZoen8ncXx625h3ZkIaZAWtdwwuGPJ+aSSnaeDcPBi5keyT+lbCx93KJ4B2PxdITxaVSDg6V28vfNTxKckwNrCCiM7DCt2wWkRmF52ZC0SUhLh7VIdf/WYD3tLO14IFKAABShAgUIXyCk0HZMci/ar+ynXVb98bazq/avGOkU16s+OzMb5h/JPGLSztMW4ZqMwsFbvQt8bJ6QABUqWAEPTJet8c7cUoAAFKEABClCAAhSgAAUoQAGjFtAWmt4/sQl83G011t1l3gVcD8v6yD7RIbtArXi+MIN2hR2abufthPUf1C+w85uf0PTtiHh0+P68Qdb201BvDG7upnOsLzbdwfIT4bLnj3zWFF5uZaTHlh4Lw1f/3pU9v/q9ehAfL6+rVZt8Il/VeBXj9mviit9H1DKIAwcpeoF5u4Px494Q2UKqlrOB3xfN9FpcfkPTPwzywgjf7IPIuhay7nQExq+/JXvawdocn/X0RN/G5eFiayF7TlQV/W5nENa/CHApntQWmg56kojWs8/Kjh/f1QMTu3tm65KbStOztgdi4cEHsvG+6FUVFlqqxWc3qQhND2jKMJZeFyw7UYACFACgHpoWKPsmNEHdSvKf1Rmazt/lsvfaE7z1V9YNh2P6NkS1Cg75G9QEjk5Lz8C3a88iKjYJluZmODutJcrbmpvAyrnEohYQwem3do6TQsXFLTjNwHRRX12cnwIUoAAFClIgPSMDITEPEJscB0/HyrwpqCCxOTYFKCATYGiaFwQFKEABClCAAhSgAAUoQAEKUIACRiOQm9D0gRtP8fria7K1i9DflZmtYKHjM5wLM2inLTT96/BaBgnoiUBBlYnHZXvvUb8s/nrbp8DOZX5C06npGfBQW6+ojD24he7ws66NdKtXFlXLyiuPq/a9FhqHl+dfkB0uqpV/1rOq9Fjvny/iQvBz5fNW5ma4811rlM6mQrl6QF8cM6VX5ni5aQ0q28G3hmNuDmFfIxY4GxiDvr9e0ljh3+/Ww0t1dIfwFQcUZWh6wO+XcfJutHLtokL/yS+bo5KzlU7xj9cEYOO5SNnz2kLTKWkZ8JyU+/en3ISmN52PxEerA2RryU2VbyO+rLg0ClCAAkYtoC00PbCZK34ZJr8pjKHp/J/GYX9exeEXn2jRuKYrhncu/jfe+V0Px+ZjdyS8Ho0q4K83vPIPyRFKjIAITg/Z8oG03+ISnBaB6eVH1yE+OYEVpkvMlcyNUoACFKAABShAAQoUhgBD04WhzDkoQAEKUIACFKAABShAAQpQgAIU0EsgN6FpMWCrWWcR8jRRNvasfjUwsp271vkKM2h3JOAZhv55VbYOfaqt6gNlaqFpsacGU0/hcWyKcnsVnaxwfmoLfbab6z6qQR1xsAiCnv26BWISUlH7y5Oy8Ya2rID5Q7IPZIxdHYDN5+Vh0UvftER5e8tcr40HFC8BbeGx6uVtsHt8Y9hZlc52s0UZmlZ/7xRh/k1jGmS73tpfnERMYqqsj7bQtOjg9bkf4pLSZH0D5rSGvbVuExHiFmFu1fZWm4qYM6Cmxrquhsai63x/2ePidX5wUtNs5yheVx93QwEKUKDwBbR93xM33tyY7Qtble97+oamk1PTcSM8HlcexCL4SYL0ySD1K9tJv4pxS3LbdvEx3l95Q0nwfu/68KrkVGxJRHXphdsu42lM5p/tlr9TF13ruhTb/XJjBSOw9fZeTD0+Vxrc1IPTDEwXzDXCUSlAAQpQgAIUoAAFKCAEGJrmdUABClCAAhSgAAUoQAEKUIACFKCA0QjkNjStrZpzdtWmCzNod/9pIlrOOiuz7eLjgpWj6ubb2xRD04MWXsGJO1GyvYuwsggtG7r9tC8EP+wKlg17/PNmEJWBP113S/b4tk8aoamnfbZL+P3gA8zeHijr06dReYjKtmwlW2DhwQcQFezVWzk7C6x5vx7qVbLTCVSUoenmM88g9FmScm0+7rbYP7GJzrUuORqKqf/d03heV2havZK1OPDTrh6Y1N1T5xzj19/CutMRsud1haZFNeum35yW3YghDhQV7Ld+3FAW3CvZVyh3TwEKUMCwAqqh6ZquZXAnMl6aQP2mRX1C0wdvPMM7y64jKTVdY5HOthZY8149NKyi+/uoYXdmnKOpfj+tX60c3uxaxzgXaoBV/XP0Nk7feCiN1NjTATs+aWiAUTlESRQoDsFpEZhecXQ94pLjWWG6JF7E3DMFKEABClCAAhSgQIELMDRd4MScgAIUoAAFKEABClCAAhSgAAUoQAF9BXIbmk7PABpNk1cwFnNN71sd73WopDFtYQbtxNoqTzimsYZdnzbOdwDEFEPTa08/xIT1tzU8RPBYBJAN2cKjkiDCOqpNVPk+GxSDY7eygtsikHNtZqscp74bmYB2353T6PdG64r4bqBmFdwcB2SHYiMg3lNazzkrCyCrbm5id090qOWMOhXLoIylvMpyv98uQ7znKZqokC9CZ9qatsr1PwzywgjfvN10MHLZdey+8kQ21W/Da6F/U1fZY+J9TNyEMG+3/CYERae9ExrDw8UaDjbmsuPOBcWgzy+XNLbySZcqmNyjKkqpFA999DwZP++/j6XHwjT66wpNi45+d6IxcKG8MrV43MrcDKPau2NU+0pwc9CsBh+dkIpbD+Mhqt1XdrYqNtciN0IBClCgMARUQ9PzBnth4obMn+0Un+qhWENOoelfD9zHtzuCclyytu9NOR5UjDpsPBeJj9cEKHf0To+6qONR/KovX7z7CH/vv6nc54p36uJlVpkuRldy4W9l9bXNmHtmkTSxqVWc9g+6gs3ndkpr93apjr96zIe9Zcm+gaTwryDOSAEKUIACFKAABShQ3AUYmi7uZ5j7owAFKEABClCAAhSgAAUoQAEKmJBAbkPTYmurTz3EpBeBDcVWxceDizCspbmZxu4LM2in7SPMRaBvcg9PjPCtCHtreYgyKj5VCl96udloXbtiM6YYmhZr7/6jPy4/iNU4J0087TGhmyfaeTtpfBS72Gvwk0SERSWhrZf+H0muPpcIT0bEJMvmHt2pMr5+pZper5Dvdgbhl/33NfqKccd388CApq4aoVjRWcx5OyIejT3sWf1WL2nT66Stgr22XYjq064OlohNTJOu51SRSFZpqqFpUQlavB8oWmpahkYlTnOzUrCykL/HiVDy2M5VckQU17K4ptWbqNTsW9MRMS+CxdfD4mTrFDcaPItL0ThOrEUExD/ukjV3z58u4mLIc619vSuUgb21OcQNDiFPE3WuN7vQtDhozN8B+PdCpM7jxbrK2llIr724pDTJVFHR9MNOlfGVnq//HEHZgQIUoEAJEVD92VbcCDhubQACHmZWmxaV/ptVdZB+n11o+mF0Mpp8c1omJj6ZQXxvuBD8HEGPE5TPiZ+br81qpfVnrBJCjt4/X5RcRKtawQHv9aqf7Z8TTM0lLjEFC7deRsSzzOvotZYV8OMQL1PbBtdrhAJfH5uLbXf2SiszleA0A9NGeCFxSRSgAAUoQAEKUIACxVKAoelieVq5KQpQgAIUoAAFKEABClCAAhSggGkK5CU0LYKH9b46hZjErICh2L0Iw4pQrLZWWEG7ZcfD8OXmuzpPhgj0ifUrAn2KjqqhE/GYqDC3S60qrAgAqjcxjmprUc0Bq9+rp9fFIKrOfqRSyU79oKSUdI2QpwiymJdWKRn74iARdPyyt2YYOehJIlrPPpvtesQeRBBZhESfJ6XJApoh89pqhKp1Dbbm1ENl9UNdfY581hRebmX08hEVhVvNPiuFPHU14SHWbmZWCrGJqVJAUxGM3TC6fq5C33otip2MRkDcjPHaH1c0XiO5WaBqaLra5BMaIWl9xhrzUmWtrz31Y8W12WrWWY33zezmeKmOM0TQTQSptbV32rljpkql7DOBMXj1V81q07rmqFrOBg+eJsoMcwpNi3B353kXdFb6zm4/XeuWxfJ3fPRhZR8KUIACFHghoB6avhkeh0/X3ZKe7dWgHBa/VUf6fXah6bGrA7D5fNYNL9+8Wh3vts/6hBjxs7P4GVrRxI11E7p5lNhzoH6DaJu67ujXVvsnU5gi0n8n7uL41czzXc7eEts+bgjPstamuBWu2QgFTCk4zcC0EV5AXBIFKEABClCAAhSgQLEVYGi62J5abowCFKAABShAAQpQgAIUoAAFKGB6AnkJTYtd/nkkFN9suSfbsAjfXp3ZCiLIqt4KK2iXkQF0+P487kRmVk7Tt/08rBYGNXNVdh+x+CoO3nim7+HKfqJq394JjfU6TlRrFWFyQzRRMXr9B/W1DpVTkDy7+f2+aAYRrNSnxSalwftzP51dPVysceqr5voMpexzPvg5Bv5+OU9h1jkDakIEQNmKr4CotPnJmgCt1dT12XVhhqbFes4HxeCVX/QLNTtYm+Pg5CYYveomzgbGaN2OemhadPIPeY6hf1zNMZwtbiDZM74xhv3vqqwifE6haTGHqEb/5+FQqXK2evXu7Nyrl7fB8c+b6XNq2IcCFKAABV4IqIem61Qsg1pfnFT+bHR9li+cyphnG5pWvTGopmsZHJ3SVOYr3tdrf3lS+oQA0bT1KWknZMrGO1jpF67c9uAOXmhRu4LJM1y69xir9t1Q7mPGqzUwqr27ye+LGzAuAVMITjMwbVzXDFdDAQpQgAIUoAAFKFD8BRiaLv7nmDukAAUoQAEKUIACFKAABShAAQqYjEBeQ9MJyenw+SorsKHY8Be9qmJs5ypa919YQbvbEfEYvOiKLAiY0wn5tKsHJnX3VHYrTqFpsSlh8snaW7gYkvlR4/q2laPqoouPi77d8eaSa9h3/anW/lN6VsXHXbRfG9lNEJ2Qimn/3cOGsxF6r0N0VA3E5upAdjY5gSMBz7DseDjEr0mp6dmuX4SF61W2QwdvJwxo5ioFw0Sr+/UpWZV1fRGye8/TNkZETDK+3xWEdad1X8+vtXTD5Hvz2t0AACAASURBVO5VUcHREsP/dxWHbmq/gUNbaFrM+SQ2BZ+sDcCJ29FaPcSNED8MqilVYu/+o78sdK5PaFqxr8jnyZixNRAn70ZnWxFe0b+SsxXOft1CX1r2owAFKEABAOqh6YZV7KRPVVFUhlZ8H9JVaVr8zF5jygml5YxXq2OUSpVpxROqY4obIAN/aFPi/dW/B48b0BiVy9mZrIt6YLq1lxM2jtZ+06XJbpILNxoBYw5OMzBtNJcJF0IBClCAAhSgAAUoUIIEGJouQSebW6UABShAAQpQgAIUoAAFKEABChi7wI2wOHSed0G2zP0Tm8DH3TbHpc/fE4L5e4Jl/dwcLOE/vWW2xxZG0C4lLQO/HbiP9WciEPI0Mce9DG9VAXMHeyn75TU07VvDEZvGNMhxPtGhsCpNqy7mvwuP8NvB+7gbmZBjuFQc9+vwWhjQNKsCd04bE4FpEZzW1i5MaymFQPPaLt2PxaxtgbgeHqdXuFUET38c4p3X6XiciQqIa/thTJIUHE5PBzKQgQqOVqjoaAlXB0uUsSxtFDtLTk2X3puCHidKlT1LlQI8y9qghqsN7Kyy1ij6xCamSRX8rSzMYGVeSvq9tYUZLEqbScdl18KjkqTq06IitNh7tXKZcyha0JNEJKWkw8G6NOyszSE+McAshzG1zSeq/D94lojbEQkQnywQn5wGG0szaUxRZV78L75mowAFKECB3AloC03fe5SAtt+ekwYqZ2eByzNa6aw0LT6VodMP55WT6rohbvHRUOkmNUW7+10bvm8j8xNsxM2Hijb3vXY5fu/N3RkunN7qgelq5ctg2dt14F0h8+YxNgoUhIAxBqc3n9sJEZoWzdulOv7qMR/2lqZ7M0RBnDeOSQEKUIACFKAABShAgYIQYGi6IFQ5JgUoQAEKUIACFKAABShAAQpQgAImKVAYQbvniWlShWURohQBSlFxT4T3RIBQhCk9y1pLH2te0poIat6JTEDQ4wQkp2UgIyMDNpalUd7OAtXK20AE4I21iVB84OME3ImIl8Lfyali7WZwtDGHZzkbVHG2Qum8JD+NdcNcFwUoQAEKUIACJVJAW2haQPT86aLyE0Q2jK4vfaKIuFFGtB3jGqGxh730e7870Ri48PL/2bsL6KiOvg3gT9xDEgJJkGBBgrsEd4q7a6kAbV8oUooUt6ItLS1UcIfiWtwhSHAIJBAkODHi+p65aZa9K3Hb7DPn9JDszp0785u7F97ve+5/FXa7v6mCWiVs1SzFN3qM2vRA8frVKbXhYmeml+bKixb/O6Lq1IuIiEn8JgsXByuM6VFdp1xUA9MlC1pi5WAGpnVqE3V4srkpOM3AtA5fSJw6BShAAQpQgAIUoIDOCzA0rfNbyAVQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQIGsFtIWm91x/i2Fr70snb+Zuj3svwzWGpsU3CnjMvqyY5F+D3dG2sqPapJcdf47Z+x4rXn+6sAGM+QCa5KFc2Vv8bm9thkn9amftxmfS6KqB6VIFLfE3A9OZpMthUiugHJy2MDHHkMZ94GKX+m8zSu15kuunHJiu4VwZPzWfnmyF6SuvbqCmc5XMODXHoAAFKEABClCAAhSgAAUAMDTNy4ACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFEhWQFtoOjY+Ae6TLkB8c4hoZsaG0rdviKZcaVr0cx17VnGOb1u5YlybYmrnFAFsEcQWzdbcGPfn1OPOKAlcehSMLr9+rNhtZmKEL9pVRDEn9arduQXu2LVnOHjZTzEdBqZzy87o5zyUg9OWphYY3Kh3tgWnvfxuQYSmRevg1gozG47TugkHfI8hMCoYCy79joZF62BuowmwNrXSz03jqilAAQpQgAIUoAAFKJCJAgxNZyImh6IABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKBAZgksOPQEbgUt0KV69lZC1TR/baFp0Xfm3sf4/cRztcOUQ9PizQo/XERgWIzUz97KBLdm1IVyEekPkXGoMPkCRMBatMpFrHFodLXM4swz48TEJaDX8lu46BusWFP3RqVR1905V63xxfswKSx970mAYl5lnC3xx0B3iD/ZKJBTAhvu7MACz9+l01uYWGBI46wPTqclMC2qS392cKyMp7R9CfzScjacrQrkFBvPSwEKUIACFMgUgX+890vjdCvbLlPG4yAUoAAF0irA0HRaxdifAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAlkocPRuAH7Y6Ysn7yNhAMB/ccMsPFvqhk4uNP0iKAo1Z3iqDaQaml50+CkWHX6i6NeqQn78OdgdJkYGEIHpHr/dxM3noYr3f+1XFl1r5HxgPHVC2d/r7zMvpOskqbWrUwJNqxbJ/oloOOOFuy9x8PIThEcmhuRFq1cqH+Z2c2NgOlfsECex5+G/mHJ2gQRhYWKOIY37ZFnF6bQEppN25o8bG/DbtdWyjRKVple3/Qlu9sW5gRSgAAUoQAGdE3gS/BzxiEeXHUOluV8fckTn1sAJU4ACeUOAoem8sY9cBQUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSggA4LRMXGY8fVN/jn6huc9/lYQdjS1BA+8+rn+MqSC02LyXVbdhMXlCofi9dUQ9Ph0aKS9EWItSo3W3NjhETGyl4rbG8Gz8m1YSBS42xaBU55B2LCP77wexch9alZ1gmtarjCwcY8R9R8/INw5cEbXHnwWnb+US1dMbZNMVll8RyZIE9KASWBE0/P4YczCxAaHZZlwen0BKaTprj+zj9Y6Llcbc9Wt/sJVQtW4F5SgAIUoAAFdEqg396v8SzEH8vbzEffPSMYmtap3eNkKZC3BBiazlv7ydVQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQrokMCjtxHYee0tdl57A/GzalvQszT61XXO8RWlFJo+cjcAg/66I5unamhavClCvoP/vqsWnFY+UISot42ohEpFrHN83bowAf/AKEzd9QgHbr1TTLdsUXs0qFgI7q4OWb6EgA+RuPXoHW49fg+/1yGy81VxtcH4T4qhSVn7LJ8HT0CB9Ah4B/hi6MExWRKcfhn0BitPbURkTBQ6uLXCzIbj0jzFf7wPYOb5JWrH/dR8Bpq41kvzeDyAAhSgAAUokFMCVVe1lE7tNeRfVFvVSvrTQPpeHTYKUIAC2SvA0HT2evNsFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFMCZB0FSUFoEplUrLyfxWJoa4cKkmihgY5rjYsqh6WPjqsPdxUo2p/gEoPykC7KK0ZpC0+KgV8HRGL7uPq76hSBWHPhfMzY0QIsKDvipdxnYWhjn+Jp1bQKej0OwxfM1Nl16pZh6cSdb1CrnhNKF7TK9+vStx4lBaRGYjomTVw8XExjetIhUXdrCxFDXKDlfPRP4EB0qBacfBDyCuYkZ+np0RYkCrhlSEBWmD9w4lqHAdNIEDj46jgmn5qrN5wePb9GtbNsMzZMHU4ACFKAABbJDIAEJUlBatGuDD6P66tYMTWcHPM9BAQpoFGBomhcGBShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABbJJYP/Nd9h86TWO3QuQndHa3AihkXGy1/rXc8b8HqWzaWbZf5qEBODJ+wgpRF3UwRyF7c2yfxJ58IxnHwbhj9MvcPTOe9nqCuSzQJEC1ijiaI3C//1nYZZyOD0mNh6vAsLwUvovXPFzaESMRr2KRWwwtnVRtKqQPw/qckl5VUA5OC3W2Kx8AzQtXz9dy91x5QBEaFq09FaYVj3xyacXMOrYFLX5jKg+GF9U6ZeuefIgClCAAhSgQHYJJIWmRWXpK4MPosbqNvAa/C8MDFhpOrv2gOehAAU+CjA0zauBAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAlks8M/VN1JY+pxPkOxM5VysYGwI3PYPk16vWdwWV/xCpJ8Pja6GykWss3hmHD6vCuzyeos/T/nD6+kHrUsUQWpbK+2VzD9ExOBNYHiKRPmsTNGiQn70qO6IRmXsUuzPDhTIjQIiOD3k4Gj4BDyWpleteCW0rdJcqj6dmhYZE4WVpzbiZdAbqXtmBaaTzu350gujjk1FeEyEbDq93DthQt2vUzNF9qEABShAAQrkiEBCQgKqrW4FEZq+POgAaq75RKo4bWjAbyTJkQ3hSSmg5wIMTev5BcDlU4ACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKZJ3AhouvsOnSK1x78jG4ampsiPaVHdGuiiP23niLXdfeShMY0awIVp99ifDoOOT1KtNZJ86RVQVOegfi1P1AiD+9X6UcgE6LYLWSDmhd0RGD6uRHPouUq1anZWz2pUBOCIjg9IAD/4Nf4DPp9C52BfFp474pBqdFUHrnlf1ZFphOsrj19j5GH5+Gt+HySvItizfCgqY/5AQZz0kBClCAAhRIUSA+IQHVRWjawACeA/ej1pq2DE2nqMYOFKBAVgkwNJ1VshyXAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClBAbwV2e73F7yee4+bzUIVBNVcbtK3siPZVHFEsvzmGrrqLg7cSg29TOpZAQGgsfj2eGNRjlWm9vXSydOHiejxy5z2O3AmQXZupOan49nQne0sUzW+JCi6W6FrNETWLWaXmUPahgE4JiOD0+POzcf7xFWneotK0CE6LALWmJgLTosK0qDQtWmZXmFY9p2+gH8acmAG/4MS/L5JaNaeKWNV2iU5Zc7IUoAAFKKAfAvEJ8ai+ujUMDQxwccA+1F7bjqFp/dh6rpICuVKAoelcuS2cFAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSggK4J+L6JwLYrryEC00/eRyqm/1mjwuhSvQBEaDqp9fvjNk7cD5R+ndapJDpWLYBWi67hXWgM+tV1xoKepXVt+Zyvjgm8DonG3RdhCAqPQVB47Mf/ImKl6zAmDihV0BIlC1oq/nSwNIKtqY4tlNOlQDoEYhJi8c3JSbjod01xdNsqzVGvdE3ZaF5+t3DgxrFsC0wnnfxF6GuMOzEDd949kM2nkLUTDvRYn44V8xAKUIACFKBA1gl8DE0b4sKAPaiztj2uDj4MIwPDrDspR6YABSigRYChaV4aFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFMiAgOfjEGy/8hrbLr9BVGy8NFIBG1MM9HDGmNbF1EbutuwmLvgGS69P71wSnzcqjJ+PPsOPB/yk1/aOrIoaxT4GrDMwNR5KAQpQgALpFAiPj8CoU1Ph6eelGKFEAVd0rdkWdlb5cPDGMZx/mFiNWrRxtYejX4Wu6Txb2g8LjAzG+JOz4PnyuuxgQwNDnO23C5YmFmkflEdQgAIUoAAFskAgLiEeNaRK04Y413836q3rgKuDD8HIwCgLzsYhKUABCiQvwNA0rxAKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKpEPgyJ0AbPZ8hYO33iuOLl/ICt1qFMTwpkU0jtj+5+u49uSD9N7MLqUwtGEhRMTEo+XCa3j0NgJdaxTEr/3KpmM2PIQCFKAABTJbQFScXnrzb6y7tl3r0NamVpjZcByautbP7NOnOF5UXDS+PzkbJ56eV+u7s8vfKGHnmuIY7EABClCAAhTIaoG4hDjUWN1GCk2LB3s81nfE1UGHYGTI0HRW23N8ClBAXYChaV4VFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFEiDgM+bcMw78AQHbr5THNW0nL0UeBaBaW2txcJruPsiTHp7dtdSGNKgkPTz2vMv8f12H+nnrcMroUFpuzTMhl0pQAEKUCCrBY74n8HUEwsQHhMhO1UHt1YYXm0gClk7ZfUUkh1/0ul52O97TK3P763moV7hGjk6N56cAhSgAAUoEBcfhxpr2sDIwBCn++1E/fWdcGXQIRgzNM2LgwIUyAEBhqZzAJ2npAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIU0D2BB6/CMXrLA0WlaLGC3nWcpKB0fbfkg84N5l6RKkmLNrebGwbVd1EAdFh6A1f9QvBJpfz4e0h53YPhjClAAQrogcCH6FDsefivtNKy+UuhkLVzjoelldlnnf8Z2733qe3EtPpj0LlMGz3YIS6RAhSgAAVyq0BsfBxqSqFpI5zq+w8abOiMK4MOwtjQOLdOmfOiAAXysABD03l4c7k0ClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKECBzBPYdOkVxmx5KA3YvWZBzOhcCnaWKf8/+mvN9IR/YJR03Lzubhjo8TEwvffGO3y55p703prPKqBleYfMmzBHogAFKEABvRJYfHkF1t7errbmYVUHYFi1gXplwcVSgAIUoEDuEYiNj0XNNZ/AyNAIJ/tsR8MNXXB50EGYMDSdezaJM6GAHgkwNK1Hm82lUoACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKZEzgvE8wzIwNUKO4baoGqjTlIt6Hxkh95/cojf71nGXHDfzrDo7eDUDLCg5YM7RCqsZkJwpQgAIUoIA2gd+91mLF9XWKt0UVTxFW6162PSZ7jCQcBShAAQpQINsFkkLTxoZGON57Gxpt7MrQdLbvAk9IAQokCTA0zWuBAhSgAAUoQAEKUIACFKAABShAgSwVOOcThDv+YRrPYWAA5LMwhoOVCQrYmKJ8ISuYGBlk6Xw4OAUokHsEIqLjse7CS60TMjU2hIOVMRytTVGygAWc85nmnslzJhSgAAUoQIFUCJSecB5hUXFSz4U9S6NvXXlget+Nd/giqcr00ApScJqNAhSgAAUokFGBNbe3YsnlP6Vh5jWeiIWey/EuIgBNXD3wU/PpGR2ex1OAAhSgAAXSJBATH4taaz6BeJDnWO8taLyxGy4POgATQ5M0jcPOFKAABTJDgKHpzFDkGBSgAAUoQAEKUIACFKAABShAAQpoFRi50RvbrrxJlZCxoQGquNqgR82CUqBE/M5GgawUCI+OwzcbvGWnaFk+P3rXccrK03Ls/wTehcag8pSLqfZwtDZB8/IO+KpZEbgVtEz1cexIgfQKbPF8jX/vvJcdvrRvWViZGaV3SB5HAQrokYDr2LOIjU+QVry4dxn0rq3+74tuy27igm8wq0zr0XXBpVKAAhTILoGt9/dizoWl0umm1P8WG+/uhE+gHyoVKId17X/JrmnwPBSgAAUoQAHExMeg1pq2Umj6aK/NaLKpOzwHHoCpEUPTvDwoQIHsF2BoOvvNeUYKUIACFKAABShAAQpQgAIUoIBeCaQlNK0MY29lgrndSqFj1QJ65cXFZq9AUHgsyk++IDupCEwv7lUmeyeip2dLa2hamemTSvmlip3iXsFGgawSGL3lATZfei0b/s7Murzusgqc41IgjwjExiXAddxZxWp+6lMGPWupB6Y3e77G6M0PpH5rWGU6j+w+l0EBClAgdwns8zmCyWfmS5MaV3s4Tjw9jyuvbsDFuiAO9tiQuybL2VCAAhSgQJ4ViImLQa21iaHpI702oemmHgxN59nd5sIokPsFGJrO/XvEGVKAAhSgAAUoQAEKUIACFKAABXRaIL2h6aRFj2rpiu8+KabTBpx87hVgaDpn9yYjoWkxc1F5es/Iqiie3zxnF8Kz51kBhqbz7NZyYRTIMoHImHiUHH9OMf7PfctK36KiqX2yxAs3noWyynSW7QYHpgAFKEABIXDsyVmMOT5dwvi6+hDcD/DBUb8zMDE0xvkBe6U/2ShAAQpQgAJZKRAdF4Paa9tKf+cc6rkRzTf3hOfA/TA1Ms3K03JsClCAAhoFGJrmhUEBClCAAhSgAAUoQAEKUIACFKBAlgpoCk0Xd7SQzvkyKApRsfEpnn9M62IY09o1xX7sQIG0CjA0nVaxzO2vKTQtKkfnszBGaGQsxP7Exicke1IrMyN4Tq7Fyr+ZuzUc7T8BhqZ5KVCAAmkR+BAZh7ITzysO+aVfWXSroTkwvcvrLUasuy/1ZZXptCizLwUoQAEKpEfgvP8VjPh3gnRoH/fOiImPwXbv/dLvouK0qDzNRgEKUIACFMgqgei4aNRe2w4mhiY42HM9WmzuhUsD98OMoemsIue4FKBAMgIMTfPyoAAFKEABClCAAhSgAAUoQAEKUCBLBVRD0y52Zrg6pbbinCI0ff9lOG77h+KnI0/hHxilcT6HR1dDpSLWWTpXDq5/AgxN5+yeawpNLx9YDh2rFlBMTDxccftFGE7eD8Sqsy80TrhxWXts+rJizi6GZ8+TAgxN58lt5aIokCUCgeGxqDD5gmLsZf3LoUv1j3+fqZ60/5+3cfxeIKtMZ8lucFAKUIACFNAkcP3NHQzeP0p6q4mrBwpbO2PD3R3S7+va/4JKBcoRjgIUoAAFKJAlAsqh6QM91qHllt4MTWeJNAelAAVSI8DQdGqU2IcCFKAABShAAQpQgAIUoAAFKECBdAukFJpWHjg6Nh7jt/tgi+drtfOVdbbEie9qpHsePJACmgQYms7Z6yI1oWnlGV56FIxBf91FSGSs2sQ3fFERTcvZ5+yCePY8J8DQdJ7bUi6IAlki8PZDNKpMvaQY+/cB5dCpmvbA9JkHQei1/JbUf9Wn5dG6Yv4smRcHpQAFKEABCqgKeAf4otfuYdLL5fK7waNwTay8uVn6fXGzqWhWrAHRKEABClCAApkuEBUXjTpr28HUyAT7uq9Fqy19cGngPpgZmWX6uTggBShAgZQEGJpOSYjvU4ACFKAABShAAQpQgAIUoAAFKJAhgbSEppNOtO7CS4zf5qN23qNjq6N8Iatk5xMXnwCfNxG4+TwUN559wL0XYbA2M0JVVxtULmotVasuaGOaoTWFRsXB+1U4HrwKk6pk33sZhpi4BBSyM4NLPlPUKmGL5uUdYGxokOx5Dtx8h+jYBEUfMb+SBSy0HvM+NAYiZKPcqhazQfH85rLX3nyIxvmHwYrXzE0M0aZSfqmK98Fb73DBNxgOViboWqMg6pbMBwMDQISHfz32THIThu6FrKSvk6/mapNqq7CoOMni1vNQ3PIPxcPXEShsZ4aKha1QoXCivaO1SarGU7WpXdJW8hXtyJ0AnPQOxLOASIiqjm4FLVCpsDVEn4qFtVcjfx0SjQs+H13EWGIvv9v2UDanGsVtMbRBoRTn2a6KI0yMkt/jpEFeBUfjgm8QfN9EwO99JILCYxARHQ9Rad3CxAj5rU3gYGUMeysT5LcyQT23fHB3Sf5aT3GCOtAhraFpsSRxrbZcdE2tKn2D0nbYOrxSiqsWx4vK9jeeJd4jxHUhrCsXsZbuEWWdrVK9r5pOFp8APA2IxMNX4bj/Kgx3X4RJ16r4zIlruFh+c7Sv4oiiDvLPrepYfu8icP1pqOJlU2MDfFLJUfq8amvnfILwNiRG7bOv2l/cF8U9LKkVcTBDzeK20mf34K33uOMfinIuVuhRqyDcClpK3UT/P07549HbCFiZGaJCIWt82rAQnGxTdz9NSACeBUbi9vNQ6T4jziXuf+VcLKV7gxhP/GyUwn1TzCUkIlaqzqrc2lbOD1NjQ+kztfXya8lOuCfdzyoWskajsnaK+4gmwxP3AxEcLg/k/332Ba76hci6/9jDDTZmxslea25OFsnej1QPFr5X/ELw+G0EngdG4UNkLMKj4xETFw8bc2Pp+hH3iMR7hQnaVXaUfmajAAVyXkB8I0KNGZ6KiawYWA4dlL4xQdMM/7fRG9uvvEFq/+7K+VVyBhSgAAUokJcEnoW8QId/BklLymdmi77lO+N3r7XS79/VGYG+5bvkpeVyLRSgAAUokAsElEPTe7utQeutfXFxwD6YGzM0nQu2h1OggN4JMDStd1vOBVOAAhSgAAUoQAEKUIACFKAABbJXID2haTHDurMuS8FD5da7jhMW9yqjdQEPXoejz4rbEOGV5FrPWk6Y38NNCtilpYkw3qy9j/H3mRcpHmZmbIh+9ZwxpUMJrecp8d05KeCX1KoXs8G+kVW1ji3Czt2W3ZS9P6tLKSm4qNyO3g3AwL/uyF47+G01fLLES23s4U2LYGK74qg89RICwz6GLZM6LuxZGn3rOqe43lVnX2DSDt8U+33ftji+bl4UKeUiXceeRaxIn/7XZnctJQWQxBpE+FtbE9fI7C5usDBV31sRxP5s9b0U55jaDvdn14OthfbgpJj/uvMv8evx5ylek6rnnNS+BL5qViS1U9HZfukJTYvFatvLG9ProICWhyLE5TT/oB+WHn2WrJetuTHWfFYedUrmS7OrCL1+ufZ+qvZbVM//sUdp1C5hq/E8f572x9Rdj2Tv7flfFSncrK21WewlBZKTmrgPPZ5fX6375J2+WKl0HxMPlbStlB9z9vup9d0yrJIUNG4474rae+LBkEOjq6X4MIu4Jw/6+64UVk+uiYdG1gytgFIFtT88Io4XQfQWC6/Jhjo7oSbehERLfwco31eVO4n5Tu9cEkO0PBSh6e+dNF8E/x3Qr64zFvQsnezhAWEx+PXYc4j7p7Y5axvg3zHV0hTKTu86eBwFKJC8gPi3qrh3JLU/BrlLD8Yk18R9WtyvRfu1X1npITY2ClCAAhSgQHYLvIsIwCdb+yMmPvH/DjC9wVhMPbtQ+nlQxZ74ttbn2T0lno8CFKAABfKwQGRsFOquaw9TI1Ps6bYKbbb2Y2g6D+83l0aB3C7A0HRu3yHOjwIUoAAFKEABClCAAhSgAAUooOMC6Q1N773xDl+ukQdcXezMcHVKbY0i2qpTa+MrbG+G+r+xRwAAIABJREFUjV9URGmnxCqqKbWzD4OkMKSmYHFyx4rzbBtRWa0atDhGNTQtgosHRmVNaFqMff3pB7WpihChCDLP2vdY4zJE6NJ7Tj2twW/hMWzdfbUK2MmZiGrhIhgpbLQ11dD0yBZFcexeYIqhSzGeGPfCpFpqlb6zMzQtwpC9l99O1Xw1GSwfWA4dU6hSmdI1qwvvpzc0LaoW157lqRag1+YmqkkP+PNOmvZjVEtXjG1TLMWAv3AWVdZ/2OWLzZdep5l9TOtiGNPaVe04TaHpnV9XTjbMnd7QtKgAL6o+h0TKqyyLSYlK0xUKW2G311uNa0upSuqe62/x9Xpv2UMQKSHN6FwSnzUqrLWbptC0CCp+s8E7VeFjbQ/gZGdoWlTa7vHbLY3mKfmI9+/Oqgc7y+SrXadmHPahAAXSLyAq7zeY+/GBkr8Gu6Nt5eQD0+Js4hsu1l94JX2bx/5k/t2X/pnxSApQgAIUoEDqBMJjItB2e38ERSZ+s8ofbebji0PfST+3KdkU8xpPTN1A7EUBClCAAhRIQUA5NL2760p8sq0/Q9O8aihAgRwTYGg6x+h5YgpQgAIUoAAFKEABClCAAhSggH4IpDc0LUKR1adfggg7KjefeR6wNDWSvbbp0iuM2fIwzaAiMHxrZl3kS6ZasBj08O33GLLybprHTzpAhHjPT6wFEyMD2RjZGZpO9+QB7P1fFdTQUN1W7FG92eoVwVNzrspFrKUKtdqaami6vpsdzvkEpWZoqc/UTiXxZWN56DI7Q9M9f78FEbRPbxOVwasUtU7v4TpzXHpD02KBO66+wdcbvGVrHdqwEGZ2KSV7TVT8rj7tEsS50tq+aV4UE9oVT/YwUcG60bwrEOG59Lbf+pdD5+oFZIdnZ2g6vfNOOu7F4oYah8jIZ27HV5VRt5Tmat+aQtMivJ2Wz9zRsdXVKmRnV2hahOxrTPdMd2Ba/N31dGGDjG4bj6cABTIgIL7dpMmPVxUjrBxSHm0q5U9xxBdBUag5w1PqN79HafSvl/K3eaQ4KDtQgAIUoAAFMiCQgAS03z4Q/h9eSaNs7bQCPXd/Kf1czakiVrVdkoHReSgFKEABClAgUSApNG1mZIqdXf9G220DcGHAXlgYm5OIAhSgQLYLMDSd7eQ8IQUoQAEKUIACFKAABShAAQpQQL8E0huaFkrD1t6HqFKq3Pb8rwpqKgV4Y+ISUOmHixrDZ062pqhezAZB4bG49uSDxgqknzcqjOmdS2rdlKjYeFSZcknj+CK4JipVW5oZISEhATefhWqtpjq+bXGIasnKLTtD02Kuogpv5aI26PDzdbUw+rROJaXqiJ+tugvxtfHKTVv13q2XX2PUpgdqdlZmRvAolQ9O+UzxITIOF32D1c4nDlrzWQW0LO+g0V41NK3cydbcGFVcraWK0g9ehUt7q9pEhew7s+rKAvai+vP8g08QJ1Ku/7XgiFjsu/FOdrgYt0lZ+xQ/qLO6loI4j2rTFOhM6iP6VyxiDVcHc9iYG0FYifC5uM7EXN6Hxkj/bRlWEfZWJinOQdc7ZCQ0rRw8S3IQn/d9I+XV2ledfYFJO3zVqMRnonJRaxSxN5eqsD8NiNTY5/r0OnBIZi9WnnmByTvVxxeDiQrO7oWspM/B84BIrcFtcV3cn1NPdj1ld2i6uKMF/hhUDlEx8eiw9IbMQsxP3Adc85uj49IbUmVt5XZnZl2161V8zkQwUPXBF3GceGiiUhFrxCUk4Mm7SFzwDVazL1nAAmcn1NR4iSf3GRMHiM+wOIf4fHk+CtG4t+JBEPFAiHL75+ob6X6l3LZfeaP2d0fXGgVhYaL+2Vc+rlFZe3Soorni7O8nnmPmXs3V/cX9rVoxGxSwMYGNubF0HhH8j46NR0BYLN5+iEY+S2OIgCYbBSiQMwKq96DVQ8ujVYWUA9NittN2P8Ifp/yle1RyD4/lzMp4VgrIBZZ7rcWtN4/QyfVzNCjlrPbgKr0oQIG8JSCC0g8CHkmLOtFnO5pu6i79XMjaCQd6rM9bi+VqKEABClAg2wUiYiNRb10HiND0ji5/od32gQxNZ/su8IQUoECSAEPTvBYoQAEKUIACFKAABShAAQpQgAIUyFKBjISmZ+97jGXHn8vmp/rV58tPPseMPerhs1ldSuHThoUUx4qQX4/fb0nhSNUmQpEFbUw1Oohgmwi4qTZRTfCn3mVgq1SlWgS4T3oHYujKu7LwtAhvb/qyIsq5WMmGyc7QdIvyDlj7WQXp/NN3P8KKU/6KuYhwrgg9irbuwkuM3+Yjm+fsrqUwpMFHS/GmtrB6j5oFMaebmxRWTGoiPCnCyr8ceyYb18XODJd/qA1DeQFuqY+20LSYhwh4K1ftPu8TjO6/3VTbI/GV99VcbZK9vkWgvvzkC7I+ves4YXGvMun+XGy8+Apjt8ornwuP3weUg9gHto8CGQlNi+uq6NizMk4RRr84uZbiNRFGr/jDRbWQr+i3d2QVFFD63Gurijy4vot0TWtqIuBebdoltYclkkLGrSvKQ3SvgqMxaYcPDt56LxtueNMimNS+hOyzkN2haeV7ZtP5V+H9Klwxx45VC0ihadGGrrqrNv8z39dEqYIWsjVpCquLQPBfQ9whqkIrt3svw9D/zzt4GRQle/3XfmUhAsqqTVtoWgThReVW8RlWbkuPPsO8A36y11JbrXn0lgfYfOm17FhNIfG0fK6/WHNP7WENt4KWWPmpO8SfbBSgQO4VEA/ItVnipZhgcg+AaVpFodFnpJdZZTr37jFnlijQZGM3fIgJw4c3tRH6siWmdCyJYU3k3+JCKwpQIO8JDDnwLbxe35YWdrz3VvTd+zVehb2BkYERzvbfxWqgeW/LuSIKUIAC2SbwMTRthu1d/kCH7YMYms42fZ6IAhRQFWBomtcEBShAAQpQgAIUoAAFKEABClCAAlkqkJHQ9PoLr/DdNnn4VIRZlQNxqsFjsRhNVZ3F6yLo6zHnMvwD5cG8oQ0LYWaXUmoOoqJnlamX1F7/qlliwFFbu/U8FO1+ui4FKUXlwWX9y8pCxEnHZWdoekrHEhjWpIh0atUA4SeV8uPv/6qWnvMJQo/fbsmWpik0vfb8S3y/XR6uFmsV1Ra1NdVrQfQ7Nq463FXC5OJ1TaFp5eC36jlm7X2M31TC7b/0K4tuGgKXysdmRWh64j8+WH3upWyKP3QoARGMZZMLZCQ0LUYqN/GCrAq88gMA4n1NVaBFgN1rWh1YKwX7k2a189obfLXeW22b7s+uJ3tAIqnDuK0PseFi4tdYJzURmL48pbZUZVpTE5XFR6y/j91eb6XK0uKz18xdvbJ5doemj42tLlXFFq3Lrzdx6dHHisvK4T5ND7NoCk2XnnBeLax+4rsaKOusORT8PDAKtWd6ysgal7WXHjhRbdpC06oPyygf12LhNYjjlFtyD8wk9cuK0LTHnCvwexchm8vJ8TVQxomBad4jKZCbBa4++SB9W0dSW/d5BTR3T9vDUF+uuScdvmKQe25eKuem5wJvwt+h1ZY+SIg3RcDD4YiLzoexrYthdGtXPZfh8imgHwJfH5mEs88T/12+ocOvWOi5XBGk3tV1JYrnk3+Dln6ocJUUoAAFKJBRgaTQtLmxGbZ2WoGO/wzG+f57YGkifwg/o+fh8RSgAAVSI8DQdGqU2IcCFKAABShAAQpQgAIUoAAFKECBdAtkJDR9yjsQfVYkVjlKalM7lcSXjROrnGkKNYvqofdm19MYUhbHaApi1yhui73/q6K2xuP3AtH/T/n5RaXUmzPqwNTYMFmTey/CcPdlWLKh3ewMTSuHzVVD033qOGNRr9LSejRVbdYURPx28wNs8fxYfVW4X51aW1a5VxVI036pVg5POkZTaPrU+BoorSVUKAKeIuip3Ea2KCoF6JNrWRGaFmFyESpXbiL8ufGLijDQUFU73R+uPHBgRkPTzRdcg6hSnNRECPnx/PqK3/+30Rvbr7yRSY1oVgSTtTz0EJ8AlJ8kD2KLg/eOrIoaxdSrljeadxU+bz5WZBZ9kwvuJk1EBKf/PuOP9lUKwDmf5ir32R2avjKlNgrZmUlTVA1Ni3BfhyqO0ntz9/upVY0//X0NWYXkgLAYqcK3chOV/4VNck1UuRfV7hX3AZXK4UmvawpNizD83Vn1ZFXolc8lqt3/dOSp7PQ7vqqMuqXyJTunrAhN1511GU8DImXnVX6wJQ98tLkECuQ5AdV/Z2z4oiKallN/4CXPLZwL0kuBzfd2Y97FXxHyrAsigypJBmNaF8MYhqb18nrgovVTYPzJ2Tj8+KS0+EXNpuCo31kcfHRc+n1F6x9Rp1B1/YThqilAAQpQIN0C4bER8FjXESI0vaXTcnT6ZwhD0+nW5IEUoEBGBRiazqggj6cABShAAQpQgAIUoAAFKEABClAgWYGMhKYv+gaj6zJ5EHZiu+L4unliZaMrfiHouPSG7PwdqxbA8oHltM4pKjYeIqys3JxsTaXKs6ptxSl/TN/9SPaycmg7o1uvy6HpDktv4KpfiIKgXql8+OeryimSqIYFlfdT+WDV0LQIZT9d2EDr+C+DolBjhrxK7eD6LpjTzS3ZOWVFaFpTMF9MQgTu+9VzhqjsXdXVBmJN+t4yGppus9gLN5+HKhhVr5P2P1/HtScfZMxXp9SGy3/hYE3+mkLBv/Yri64aqparfoZFaNtnngeMMmFvdTk0rVqNVTgf/LYaqhS1TvaSP3DzHT5bnViFVTRtn3tNoekGpe2wdXhisEtTEw95iIc9lNuqT8ujdcX8yc4pK0LTQ1fdxcFb79XOW7KABfrXc5Yq12p7QETf7xlcPwVyQuDcwyD0+P3jt3CICvjiYSg2CuRVgS8Pf4dLL7wQ9Lg/okNLSsvsUr0AlvXX/r+x8qoF10UBfRaYcW4Jdjw4IBGMrT0MAZFBWHlzs/T71Pqj0aXMJ/rMw7VTgAIUoEAaBcJjIuCxPjE0vbnj7+i841OGptNoyO4UoEDmCTA0nXmWHIkCFKAABShAAQpQgAIUoAAFKEABDQIZCU1vvfwaozbJQ24/9nDDgHou0pm2XXkDMb5y++6TYhjVMvmvjq427RJeh0TLjvNf1FCtCrCmsNy2EZVQ380uU/Zal0PTladchAi8JrVaJWzxfQpVnUVfUUlWuTJv7zpOEFWwVZtqaLqwvRku/1Bbq7vYT7Gvyi2nQtP+gVGoN/syYkXZ4mSaCE43LmOHFhXyo5qrDTIhZ5sp12V2DpLR0LTqdSiC6ffn1FMsQfV98caLxQ2TXeKOq2/w9Qb5fUVTdcXAsBhUUKmmXLGwNf4dUy1TCHU5NK3p3iwCzSmFyb1fhWPiPz4yvzsz68LeykT2mqbQtAgbz++RWDFfU9M0p5wKTe+98Q5frvkYDtc0XxHAF1WwRSXbFuUdIALVbBSgQPYLnPQORF+lbz3ZMqwSGpbJnH8HZv9qeEYKpCxw+503+u/9Wur49u54JMQlfgtFpSLWODw6c/6Nk/Is2IMCFMgtAos8V2Ddne3SdIZW7oN8ZrZYfHmF9PvnVfrhq+qDc8tUOQ8KUIACFMjlAh9D0+bY1HEZuuwYinP9d8PKxDKXz5zTowAF8qIAQ9N5cVe5JgpQgAIUoAAFKEABClCAAhSgQC4SyEhoesGhJ1jy71PZakQVaVFNWrT5B5/gpyPy93/qUwY9azklK6BanVZ0FpWmRcVp5ZaeKrVpodfV0LTIAhcZcyYtS9XaV1RqFBUbVZtqaLp6MRvsG1lV6zi5KTQtJqkpeJscmJWZEUTIe3jTInBQCYhmCnQuHSQjoemEBKCwynUoKkiLStKiaXrf0doEN2fUTVZDU4V7UWVaVJtWbpcfh6DTL2mrdJ+WbdDl0PSPB/zw89FnaVmu1r7HxlWHu4uV7H1NoWlNwXblg3JTaFrMS9NDOcmBuTqYY1jTIlIlalapz5RLi4NQIEWBo3cDMPCvO4p+4uEPUdWejQJ5WeCXqyvx981NsIishycPWyqWamlqhCtTasPO0jgvL59rowAFNAgs91qH5dfXSu90LtMGtV2qYeKpudLv7Uo1x+xG39ONAhSgAAUokKJAWEw46q/vBAtjc2zo8Cu67vyMoekU1diBAhTIKgGGprNKluNSgAIUoAAFKEABClCAAhSgAAUoIAlkJDT91Xpv7Lz2Ria5fURleLjlk14TFUlXn3spe3/lkPJoUyl/svo9f7+Fsw+DZH1OfFcDZZ3llS085lyB37sIWT8RuBTBy8xoaQ1Nn/cJRvffbspOPatLKXzasJDsNdWQj3hTVHMWVZ1FW3r0GeYd8FMc06eOMxb1SqzQmppzhEfHwe3785lBgJblHbDmswpqY6mGpmsUt8Xe/1XRes7cFpoWE731PBQTd/jiql9Iqq1EdVnxYEDrislfw6keMJd3zEhoWtOeK1d6DouKQ+kJ8utUBE8vTq6VrMod/zC0XHRN1kdU+93whTzcf/j2ewxZeVfWr3vNgljaVx6uTu8WpCc03WqRF277hypOKa6nx/Prq01h8k5frDzzQva6CEIVskusJtnl15u49ChY8f6KQe7oUMVR+n3ufj/8ckweiD79fQ24Ffx4/5y0wxerzsrHT6/D2Qk11aosawpNj21TDKNbaf+WgdwWmhYeu73eYta+xxDV6VPbRNX9zV9WQqmCrDydWjP2o0B6BA7eeo+hqz7e45X//Zme8XgMBXRFQFT8exz8FCZvv4D/K2fZtFPzcKqurJPzpAAF0iaw/s4OLPT8XTqoqasH+pTvjC8OfSf9Xs2pIla1XZK2AdmbAhSgAAX0TkA5NL2+wy/otvNzhqb17irggimQewQYms49e8GZUIACFKAABShAAQpQgAIUoAAF8qRAekPTokps9emXIIKRyu3+7HqwtUiscLbs+HPM3vdY9v687m4Y6OGSrGXT+Vfh/Spc1ufBXA9YmxnJXuv3x22cuB8oe23XN1VQu4RtpuxVWkPTh269x6dKAR4xiZwITYvzqs49vSBdqhfEsv7qIdO8EJpOMvF7H4k9Xm9x4OY73Hz+MdCqzUxUkT05voZaUDS9xrn5uIyEpjVV8x5QzwU/9nBTLFn1OhIVvR/O9UiWRHzmxWdfuamOK94T9xBxL1FuVV1tcGCU9oroadmL9ISm6866jKcBkYrT5FRoWgSyRTA7M5qmbwHIK6HpJB/xYMWe6+8ggvjK+6fNTwSnz0+sBRMjg8wg5hgUoICKwN7rb/Hl2vuKV7d/VRkepRIf2GOjQF4WOP7kHEYfn4a4mHx4f38kDIyikBCX+ECVaO0qO+LPwe55mYBrowAFkhHY/fAwpp5dKPWo7lQJkz1GSlVCRXOycsThnpvS7HfE7zS23NuDIZV6oX6R5B9uTfPgPIACFKAABXKVgHJoel37pei+6wuc7b8L1ibybxfLVZPmZChAgTwrwNB0nt1aLowCFKAABShAAQpQgAIUoAAFKJA7BNIbmt56+TVGbXogW4STrSlEgC6piRDqZ6vvyfp807woJrQrnuziy028gJDIWEUfEVJ9urCB2jEz9jzG8pPPZa+LisyiMnNmtLSGpjUFEXMqNN1i4TWI4GJSE+HM71Nw12RWuYg16mkIIuVkaLprjYL4tV/mVAtWXXN0bDw8H4fg5P1AKZB/7+VHQ+W+jcvaY9OX8srGmXHN5bYx0huajotPQM0ZnmoPVYgAvgjiJzXVELF4/fmihjBMJmu64eIrjNv6UEY1pWMJDGtSRPZaTFwCio07K3vN3soEd2bWzRTm9ISmVe8pORWaFpX8RUV/5SY+V5WKWKfZZkh9F5gaG8qOy+nQtKYgd5oXpuWAoPBY6ZsQxP3hpHcgXgZprkI9rVNJfNG4cGadluNQgAL/Cag+kMPANC8NfRL44cx87PU5AufYHrh5zx2mNj6I/vDxYTRjIwOcm1ATRR3M9YmFa6UABZQEjj05izHHp0uvlLQrhtVtl6DRxq6KHif7bIedeeofNFrutQ7Lr6+Fq21hrO/wK2xN0/6/F7hBFKAABSigGwKhMWFosL4zLI0tsKb9T+ix60uc7bcL1qYMTevGDnKWFMhbAgxN56395GooQAEKUIACFKAABShAAQpQgAK5TiA9oWkRRqw67RICw2Jk6+lV2wlLepdRvHbvRRiaL7wm61O9mA32jdRe6dU/MAq1ZnrKjinuaIHzE2uq2W3xfI1vN8uD25lZSVY14Fi+kBWOjq2udQ9n7n2M30/IQ9w5FZr+eoM3RLBIud2YXgcFbEwz5RrMrtC0puBrStdQpizwv0FeBUdj1r7HapaZGb7NzPlm9ljpDU2vu/AS47f5qE3n+vQ6KKh0DfZafgtnHgTJ+u3+pgpqJVMtfvi6+9jt9VZ2zKpPy6N1xfxq56vww0W1+9TOryujTsnUBwW0mWoKTW8dXgkNSttpPCQ2PgHic6Pccio0/eZDNKpOvSSbS+86Tljc6+P9OyPXUnaGpqfvfoQVp/zTdA1lZG2qx954ForRmx+oPWDRsWoBLB9YLjNPxbEooPcCWy6/xrdKD+wxMK33l4TeAbTa0gdvwt/B7v0UPHgBWDsdR+jrZpKDoUkI4mNsMbNLKQxtWEjvbLhgClDgo4DnSy98ceg76QVnq4I41HMDGm/siuCoD9JrGzssQ3nH1P27/2HgY/Tf+w2i4qLQrWw7/OAxitQUoAAFKJBHBUKjw9BgQ2dYmlhgVdsl6LV7GEPTeXSvuSwK6IIAQ9O6sEucIwUoQAEKUIACFKAABShAAQpQQIcF0hqaFpV4x259iO1X5IFcQXDiuxoo62yp0AiLikPpCefVdM58XxOlClpoVJu66xFEGFG5NS1njw1fqFf1vfrkAzr8fF1tnD8GuaN9FccM70q1aZdklXKtzIzwcK6HxnETEoDq0+X9RcecCk0vO/4cs/c9ls01M0N82RWaFgtQrTwu9uH+7HowSq4ccYZ3/+MA4pov/t05tRFF9XNRBT0vt/SEpq/4haDHb7cQFRuf4ud40g5frDr7QtavVYX8WD20vEbW4IhYuE+6oPae6r0nqUOHpTdw1S9E1t/VwRznJ9VKtpp1avZ017W3GLH+vqzr7K6lMKSB5qDSP1ff4JsN3rL+ORWaFpNQ/QyLa/nw6GpwL5TxCkLZGZrWVHlc0303NXua3j7nfYLR/bebssPdXaxwbJz2h2zSey4eRwF9FVD9rDMwra9Xgv6u++abuxi4fyRiwosi0HcIbCwAY5fVCHw0WEJJqjrt4WaD7SO0P6Cqv4JcOQX0S+Duuwfou/cradH5zGxxtPdm9N49HL5BT6TXfmkxCw2LfvyWsOR0fr7yF1bd2iJ1mdPoe7Qt1Vy/MLlaClCAAnoioByaXtl2sfT3xpl+O2HDbxnQkyuAy6RA7hJgaDp37QdnQwEKUIACFKAABShAAQpQgAIUyHMCaQlNez39gE9X3pUFiZNAtFX/bTr/Krxfhcvc6rvZYeOXFWFiJA+cin4tF16DqMiq3OZ0c8Pg+i5q9iLMWnnKJYRExsreE6HadZ9VQN1SGasm223ZTVzwDZaNvWZoBbSs4KA2lz3X32LYWnmAUnTKqdC075sINJx3RW2eAz1cMK/7x6/xTu8FnZ2h6TaLvXDzeahsqpPbl8CIZkXSO/00HaepKq8ImIrQdF5vaQlNi6rgs/Y+VnvoIclIUwXpU96B6LPithrjjq8qq31+xYMJozZ5Y5vKAxu25sa4PauuxgC7CGSLYLZq61GzIOZ2d4OlqVG6t/DW81C0XuwlO75kAQuc/r6mWiBb2DScewVPAyJl/XMyNC3uV+K+pdzEfI6MrQa3gh8ffkkPUHaGpi/6BqPrMnlgWfwdcH1aHYg/s6Npuv83LGOHLcMqZcfpeQ4K5HmB1edeYuI/H7+9gIHpPL/lXKAGgVW3NuPnK3/D9sMA+PiVQKvKJrgS8QsCHg6Teru6PsDTp4mVY8U344hvyGGjAAX0W+BZiD86/JP4YIWZkSn2dV+LSafnwfNl4oPnsxqOR3u3FikiiQr3ffaMwPuIQJSyK4aVbZcgn5lNisexAwUoQAEK6JbAh+gwNPyv0vTfnyyS7v0MTevWHnK2FMhLAgxN56Xd5FooQAEKUIACFKAABShAAQpQgAK5UEA1NG1vZYK1n1WQZhoaGYv7r8Jx1z8U916G47a/PLiatBwRID03sSaKOpirrfCcT5BUdVa1iZD1ol5lUMbJEhExcTh5P1AKHasGph2tTXBtWh2tFX2P3AnAoL/vaJTtVdsJg+sXQmknC7VwZGBYDJ4GRME/MBKtK+bXWLVYBHREUEe5iVDhwl6l0aV6QSkYGRIRi7XnX2LOfj+NcxBfjz2qpSvyW5so3j96NwAD/5LPeXGvMuhdx0nqs/ToM8w78HG8PnWcsahXaek9TRVNtVVVFWOIsVSbk60pRrd2RbcaBTWGRl+HROPh63BUc7XRGjrMztD0/zZ6a6xs3q+uM75t5YpCdmayJYZHx+F5YBTyW5nI3JM6iQrl916EQQRcSziawymfmcaqwy+DonDSOxDTdz9WC+YXd7TA+Yk1c+EnOnOnpCk0Pa5NMTQqa4+EhAQ8fR8JEZC99zIMV/0+qDklzaZ/PWfM75F4Dau2Vou8NN5bxDXfrrIjbC2M4fcuAjP3PsbBW+/Vjhf9xGdEUxNBa/HwwKO3EWpvi3vdnK6lULO4rXQNGSg9wyFCzv5BUXj2PlK6hjQFj7RV0m/mbo8FPUrDxc4M4vkPca19vuaetAbVJu6dR8dVRwlHC9lDJJN3+mLlGXkF7itTaiuu9S6/3sSlRx8f6FgxyB0d/quuP3e/H345Jv/cn/6+hloQWtwDq0/3VKsILuYoqtKPbFkU5ZytZC7iPVFB3Od1BBKQgIqFrTW6Z2do+u2HaFSZekltHsJ/dpdSEPthamyoeF9cE+JBiIDQGK1VtffeeIfImHjFPcLoaWXsAAAgAElEQVTB6uP9O2kgMc79V2E4cPM9Fh1OrNin3MTnRNyf2ChAgYwJ/HXaH1N2PVIMwsB0xjx5tO4KDNj3DW69eQA8m4w3wcDc3nZYcG0uAh4MlxbVqU4EDt55iejQkhjdujDGti6pu4vlzClAgUwTCIwMRttt/RERm/jw5vbOf2DF9fU44nda+n1cneHoV75riudbcvkPrLm9Teo3sGIPjK71RYrHsAMFKEABCuiWwIfoUDTc0AVWJpb4s80C6RsLTvfbCVtWmtatjeRsKZBHBBiaziMbyWVQgAIUoAAFKEABClCAAhSgAAVyq4BqaDo98/xjkDva/xfY03R8h6U3cNUvJD1D45d+ZaVwb3Kt1/JbOPMgKNk+ouKoCM6FR8WphQQvTq4FVw2Bb8/HIej8yw2t44oAtQgQJjURwhRBRE1NhCO3Dq8kVc/NrtC0CH7WnX0ZIvyrrYk1iBC1oaGBFJIPCo9VBNfFfBuUttN4aHaGpu+/DEOzBddSvH5ExWERmE4K3o9pXQxjWquHFsdtfYgNF1/JxhP7I6oOmxobICImHlEx8WoBfuUDhjQohNldS6U4J13voCk0ndY1lXW2xL9jqqtVlk8a54pfCDou1f45S+584tq9OrWOxtB70nGaKkKrjin2387SWOPef1IpP/4eUl7jNNr/fB3XnnzQ+J7q/UF0Su4eIT5r4jMnWnaEpsV5tl5+jVGbHiS7pWLO9v/ZiIdERFhcNBEkF5UsNbXsDE2L8w/66w6O3A3Qug6xv6IZGRrI7tkvFjfUeEytmZ7wD5TfN8V+WpoZQXxBwodI9b9HVAfaNqISxLcqsFGAAukX+P3Ec+mBmaTGwHT6LXmkbgvceeeNfnu/RlSwO4Kf9kDlItZYPtQRHTZ9h/cPRkiLm9CuCNbe2Qp/Pw+4FojFxQlNdXvRnD0FKJBpAjHxsWizta9UKVq0vz5ZhEOPjmO7937p9y+q9seIaoOSPd+DgEfouftLRZ8/2sxHbZdqmTZHDkQBClCAAjkvoByaFvd58e9PhqZzfl84AwroqwBD0/q681w3BShAAQpQgAIUoAAFKEABClAgmwQyEpoWIbL1X1RIMRgmAnRtFnslG0LVtNyqrjbYP7KqWqVT1b4iFNxikZfWwHJKlFuGVULDMprDbSmF8ZTHFiHaSTt8tZ5u3ecV0NzdIdtC02Iioqpy92U3NVaTTcllTjc3DK7vorFbdoamxQR+2OmLv1Uq76Y0/641CuLXfmXVuonK56ICenqbuO69ptWRQrZ5vWU0NN2mUn4sH1BOVulXk5moMr/n+ts0c67/vKJUSTil9uMBP/ysoep6SseJ991drHBsnOZwcGoD/WIcUfm4cVk7bL70WuNp3QpaQlSEFi27QtPiXJ+vvof9N9+lhkLWRzyI8nCuh8bjsjs0LSpH15zumea/Y8TnWATvVZvq/S2tONruPWkdh/0poM8ComK+qJyf1BiY1uergWtf7rUWy6+vg/G7wXjx0hXfty2OvvWt0HTNCLx/8JUENK1TSQQaH8fSXc5IiDPHluEV0LC0A/EoQAEKKAQ6/DMIz0ISv81mUbOpEA9krLy5Wfq9l3tHTKj7TbJak0//iH2+R6U+TVw98FPz6dSlAAUoQIE8JBASHYpG/1WaXtH6R/Tf9w1O990BWzObPLRKLoUCFNAVAYamdWWnOE8KUIACFKAABShAAQpQgAIUoICOCqQ3ND3QwwXftnLVGDjTRCGqdvb/8za8X4WnSmp40yKY2K64VBk0NS0iOh7zDvjhz9P+qeku6zOvuxvEejQ1Echuv/RGstWaxXEiHPrnIHcUHXtW6/lzIjQtJhMcEYupux5JVWXT0j5tWAizumiuppzdoWmxv33/uI1Lj4JTvQQRuj8wqqpaf01VZFM7qKO1CXZ9UwUlC1ik9hCd7pfe0HRxRwtMbl8cbSs7pnr94rMrrtPUNBF03fhFRbgXskpNd6nP5cchGLH+vloF4ZQGSC4cLI5devSZdO9JrolKxwe+rYpd197itxPPNXbNqdC0mIyofj9y04M0P3jiO68+LEwN1daT3aFpMYF9N95hxLr7aQpOi89y7RK2svmLatrlJl1I6bLQ+n6rCvmx8tPyyVY/T/fgPJACeiKw+N+nWHjoiWK1DEzrycZzmVoFRvw7AWef3Eeo77eIjDbAqfE1UCS/IWr/PRDvvRNDjtM7l0TLKvFo/tNhRASXReNK0dg0pDlVKUABCsgEeu8ejvsBPtJrP3h8i9CYMCy5/If0e5uSTTGv8UStYhdeXMXww98r3l/e+kfULaT54VKyU4ACFKCA7gmERH1Ao41dYW1iid9bz8OAff/Dqb47kI+had3bTM6YAnlAgKHpPLCJXAIFKEABClCAAhSgAAUoQAEKUCA3C6QUmhZhP0tTIzhYm0jhMlEptWk5h3RV2Y2LT8DcA37Yee2txhCyOFdpJ0upUpq2ys8pWYrKr1N3P8Jt/7BUBQDFOcX5REBYW4uJS8CMPY+0Vjoe1qQIJrQrDhMjAyRXoTQpNO35OASdf7khO93iXmXQu46T9Nqqsy9kFav71HHGol6lpfduPQ9F68VesmNFsDm5+Sd1vvEsFLP2Psbdl6mzEfMR89LUVNcpqv2Kqr/a2uuQaFSbdkn29jfNi0puaWl7b7zDL0ef4bZ/aIqHFbY3w+Ufaqv185hzBX7vIlI8PqmDuEZE+Ld/PWd4uNnpVRgyNaFpUXlbVN0WQelGZezQpJw9qrmmrwrNHf8wjN/+EOLPqNh4tT0SofUWFRykML+4L6W1xcYnYNnx59ji+RrPAyJTFbC1tzLBnZl1kz2V19MP+GLNPY2BbBGGXtK7NGoUt8Vvx59j1r7HGsdSDk0vOPQES/59Kut3ZUptFLIzk15TrYC/YpA7OlRJDKj/euwZ5ihVZxWviQrWYvzkmrjP/XTkqXR/To2N+Fycn1QLRewT56TcNIWmZ3Quic8aFdY6hW1X3kD8faTcto+oDA+3fKne5vehMZLvv3cCUnX/V3ZLOsnTgEg0nnc1TdX5bc2Npfu3uFeXdU7eOdWLYUcK6KnAjwf98PORZ4rVMzCtpxcCl60QCI0JR+stvfHudXl88G+HlhUcsGZoBen9qn/0xpv7iZWmZ3Quhc8aFUKPtWtx7noJmJmH4dbUZrA2U/9GBfJSgAL6LfDpgdG49vqWhPC/GkPhYGGHaWcXSb97FK6J31rN1Qo06tgUnHya+IBhSiFr/Vbm6ilAAQronoAiNG1qhd9azsHA/SMZmta9beSMKZBnBBiazjNbyYVQgAIUoAAFKEABClCAAhSgAAUooCwQHRuPey/D8SwgUnq5clFruDqYZyqSOIfv2wg8fB0OEdiOjk2AuYmh9F9BW1MUz28OEYhMbYtPAB6/jZBCx6KJAGf5QlbIZ2GsGOLeizDAABBBUnEe0//+FL+LUHVuaSIg+fhdBHxeh0vhQGEjKsaKtRRztEBRe7NUV/nOiTWJ+d989gEvgqMRGhkrWYuMrZWpIfJbm0gB3oI2pjDQQp60flFJPCwqTjIQ1ayFgaGBARxtTJDfygSONqZwsDLRq6B0TuynpnO+Co6WHhKIiImTwsLuLlYQVZ8zs4kwv7g/BITF4ENknHQdWZgYSucplt8cRRzMIcLBqW2iqrt4cEOMK64j8RCIcohWnOdFUJR0fzAzMUz8M+k/E3HtpfZMWd9PBObF/eFtaAyCw2Olz4awKWxvjuKO5rL7XtbPJu1nkK4f/1CIytHCVXy+xb6K/0TQu1h+C41VspPOJO4LPm8i8Co4Sjo2Oi5euk+IgLS4l4t7g/g7QNxvlP8OSPtMeQQFKJAkIB7sUq7G/89XlVGvVOofnKAkBfKiwHn/KxCVpoP8+iL6gxvm9ygtPcgnmsfqwfC7OVT6eWaXUhjasBC23fLEyFVR0mv9moVgQft2eZGFa6IABTIo8PWRSTj73FMaZWDFHqjmVBHfHpsq/e6evzQ2dfxN4xnEMeLYpLa63U+oWjDxQQ42ClCAAhTQbYHgqA9oLCpNm1phWcvZGLR/FE71/Qf5zOTf0KXbq+TsKUABXRFgaFpXdorzpAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUSLPA1F2P8Odpf8VxO76qjLoMTKfZkQfkPYHlXmvxy4VjCPT5THrw59rUOrAxT3yIrOWGYbhztZ/086yupfBpg8Rvzik/YyeCghzh4PgIF8Z2g42pdd6D4YooQIEMC4w/OQuHH5+SxulRrj0+KdkMogq1aM5WBXCo50aN5xh7YgaO+p2R3utcug2mNRiT4blwAApQgAIUyHmB4KgQNN7YTQpN/9piFgYf+BYn+/4DO4amc35zOAMK6KEAQ9N6uOlcMgUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhTQB4GJ//hg9bmXiqXu/LoK6pRkNTN92HuuMWWB4Ye/x5Fr9gh/64EetZzwc58yioM6b/0Wnhe7Sr/P7loKQ/4LTQ9adxhHvCxhYBiLb7u+xViPnimfiD0oQAG9FJh+bjF2Pjgorb29W0t8VrkPOu/4VPrd1MgUFwfshaGBoczm4otrGHZ4vOK1LZ2Wo6xDKb3046IpQAEK5CWBpNC0jakVfm4xU3qQhqHpvLTDXAsFdEuAoWnd2i/OlgIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQIBUCY7c+xMaLrxQ9d39TBbVKMDCdCjp20QOB0JhwNNvQFy/vD0VctB3WflYBLco7KFbed9cEnDzdVvp9Tjc3DK7vIv18/vELdP/FV/rZpdgZHP1yBOzN7fRAjEukAAXSI7Dg0u/YcHeHdGiL4g3xg8coqdJoUjvaewscLT7ee8Tr35+cjUOPT0pd+pXvinF1hqfn1DyGAhSgAAVykUBQVAiabEz8lpKfmk/H0INjcLLPdtiZ58tFs+RUKEABfRFgaFpfdprrpAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAJ6IjBy0wNsu/xasdo9/6uCmsUZmNaT7ecyUyFw3v8KPt26ESHPusDFzhRXp9SRHTVw9yQcPdVGem1uNzcM+i80LX6vv3AfHr/IB1NrP/zYNx96leuYijOyCwUooK8Cy66txp83NkjLr1+kFpa1nIP66zshLCZcem1b5z9Q2r6Egufqq5tSmE40Ea4T1aYLWTvpKx/XTQEKUCBPCARFBqPJpu7SfX1J82n47OBYnOizHfYMTeeJ/eUiKKBrAgxN69qOcb4UoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAJaBUasu49dXm8V7+8dWRU1itlQjAIUUBL43WsNFuwNQ2RQJQxtWAgzu5SS+QzcMxFHT34ivTavuxsGeiRWmhbtx6NX8POBCOlnj9qnsb33JNpSgAJ6LHDl1Q0s91qHms5VUN25Iqo7VYKxobFMZM3tbVhy+Q/ptRrOlfH3J4vQfvtAPP/wUnrtzzYLUMulquKYyad/xD7fo9Lvw6oNwLCqA/VYmEunAAUooPsCgZHBaLqpO2xNrbGo2VR8fmgcQ9O6v61cAQV0VoChaZ3dOk6cAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClBAWeDz1few/+Y7xUv7R1VFNVcGpnmVUEBVYPDeiTh6ugni4yyw/vOKaOZuL+syaM9EHPkvNP1jdzcMUApNh0XFofL044iINIel4yVs/rSlFJZkowAF9FNg+fV1WO61VrH4Apb5UadQdTR19ZD+MzQwlN7b8eAAZpxbIv1c3rEMVrVdgs8PjsXNt/ek135uPgONXetJP994cweD9o+SfhZVpkW1aVGdlI0CFKAABXRTQDk0vbDZFHxx6Duc6LMN9uZ2urkgzpoCFNBpAYamdXr7OHkKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEhMPjvu/j3znsFxoFRVVGVgWleHBRQEwiNCUftZVMQ9LQTnOwArykN1foM2jsBR060lV6f36M0+tdzlvX5bP15HLgWB0PjcHzTzQfj63xFaQpQQI8F9vocwbEnZ3Dy6QWZgqttYSk43cTVA9WcKuLfx6cw/uQsJAAoaeeK5a1/xNwLv+DE0/PScXMaT0Dbks2kn6edXYRdDw9JP4+rMxz9ynfVY2EunQIUoIBuCyhC02Y2WNBkMr48PB7H+2yDA0PTur2xnD0FdFSAoWkd3ThOmwIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQIFGg3x+3ceJ+oILj4LfVUKUoK1Ly+qCAJoFzzy9jwMrLiAyqhAEeBfBj93Jq3Qbv/R7/nmgnvb6gZ2n0qysPTV98FIyuv96U3i9a4hIufTOW2BSgAAXwMPAxjvmdkULQ3gG+MpHaLlXRpmRT2Jvlw+Qz8xEWEw4XayepwvR2733Yen+v1H+yxyh0L9sOd955o9/er6XXyjqUkqpNs1GAAhSggG4KBEQGodmmHrA1s8H8JpMw7PD3DE3r5lZy1hTIEwIMTeeJbeQiKEABClCAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUoIB+CvT4/RbOPQxSLP7w6GqoVISBaf28Grjq1AjMv7AGS3e4ID7OAus/r4hm7vZqhw3ZNx6Hj7eXXl/YszT6qoSmxeudl12Gp28kjM1f4emcHqk5NftQgAJ6JHDe/wpOPj0vBajfhn/8JggRlK7hVAnn/C9DVB61N8+H+U0m4/qbO1h2bbUkNLrWlxhYsTvmXFiqCFPPavgd2ru11CNBLpUCFKBA3hFICk3nM7PBvMYTMfzfCTjeeyscLNT/HZp3Vs2VUIACuVWAoencujOcFwUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgQLIC3X+7ifM+wYo+/46phoqFGZjmZUOB5ARa/b0It+/UhrMdcG1KQ41dh+wfj8PHEkPTi3qVRp868krT4vUdV9/g6w3eUp/ln1ujo3s1wlOAAhRQEwiNCcdRv9PY73sMl19el71vaWKB8JgImBubScHp9xFBmH5ukdRnWLUBaF2iCQbs+x9Co8NQp1B1rGj9I4UpQAEKUEAHBQIigtBscw/kM7PF3MbfY8S/E3Gs91bkZ2haB3eTU6aA7gswNK37e8gVUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAG9E1ANTB8ZWx0VClnpnQMXTIG0CPgGPUHLpfsRGVQJQxq4YHZXN42Hf7r/Oxw61kF6b3GvMuhdx0ljv6YLLsH7ZTQGerhgXnfNY6VlfuxLAQrkbYGzzz2l8PTBR8c1LvTHJpNgZWKJr49Mkt7vV74LLE0s8eeNDdLvy1rOQf0itfI2EldHAQpQIA8KvI8IRPPNPaXQ9OxG46X7PEPTeXCjuSQK6IgAQ9M6slGcJgUoQAEKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgQKKAamD62LjqcHdhYJrXBwVSEoiMiYf7pHOIigXWf14Rzdw1fyX60APjcPBoR2m4Jb3LoFdtzaHpP0/7Y+quR1K/F4s1V61OaU58nwIU0D8B7wBf7Pc9KgWoRZBOufUt3wVtSzZD/33fSC+3LtEYd949wPMPL9GmZFPMazxR/8C4YgpQgAI6LpAUmrYzs8XMRt/hmyOTGZrW8T3l9CmgywIMTevy7nHuFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUEDPBFQD08fHVUc5Bqb17CrgctMrcN4nWHrooISjBc5NrKl1mE8PjMWho52k93/qUwY9a2kOTQeFx6LFwmt4ERSF7SMqw8MtX3qnxuMoQAE9FBAhun2+R7DP5ygeBj5WCLjZF0evcp0w+8LP0mvid59AP+nnjR2WobxjGT3U4pIpQAEK6K6Acmh6RsNx+N/RH3C09xY4Wjjo7qI4cwpQQGcFGJrW2a3jxClAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKCAfgkoB6YNDIDj42qgrLOlfiFwtRTIgIDv2wiM3/oQLSrkx7AmhbWONOTAtzh8tKv0fnKhafH+3hvv8OBVGMa0LpaBmfFQClBAnwUSEhKw1/cIfr7yl6zydL1CNXDhxVWJxsrEEmEx4ejj3hnj636lz1xcOwUoQAGdE3gXEYAWm3vBzjwfpjcYg5FHpzA0rXO7yAlTIO8IMDSdd/aSK6EABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKUIACeVZAOTBtbGiAY+Oqo7QTA9N5dsO5sBwVGLR/JI4c6wbAAD/3LYseNQvm6Hx4cgpQQH8EvjoyEeeeX9a6YGsTS2zptAKFbZz1B4UrpQAFKKDjAsqh6Wn1R2PUsak42msLHC1ZaVrHt5bTp4BOCjA0rZPbxklTgAIUoAAFKEABClCAAhSgAAUoQAEKUIACFKAABShAAf0RUA5Mmxkb4sjYanAryMC0/lwBXGl2CwzY9z8cO9ENSDDE0r5l0Z2h6ezeAp6PAnotsMhzBdbd2S4ZmBqZIjouWuYxoGJ3jKn1pV4bcfEUoAAFdEkgKTRtb54PU+p/i2+PTcORXptRwDK/Li2Dc6UABfKIAEPTeWQjuQwKUIACFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUokBcFlAPTFqaG+HdMdZQqYJEXl8o1USDXCPTf+zVOnOyOhAQj/NKvLLrVYKXp/7N3HvBRVUsY/9J775XQAgQIvQtKb9KbgCIoomDDJ3ZERUVRARsoKiCgSO+gIEqT3kPvBJJAIAnpvbzfuZvd7CabZJNsNlu+8eUle++cOTP/c+9mge/M1ZvFYSIkYCIEfji1DAtPL5eqberVEElZKbidHC29NjMzw6nxO02EBMskARIgAcMn8CA9Hj1XPQEhmn6/41T879+PKJo2/GVlBSRgsAQomjbYpWPiJEACJEACJEACJEACJEACJEACJEACJEACJEACJEACJEACJGDcBJQF0442FtjxegvU9qRg2rhXndXpA4ExW17EXkk0bYnvn2yAoS0pmtaHdWEOJGBqBJadW4O5x36Syu5ftwdOxZ7F3bT7MANwkqJpU7scWC8JkIABEygSTbtiesdX8Pq/MymaNuD1ZOokYOgEKJo29BVk/iRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRghASUBdMudpb487UWCPG0NcJKWRIJ6B+BJzZPxv59I1CQb4X5TzbEkJZe+pckMyIBEjAJAmsvb8MnB7+Wah0W2g/vd3rNJOpmkSRAAiRgTATkoml3W1e82+FlTNv9MXaO+gPe9p7GVCZrIQESMBACFE0byEIxTRIgARIgARIgARIgARIgARIgARIgARIgARIgARIgARIgARIwFQLKgmk3Bytsn9octTwomDaV9WedNU9g5KbncWD/SBTkW+PbsfUwvJVfzSfFDEiABEyWwPYb/+LdvZ9hWIP+eL/jVJPlwMJJgARIwFAJ3E+PQ69VoyFE0293eAlv7v6EomlDXUzmTQJGQICiaSNYRJZAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAiRAAsZCQFkw7elohW1TmyPInYJpY1lf1mEYBEZsnISDB0aiIM8GXz0RjDFtaxlG4sySBEjAaAmILqVe9h5GWx8LIwESIAFjJqAsmn6r/Yt4a8+n2DHyD/g4sNO0Ma87ayMBfSVA0bS+rgzzIgESIAESIAESIAESIAESIAESIAESIAESIAESIAESIAESIAETI6AsmPZxtsaWV5sj0M3GxCiwXBKoeQLDNkzE4YNCNG2HWSN8Mb5D/ZpPihmQAAmQAAmQAAmQAAkYJAGFaNrODW+2m4y398zCjpEr4OPgZZD1MGkSIAHDJkDRtGGvH7MnARIgARIgARIgARIgARIgARIgARIgARIgARIgARIgARIgAaMgoCyY9nO1wZZXmsHflYJpo1hcFmFwBIasfxbHDo1Efp49Phjmhuc7NTG4GpgwCZAACZAACZAACZCAfhCITYtD79Wj4WHnhmltX8A7ez+jaFo/loZZkIBJEqBo2iSXnUWTAAmQAAmQAAmQAAmQAAmQAAmQAAmQAAmQAAmQAAmQAAmQgP4QUBZMi87Sm15uBiGcppEACdQMgUHrJ+DE4VHIz3XAO4Md8HKXljWTCGclARIgARIgARIgARIweAKxaQ/Qe/UYSTT9etvn8e7ez/HXyBXwZadpg19bFkAChkiAomlDXDXmTAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAIkQAJGQkBZMF3LwxYbXmoGXxdrI6mOZZCAYRIYuG48Th4RomlHvPa4Bd7o1tEwC2HWJEACJEACJEACJEACNU5AWTT9vzbP4719QjT9O3wdvGs8NyZAAiRgegQomja9NWfFJEACJEACJEACJEACJEACJEACJEACJEACJEACJEACJEACRkbgfHQaVh+LxfmYVAS52WJEGx90rOei91UqC6Zre9lhw4vh8HamYFrvF44JGj2Bx9eOw+ljTyA/xwkv9MnBjF7djL5mFkgCJEACJEACJEACJFA9BO6lPUCf1WPgaeeOqa2fw/T9symarh7UjEoCJKABAYqmNYBEFxIgARIgARIgARIgARIgARIgARIgARIgARIgARIgARIgARLQRwLJGbmYs+M2ft4XXSK9VrVd8PnQumgc4KCPqUNZMF3P2x7rXmwKLycKpvVysZiUyRHot+ZJRBwfjfwcZ4zvkYxZ/fqbHAMWTAIkQAIkQAIkQAIkoB0C99Luo8/qsZJo+tXWE/H+/i/w58jf4cdO09oBzCgkQAIVIkDRdIVw0ZkESIAESIAESIAESIAESIAESIAESIAESIAESIAESIAESIAE9PGQ/tAAACAASURBVIOA6C49fvEFRD/MLDUhextLvNGvDp7v7KMfSRdmoSyYbuBrjzVTwuHpaKVXOTIZEjBlAkLUcv7EaOTluGBk1/v4esAwU8bB2kmABEiABEiABEiABKpAQFk0/UqrZzHjvy/x54jf4efoXYWoHEoCJEAClSNA0XTluHEUCZAACZAACZAACZAACZAACZAACZAACZAACZAACZAACZAACdQYgV/2RWPGxhvS/LbWlujS1B91/V2l19djEnHsSiwepmQp8uvb3AeLxoXWWL7KE4/56Rz2XHooHWrk74A1k5vC3YGCab1YHCZBAoUEeq0ajYunxiAv2xUDHonEwqFPkg0JkAAJkAAJkAAJkAAJVIrA3dT76LtmLDzt3fFSy2fw4X9f4c8Rv8HPUb8291aqOA4iARIwOAIUTRvckjFhEiABEiABEiABEiABEiABEiABEiABEiABEiABEqg8gaw8IDoViE4DUrOBzDwgPacAVvk5sEEOUjOykZyRg5SMHOTlFaC2lx3C/WwR7G4LR1uLyk/MkSRAAlohkJyRi6krr+Cvs/FSvM5NA9CrdTDsrC1LxD92ORY7T0QqxNMtQ1yw4rkwONuV9NVKchoEeWbxBfx1TpZ7kwBHrHqhCdwomNaAHF1IQLcEeq56Apck0bQberS/hGUjn9NtApyNBEiABEiABEiABEjAaAjcTY1F3zVPwsveA1NajMdHB+Zg+4jf4E/RtNGsMQshAUMiQNG0Ia0WcyUBEiABEiABEiABEiABEiABEiABEiABHRFITM/FzbgM3HyQIfsel4n7ydmwszaHvbWF9CX/WXSGDA90RHiQIxxtKKjU0RJxGhLQmEBUKnA4Mh0XYtJwPTYVN+6lICs7D5k5edL3rJxcZOfmaxTP380WDXzt0TLYCfbW5gj1tUfb2i5wophaI350IoGqEriTkIlnllzA+eg0qbv0oI510KZB2Z25MrJzsXL3FZy/JRMqN/RzwMaXwmtEOD15+SVsOvVAyqNZkCP+eL4pXO1rTsBd1fXgeBIwZgLdV47ElTNjkZfljk6tT2HNmFeMuVzWRgIkYMIEtlzbiR0390oEwjxDMaXF02XSWHBqKS7EXZF8hoT2Rfdaj5gwPZZOAiRAApoRiEmNRb9C0fTkFk9j5oG5FE1rho5eJEAC1UCAoulqgMqQJEACJEACJEACJEACJEACJEACJEACJGBIBISQ8vitZElQKf9Kz86rVAmN/R3QNNBR+moR7ITmwU6VisNBJEACVSNwLjYbi/67J214iLyfgtiH6VULWM7ono3dMWNgHdT1sqvWeRicBEyZgBBKD1sQAdFpWgimpwwMh7+Hg8ZI9p+Nxo7jt5GZnYtG/g7Y8KJuhdOvrbyCVUdjpXxb1nLCiklNakS4rTEwOpKAiRPo+sdwXIt4EnlZHmjV/BC2jHvTxImwfBIgAWMlcDPxNoZseFZR3qZhS1DLOVBtuZHJURi0boLi3Pohv6COay1jRcO6SIAESEBrBJRF0y80H4ePD87D9hHL4e/oq7U5GIgESIAENCVA0bSmpOhHAiRAAiRAAiRAAiRAAiRAAiRAAiRAAkZIICIqFX3mntJKZe3rueHwtYcqsbo3cseA5p54vJmn1J2aRgIkUP0ENp2Jx8xNN3A3MVPtZG6ONvB2s4ePqz38PBwk0WWAp6Na3/yCAmQpOlLnISMrF1eiHmLnidsl/IVo+oXHAqq/QM5AAiZIQFkwLe7Z8b3D4O5kW2ESMfFpWLA5QhJOh/k7YL2OhNNvr72GZQfvSvm2DnHG75OasEN9hVePA0hAtwQeXTEMN88+idwsTzRtuhc7JkzXbQKcjQRIgAR0SGDa7pnYdWu/NGPPkC74suv7amd/c88n2FnYlbpbrU6Y2+1DHWbJqUiABEjAcAnEpN5DvzVPwdveA5OaP4VPDn6NbSOWI4CiacNdVGZOAgZMgKJpA148pk4CJEACJEACJEACJEACJEACJEACJEAClSGQkZ2P7WfjsC0iDn+djS8zRPO6XniyR0PJJyktG3cepODO/RTceZAqfc/IzlUZ/2T3hmgc6ISz1+9j55m7iEvJls4HudtKwumhrbwhulHTSIAEtE9AbIL49UAMVh6RdXJVNl93e4TX9kTDYHcEe1e9A/x/52Jw9mYcrsckqcwzqVsIPnw8SPvFMSIJmDCB4oLpyQPDYWdtWWkiQji9cvdliO+B7rZYMiEMjQOq73fzh5tu4Ke90VK+7eq4YPlzjeFow41UlV5ADiQBHRHo8vsQ3Dr/FHIzvRDaeCf2PPuxjmbmNCRAAiSgewJ3UmIwYO3Tiom3DV+OACfV7qfyLqlypy3DlyLIyV/3yXJGEiABEjBAAtGp99BfEk174rlmY/HpoW+g7r3WAEtjyiRAAgZIgKJpA1w0pkwCJEACJEACJEACJEACJEACJEACJEAClSFwJyETK4/GYu3x+xA/CwvydECBmRmiHqQqQobVcseFyATp9VM9G6FZHU+10+XlFyDqQQquRifhavTDEuLJWj7OiIxNVhnrZm+JN/uF4OmOfpUpgWNIgARKIfDNrjuYvf2Wytm6/i5SF+kW9by1IpRWN/WXq09Ih22sLHD7for0s7uTDV7uGYLnH/HmepEACVSRgNjcNHXlFSRn5KKunwvG9wmrkmBano7Y9PTD5ghJOO1sZ4nnugTg9d7BVcy25PDPtt/Cd7vuSCc61nPBsomN+eQJrVNmQBKoHgKP/D4Yt88/idxMH4Q03IqDk2ZXz0SMSgIkQAJ6QmD6vtnYen2XlE2/ut0xq8vbKpmVd754GYmZSbieeBsedq4Idg6EuZmZxpXmF+QjKSsFDzMT8TBT/L1KAVxsnOFq4wwXW2dYmVd+A53GSdCRBEiABLRIIDrlHvqvlYmmJzYbg1mHvsW24csQ4MS/I9YiZoYiARLQkABF0xqCohsJkAAJkAAJkAAJkAAJkAAJkAAJkAAJGCqBK7HpUufZP47eQ1K6rDN0t2b+aNvIH0cu3cPu01HSMSGwbFXfG20b+uLUtQeSCFIIqDW1B0kZuBqViCvRD6XvWTl5pQ4d1tobb/UNQaCbjabh6UcCJFAKgfGLLmDneVnXeHHP9mpVS7r/xD1d3ZaZnYektCz4uNnj8p2HWL33itSVXlh4LVdMftQfg5p7VHcajE8CRklg1bFYvPbHFam21qE+eKJrqFbrFMLpX/+6gOt3ZR3jRbfpxRPCpKdDaMO+/CsS83belkI9Ut8VS59tDDtrc22EZgwSIAEdEOj420BEXRCdpn3hX389jk+ep4NZOQUJkAAJ1ByB++lx6LVqtCKBHSNXwMfBS3pd1jnljIXY+bsTi7HiwgZk5cn+XCS3dn4t8Omjb8PTrvS/Z9lz+yDWXt6G/6KOlgoi1L0OVg9aWHOgODMJkAAJVIJAdMpd9F87ThJNPxs+Gp8d/g5bhy9DIEXTlaDJISRAAlUlQNF0VQlyPAmQAAmQAAmQAAmQAAmQAAmQAAmQAAnoKYGzUalYceSeJJjOys2Hq4M1BnWsi2AfJxy5FIudxyOlzJuEeEhC6YoIpMsrOTUjBxdvJ0ji6ytRD9W6h3jYSl2nB7eQ/SMkjQRIoGIE9l5+iKl/XEFscjYszM0wrHN9tG3oU7EgWvYWIuo1+67izPUHisgDW/nix7H1tTwTw5GAcRNQFky3aeCDUY9pVzCtTG/l7is4fiVWOiS6Tq+bEi4JqKtiMzffxI97ZJuyHm3ghiXPhMHWioLpqjDlWBLQNYH2ywcg5tJTyM3wg2edlYh4ab6uU+B8JEACJKBzAp8c/AZrL2+V5h1cvw8+fOR16eePDszFhit/Sj8Pa9Af73ecWiK3tJx0jNv6Kq4nqj4BSNnRxsIayx//DkL4XNzEvGL+8uyx4A74uvvM8tx4ngRIgAT0ikBUyl08vnYcfBw8MaHpE/j88PcUTevVCjEZEjAtAhRNm9Z6s1oSIAESIAESIAESIAESIAESIAESIAETIHA/ORs/74vGz3ujkZ1XgFBfBzzTOwznY1IRGZuMfRHREoUW9bwksXT9ANdqpSI6WJ6+9kASUGdmyzpdK9sznf3xyZC61ZoDg5OAMRFIzcrDnB2RWLhHdi83CnaXOtA62FrpTZn/nYvBxgPXFfnU8XHE7xMboZaHdjrY6k2hTIQEqoHAL/uiMWPjDSnyI2E+GNy5+gTT8vQ37LuCAxdlwmlh80aHYlSbym3CeGftNSw9eFeK062Rm9S92tqSgulquFQYkgSqlUC7Zf1x7/I45GT4w7X2b7jwMruaVitwBicBEtALAgmZiei56gnk5cuenLXriVUwgxm6rxwpvbYwt8Dfo1bC3bbk36N8dfRH/HZ+naKOMI/6aOQZiuSsFPx7+4AiZmPPBvh9wPcq9d5JicGAtU+rHBPj67rVhqOVPczNzZGVm42EzIfoHNgOQ0L76gUvJkECJEACmhIoEk17YXyTkZh9ZD62Dl+KQCd/TUPQjwRIgAS0RoCiaa2hZCASIAESIAESIAESIAESIAESIAESIAESqHkCcrF01MMsKZkRHQIRFOCNQxfuSl/CRMdKIZau7eus04QTU7Nw+rpMPB0dl6oy94Bmnlj4dCOd5sPJSMAQCRy4lohZW2/h1O0UKf0B7Wvj0WaBelmK6FwrOtgq26IJYejb1EMv82VSJKAPBJQ7TPdo6oM+HatfMC2v+8jZSKw5eFuBoTLC6XfXXcevB2KkGD3C3CHueSsLM31AyxwMiMDuSw9xJTZdyriOlx16hrkbUPbGk2qbpX1x/+rTyEkPoGjaeJaVlZAACWhAQFn8PKrRQEk0vfLiJmnk2MZD8UbbySWixKUnoMeqUYrj0zu+iuENHle8jkyOwtANExXC6SX95qGFTxPF+W3X/8F7+z6XXgth9tZhS+HnWLkNbBqUSBcSIAES0DkBZdH0uCYj8OWRBdgyfCmCKJrW+VpwQhIgAYCiaV4FJEACJEACJEACJEACJEACJEACJEACJGAkBJ5fehFbzsRJ1Ux8NBDtmgRh04l72Hr4pnQsvI4nujUPQqCXY41XHHEjThJxX41OVORC4XSNLwsT0HMCC3ZHSYLp/IICKdPXhrVAgGfN389lYdt3NhqbD8o65srt9d618HrvYD2nzfRIQPcElAXT/Zv7oGs73Qmm5dXeiIzFgr+KNjtURDj90aYbWLhX1gG/dxMP/Dy+ESzNKZgufiXdT8nGqUjZxhe5daznCidbC91fdHo64/hFF7DzfLyUXedQV6x6oameZmrcabX6tQ/irgnRdCBcay/HhZd/Mu6CWR0JkAAJFBJIzk5F1z+GKwTOQsQsOk+L77tHr4Wzdck/g/1541+8s/czKUJLn6ZY3G9uCZ4LT/+GH04tlY6/0W4KxoYNUfisvrQZsw59p3i9Ycgi1Hbln5l4UZIACRgPAXlHfV8HLzzZeDi+OvoDtgxbiiBndpo2nlVmJSRgOAQomjactWKmJEACJEACJEACJEACJEACJEACJEACJFAqAblg2sHGAl+NaoA1pxPxb4Ss06O7ky26twxCu4a+ekdw/9lo/Hs6Cinp2VJuQjj97uO1UcvDVu9yZUIkUFMEIuMzMWvbLWw5/UCRwmfPdoKVpXlNpVSheXccj8TfJ4q614rByyc2Rnd2Dq0QRzobNwFlwfSwVrIO00myX406t8oIpz/bdgvf/XNHyrVfuCd+erohzM0omFa3eJ9suQmxCUbZvhxZH2Pb69/nNJ1ffIUTUjRdU+RV523xay8kXBuPnPQgiqb1Y0mYBQmQgA4JfH9yCX45s0Jlxonho/FSq2fUZvHj6eX48dQy6dy0ti9gRMMBJfyO3T2Nl/5+TzouOli/0/5lhc+FuCsYs+VFlTG1nAPRKbANOvi3QivfcNhb2emQAKciARIgAe0SuJMcgwHrnoYQTY9tPAxzjv5I0bR2ETMaCZBABQhQNF0BWHQlARIgARIgARIgARIgARIgARIgARIgAX0kIBdMh3jaYdmkJpi07AouRSVJqXYI80OPlkFwcbDRx9SlnHLz8rHtyE3sPysTefu6WOOjwXUlATWNBEydgOi0+eGmG7gVl6lA8b/hLeHv4WBQaDYdvK64x+WJr38xHO3ruhhUHUyWBKqDgLJgemQbH4zuGorrsl/jNWa378Ti2+1FHacXTwhDn6YeavOZsyMSc3bINkY83swTC8c1AvXSpS9dx1nHcSsuQ8WhWyM3/PZckxpbb32bmKJp/ViRsVtewqlT3ZCY6IV+nS+gTW17mIn/pBtcfJf+X3FMfk46a6Y4I15A5ZjSOUUMjfyV4sr9C99siuaW5Vc8B0U+0l4OuY9y7mpiS66l1KccQ+FTGFvlXCEjlZqL8iuZSzF/eS0q3OVs1fFQXZOi+EW++nF1GXcWBShAQUGB9HSYgoJ85EP8nC87Jl4VnpOOKZ9TvFbnny+Lp9Zf/TkpujRX8bnL8i88V5ircg1SLVJNsjoU56S8ZK/V+8sZFPIo7l+BWEXM1MWS11l07tlmoyXBcWUsPSdD6jadlSfbxWZjYS11mS5NuCy6TItu05pal6D2+LbHxyru847/jKVnV5caokdIZ7zaeiKCnNiVVVPO9CMBEtAfAkWiaW+MCRuCuccWYvOwXxHsHKA/STITEiABkyFA0bTJLDULJQESIAESIAESIAESIAESIAESIAESMEYCP++Lxgcbb6BNbWdM6xOCUT9ESGUKQWWv1rXQJES9wEkfWUQ9SMXX608pUlv4dCMKp/VxoZiTzgj8duge3lxzVWW+cT0bIbyOYW4oWLn7Co5fiVXUY2Fuhm2vNkd4UMnHW+sMMicigRom8NfZeDyz5IKUhRBMv9wvFEfu1XBShdPfjYnFnC0y4bSznSXWTQlH44CSGzbeXXcNvx64i9d718LrvfkY+bJW70FKNpp9cKSEi6W5GW5+0QnifZEGUDStH1dBZFIUpvx6F6ciM+FaZxmsHW7pR2LMokoEVAXmRSJthdxaLoYvLhhXFsMrhPOFAvASInJV4XwJoX0pInLl3ESRRWL3YgL3YvOp+AIy8a4kGFYS9iq9lomPVUW/ymLmIgFwSaFzCRG0igBY5i/+o+kHgda+zfBL368qnYwQ9C07t1Ya/1Tj4Xi97fOlxpq+bza2Xt+l8Vy9aj+KLx6bXsJfiAp/OL0Me24fhBBuq7PPHn0Xfet01XguOpIACZCAPhC4nRyNgevGw9fBG6PDBmPesZ8omtaHhWEOJGCiBCiaNtGFZ9kkQAIkQAIkQAIkQAIkQAIkQAIkQAKGT+B8dBqGLYhAckauSjGdmwagV+tg2FlbGlyRV6MTsXDrWUXeFE4b3BIyYS0R+HbXHXy+XVWcNKhjHYj725Dt523ncDnqoaKEut72WD25KfxcrA25LOZOApUioPx7PMzfAb8+F46jcZbIVP21XqnY2hpUXDj99+stEORuqxI+NjkbSem5CPW119a0RhtnxeF7mLZathmmQ10X3IrPxN3ELOn1uhfDpWM0iqb16RoYOj8Ch68n4dWBGXihQwcciDqK/6KPSd+TslL0KVXmQgIkQAJqCQys1wszO79RKTqLIv7AdycWS2NfavUMJoaPLjXOr2dX4evjv0jnG3rUw6iGA8ucs65rLYR7h5XpcyMxEoeiT2DfncM4crdog7noen1k3LZK1cRBJEACJFBTBOSiaT9Hb4xqOAhfH/8Zm4b9ilrsNF1TS8J5ScCkCVA0bdLLz+JJgARIgARIgARIgARIgARIgARIgAQMmcAziy/gr3PxihJ83R3Qp41hdZdWx//Y5Vis2iPrbCmMwmlDvkqZe2UI7L38EKMXnlMZ2q9dCLo1D6pMOL0acy8hDT9tO4fkdNljroW92C0I7z0eold5MhkSqG4CxQXT618Mx+kES0SlVvfMFY+vLJwWnaZFx2nReZpWcQJP/nwO/16UbRyZMbA2bj7IxPJDd6XXz3b2x8dD6qoN+jAtB5fupUvnXO0s0cjfAWlZedh+Nh6RcRlSN9bG/g7Sk0c8HK1KxDh+Kxk5ebLOpw187eHuUNJHPkh0w752v6izZesQZ1hZaL8Ddl5+AS7dTceJyGTcistE6xAn9Ahzh7WleYU6TWfn5uPi3XScjUpFZHwG6vvYo2mgo/RddPDWxAoKgJjELFy+l47L99IQn5YDfxcb1PW2Q6f6rhrH0WQuQ/MZ8n0EjtxIkjY43cnZg9lH5htaCQabr7mZuXRvm4v/xHf5azNxRP666JzwkbxL+MqOmYlxhefEeNlrJf/CmCq+UrzCPErxlZ1XzkmWmzSXyFQ+Z+Ex5RwVc8EMFuYWsJB8LGBhbg4L8V28Ljwufy38RHzpdaGf8mvxs+j2LOs0XfhV+LPimDhb2BFa8lX6WeoTXfha3fGiY1I09b5ljJd6XCvmK5aHUjy1OSjnrPAtv5aiuovxUM5Dz2qRGKlZl4rUIvfVlWj6v6ijeOnv96T3HEdrB+wYuQIOVtrbUHYk5iSe3/GW4j1t16hV8LR3N9j3OCZOAiRgegSURdMjGw7EN8d/waZhS1DLOdD0YLBiEiCBGidA0XSNLwETIAESIAESIAESIAESIAESIAESIAESIIGKE1h1NBavrSwSFtfxc8GYbg3g6mhT8WB6OGL5rks4c/2BIjMKp/VwkZhStRA4fScF/eadVondq3Ut9GoVXC3z1UTQU9ce4Pd/LimmtrUyx/bXmqOhr0NNpMM5SUDnBO4kZKLnnFPSkyJEh2khmL6eaomLCTpPReMJ70bHYs5W2eeOjvVcsHZKuMZj6SgjIETCtd88gNx8mXh571utpE7TT/9yXnod4GaDY++3VYtre0QcJv56UToX4mmHH55qiAHfnFbEkg8SIuF5o0MxrJW3Ik5Gdj4avHtQ4ftEOx/MHRVa6rK8vfYalh2UCblFvOuzO2ldNH39fgaGfH8Gcak5KnmI+WYNq4ddFxKw87xsY2DnUFeseqGp2nyFAP3ZJReQlZtf4rybgxVWTGqCZkGOZV6CF2LSMGnpRdx4UCQUVx5gY2mON/vWwuSupinmGPTdGRy7mYyVLzRFmuUprLu8TVWAqytRrUKAW7pIuDyRcVkiYVVBckmRsSRHVhYNK4mEyxMZqxUJayAy5nsnCZBAzROoSKfptJx0dPtjBLLyZJtD67vVxvc9P4WPg5dWCtlxcw/e2vOpItbeMevgYuOsldgMQgIkQAK6IBCZHIVB6ybAz9EHIxo8jm9PLMKmoUtQy8U0P2frgjnnIAESKJ0ARdO8OkiABEiABEiABEiABEiABEiABEiABEjAAAmM/ekcdl+SdSrsGOaHoZ3rGWAVpaccE5+GBZvPIDM7T3ISHRW3vdocQlxJIwFjJtB33imcuVPUajbExxlTBobDXMNumYbC5s+jt/DPqTuKdJ/s4IsvRtQ3lPSZJwlUmoAQSg9bEAHRaVoumL6fZYmjsZUOqbOBysLpUW18JHEuTXMCh68nYej8CGmAg40Frn7WEenZeaj39kFFkBMz2sLPteQGOGXRtLOtJTydrEoV+YpgW15phlYhRUKqV1dcxprj96V5yhNCt/jwCGKTZYKvnmHuWDqxseZFauApfsepE3zLhwqRcts6zth/JVE6VJpo+rt/7uCzbbfKnfH7sQ0wVElErjxg/r9R+HTrzXJjCIfhrb3x7ZgGGvkak9PAb89AdCr/4/kmeLSBmzGVxlpIgARIoFwCFRFNi2B/3dyNt/fMUonbyjccdV1D4GztgOTsNEQm3UGAkx9mdHpNxW/LtZ2Yd+xnBDr7w9/BB042DrA0s0RKdirOx13GzaSiPzu527ri39Frys2fDiRAAiSgTwTkoml/Rx8Ma9Af351YjI1DlyCEoml9WibmQgImQ4CiaZNZahZKAiRAAiRAAiRAAiRAAiRAAiRAAiRgLAREtzfR9U1Y09oeeLpXmLGUplLH7tNR2HakSMgyfUBtTDHRLn9GucAsqgSBLacf4PllRR2YhcPEvo3RMNg4H7u85K8LOB8p6yQqbMNL4WhXx4VXBgkYLQF1gun0Akv8W6SB0fvaT1+IxG/7b0t5fjS4Dp7rEqD3OetLgh9svIGf90VL6Qxs7oUfxzWUfu415xTORcs2y3w6tC4mPOJfImVl0bT8pBA/C0GxjZU5dp1PUOk63STAETtfb6GIczYqFb3nnlK8nv9kAwxpWdSNWn7idkIm2n9yTOG34MmGGNxSOx0y5UEHfHsGJ24lK+YQufZt6oG07DxsPR0HkYOyqRNN30vKRsuPjqj4iTihvvY4GZmCW3FFXaOFCPv8J+1hb22h4n/pbhq6fXlS5ZiPszVahzgjOzcfB68nIS1LtnlPbhtfboa2tU2rq6cQuJ+ITJG6dj/WkKJpfXk/YR4kQAK6IVBR0bTIavbh+fjj4sYyExTdp3eMXKHiM+/YT1h6TjMh9KpBP6KBe13dQOAsJEACJKAlApFJURi0fgKEaHpIaD/MP7kEG4cuRohLkJZmYBgSIAES0JwARdOas6InCZAACZAACZAACZAACZAACZAACZAACegFgekbrmPx/hjYWllg+pNtYWttqRd5VUcSP2yJwPWYJCm0v6sNtk1tDiFooZGAMRIYv+g8dp5PUJTWpWkABnasY4ylSjXFJWXgp23nkJAiE8gNaO6JheMaGW29LMy0CSgLpgPdbLBrWkuYW1pi8w3D47Lj4BX8fVbWGnvxhDD0aepheEXUQMZtPj6K6IdZ0szfjGmAEa1louUv/4rEvJ0yIfoj9V2xenLTEtkVF02LTtV/v94CIZ52km9Wbj56fnUK1+6nK8ZGz+kMM7OiUB1nHVeIiVvWcsLWV5uXmEeIuoW4W26iG7aYS1smOqz3nFMkVO4f7omFTzeC/GEKefkFePG3y9h8+oFiSnWi6Zd+v4z1J2Sds4UVF/C/t/46lvwXHrPYkgAAIABJREFUozj/eu9aeL13sEoZPb46iQsxaYpj0/rUwtSewYpcpHXadQezt8u6WY/v5IeZQ+pKnbpNyfp/fRqnbqfgt+eaoFsjiqZNae1ZKwmQALD8/FrMObpQQvG/Ns9jXJPhGmG5knADHx+chwvxV5GXr7oBRwSwsbDGkXHbVGJ9dGAuNlz5s9T4FuYW6BrcEeMaD0e4t3FunNcILp1IgAQMlsCtpDsYvP4Z+Dv6YnBoHyw4+StF0wa7mkycBAyfAEXThr+GrIAESIAESIAESKAKBJJ//x0P58zROILv4sWwaV7yH5U0DkBHEiABEiCBKhFIXLAASb/8onEM/7VrYVXHeMVmGoOgo1ERuB2fice/OY241ByM7d4ALeqV7BJoTAVfjUrEwm1nFSW93CMI7/QLMaYSWQsJSASO3EjCkO8jFDR83OwxZWA4HGytjJrQ2ZvxWLrzgqLGFc83wWMNKMoy6kU3weKUBdPOtpZY92I4Ggc4YM1VICffMIHIhdPOdpZYN0VWD610AsU7I0fMbA9PR9n7uxCkCmGqMCHIvT67E6wsVIW5xUXT6jpSFxc8H5/RVtpwJjchIhZiYrkde78tAtyKzovj4veQ+H0kTHT+F08A0KYJAbIQIstNmYP82MO0HDT74Iiic7Y60XTtNw9IQnFh9bztse/tVippCvF1w/cOKTpFF/eJTc5Giw+LOlULMbAQBaszwU1s2OsX7qlNFAYTq9+80zh9JwXLJzZG9zDjfPKFwSwGEyUBEjBIAgmZiYhJuYfMvCzYWtjA18Ebnvbq308zcjMRlXIX6TnpyM7LgbWFFRysHOBu5wpXGxeYK++GMkgaTJoESMCUCSiLpgfV740fTi3FhqGLUZudpk35smDtJFBjBCiarjH0nJgESIAESIAESEAfCCQvX46H8+ZpnApF0xqjoiMJkAAJVAuBxO+/R9LixRrH9l+zBlZ1+bhKjYHR0SAITF5+CZtOPUCIjxNeGmwam7m2HLqBvRGyx9mLboei23Soj71BrBeTJAFNCby55hp+O3RX4T62e0O0qOel6XCD9lu68yLO3oyTanijTy281ku1G6hBF8fkTZ5AaYJp0bw2UdZ02GBt3e4LOHQlHkHutlLXYyGgpqkn8OuBu3h33TXppBAqC8Gy3PILgJA3/lOIhFe90BRCKKxsxUXT+99ujbresi7Tcrsam45HZ59QvP5nWks08i8Ss6dl5aHRe4cU87zSIwhvK21Ey87NR8ibBxTjZ4+oh6c6+Gl1SUUX6Q0nZR2iW4U4Y8srzdTGf+Sz47jxIEM6V1w0nZGdj7pvF+U5c3AdTOwSUCKOcrdpG0tz3Pyik8Lnv6uJGPlD0aY88dmyRbCTVms1lmB95p5CRFQqlj7bGD0bUzRtLOvKOkiABEiABEiABEhA1wTkoukAR18MqN8LP55ahg1DF6G2C/8OSNdrwflIgAQAiqZ5FZAACZAACZAACVSJQG5MDMwsLGDh41OlODU12NRE01lnziBj//5ycTsOGgTLoKBy/ehQNQLZly4hfdcuRRBzZ2c4jxtXtaAcrTUCWadPQ7xHpO/eDafhw+E0ZgysQgyns2n6v/8i+0JR10brsDDYd+umNT41FYii6Zoiz3n1hYB4DLl4HLmwLuEBGNjBNDqpxydnYvaq48gXqiIA4zr64fPh9fRlWZgHCVSZwK24DHT/6hQysmWPbm7TwAejHgutclxDCXA1OhELt8rEa10bueP35xobSurMkwTKJKBOMB3q54A/bwGpOYYPLyM7F4u3ReDm/TSp07ToOE3htPp1FQJdIdQVNuERf4hO0co2euE57L38UDo0vpMfZg1T/ZxTXDQtBMBCCKxs4ndJx1nHFYeKi6bFCfE5UnyeFObmYIVzM9tD3rTyn4sJeOrn84rx5z5uD3cH7T7tQDwt5WRkijTHiNbe+GZMA7XAlHkUF01fvpeOrl8UicOXTWyMHmo6IBfvvH39806ws5YxW7w/BtM3FHXdvvpZR2ljHq0kgd5zT+FsVCp+fTYMvRp7EBEJkAAJkAAJkAAJkAAJVIrAzaQ7GLL+GQQ4+eLxuj2x8PRybBiyCLVdKZquFFAOIgESqBIBiqarhI+DSYAESIAESIAEYidNQubx47Dv2ROukycblKBQrJ460XRpAnAzS0v4/PADLAMDDXbhEz77DClr1pSbv8/ChbBt06ZcPzpUjUDaX38h7t13FUEsAwIQsGVL1YJytFYIFKSnI6p3b+SnpSniWTduDL/ly7USXxdB4j/5BKnr1yumEsJvd6XrTRc5VMccyUuXInnlylJD58XGqpxjp+nqWAXGrEkCykKXp3o2QrM6pvOYcOVOtAGuNjg2o6hDY02uCecmAW0QmLPjNubsiFSEmjIwHHX8XLQR2mBiLP7rPC5EJsDV3goXPmlvMHkzURIojYA6wbSLiwMO3wMK90cYBTwhnP5u/UncT8rCqDY+mDfadDZ8aLqAOXkFqPvWAUWHZ0tzM9hbqwp0kzNzFeF8nK1x6sN2KuGLi6aj5nSGuZlqBpqIps/cSUXfeacUA5W7Wr+x+ip+FxcogDB/B+ya1lLTEjX2a//JMdxOyJT81YnH5YHGL7qAnefjpZfFRdMHryVh+IIIxZybXm6GNrWdS+Sw+lgspv5xRXH8xIy28HO1kV5/vv0Wvt11R3FOHU+NizJyx55zTuJ8dBoWPxOGPk0omjby5WZ5JEACJEACJEACJFBtBG4m3caQ9c8iwMkP/et2x0+nf8P6IYtQh6LpamPOwCRAAqUToGiaVwcJkAAJkAAJkECVCCQuXIikX34B8mQd0WyaN4fjwIGw69IFFu76/8jG4qJpl2eegetLL1WJiT4PNnTRdMaBA8hPSlIgtnvkEYjuzIZq+iKaLsjNRfrOnQqMZtbWso7E5qpdqwyVc2XyTtu2DXHvv19iqP/q1bCqZxidTY1VNF3WeuanpODOo4+quFA0XZk7gGP0lUBiei5afHgEWbn5Uoozx3eAvY2lvqar9byOX4nFyt1FwpftrzVH8yA+Rl3roBmwRgg8veg8/j6fIM0dGuiKSf2b1kgeNTnprdhkfL/xjJTC9qnN0TyY93dNrgfnrhoBdYJp2DrgTFzV4urr6Jj4NPy4JQLpWbmY2CUAMwebxpMwNF0P0WFadJquiB2Z3gZB7raKIdoSTYuAysLlnmHuWDpR1t0/fMZhxBW2QJ/+eG1M6ab9pgHKnaZHtvHB16WI7MsSTd+Kz0THT48p2PwyvhH6hZfcSDj/3yh8uvWmwu/2V49ACNaFrToai9dWFn2uPDy9DYKVeFdkrYzdt8dXJ3EhJg2LJoShb1OKpo19vVkfCZAACZAACZAACVQXgZuJtzFkw7MIdPJD3zrd8POZ37F+yC+o41qruqZkXBIgARIolQBF07w4SIAESIAESIAEqkwg59o1pG7eLH3lJydL8czs7WH/2GOw69wZDr17V3mO6gpA0bR6svraafrumDHIvnRJkbTomiu65xqq6YtoOj81FXe6dFHBaEji4OpY/4z9+3H/1VdLhA7Ytg2Wfn7VMaXWY1I0LUNK0bTWLy0GrEEC4lHqotO0sFo+znh5cLMazEb3U2dk5WL2quNIzciRJhePtRcdCmnGR2DLmTgsPRCDoa280bWhG/xcZF0hjdnEhojY5GypxFGPhaJNAx9jLrfU2n7YEoHrMUmS4FIIL2n6Q0Dcl1fupaNfuAca+TnoT2J6mElxwfTi58KRY+2A6FQ9TFaLKYl7V9zDwkS3adF1miYj8N7661jyX0yFcHw0uA6eU3of1KZoevH+GEzfcF2Rz6VPOyAhLQcdZx1XHDv2flsEuGn/9++Lv13GhpP3pXlahThjyyvqP88+/ct5/H1BtpmoeKfp3PwCBE/7T5Hra72C8UafkkKLF5ZdwubTDyQ/Z1tLXJrVQTHm1O0U9P/6tOL17BH18FQHw/izfoUuJC04d/vyJC7dTcPP4xuhvxpxuhamYAgSIAESIAESIAESIAETIHAj8TaGFoqm+9Tuil8iVlA0bQLrzhJJQF8JUDStryvDvEiABEiABEjAAAnk3r0rCaczDxxA1rlzigosvLwk8bQQt1o3bKhXlZmaaBr5+UBBQYk1iGzTRuUYRdO6uUwpmtYN50rNkpeHqMcfR15srGK42AjiNXdupcLVxCCKpmXUKZquiauPc1YXgQmLL2DHOdljyru3CELftiHVNZXexl2z9yqOXJI9Nn5QCy/88JR+fbbUW3AGltjwBRE4eK3o6SJdQl3xaAM3PFLfFU0DHQ2smvLTvXQvDd2+OCk5WlmaY+bTHaTvpmh7z0Rhy+Gb6NPUC4sn8P7Wp2tg+PwIHLwuuy9re9mhf1NPdG3khg51XfQpzRrP5Xx0GqauvAzx3dHWEh+MCoeZremIzI9djsWqPbLuvYsnhKEPu9JKLJQ3xkx6NADjS9n09fHmG/jzrOyzXrs6LtjwUrjimtamaDo1Kw9h7x2CEB8LExtVxM8zN8u6MouOy6LzcnXY7O238M2uO4rQZz5qBy8n6xJTdfn8BK7dT5eOFxdNi2ON3z+Mh2myjXRuDlY4O7M9CptIS8dSMvPQeHpRjeGBjvjrfy0U84jzDd49qHjtYGOBUx+2g6ONRXWUbdAxu35xApfvpeOnpxvh8WYlO3obdHFMngRIgARIgARIgARIQGcEbiRGYuiGiVKn6d61H8OiiD+wbsgvqMtO0zpbA05EAiRQRICiaV4NJEACJEACJEAC1UJAiKYzDhwoIaC2ql0bjoMGwaFPH1h4e1fL3BUJanKi6VLgRLZsqXKGoumKXEWV96VouvLsdDEyLz5e2giSdfw47Hv2hEOvXlIXfUMxiqZlK0XRtKFcsbrPU3TMfH7pRTQJcMSotj7o1sgNtT3tdJ+IhjMmpucibPohhffkAeGo6296QrXzkfFY8tcFiYOHo0wgQzM+AunZedh6Jg5zdtzGnYRMlQJb1XJCjzB36atxgHEIqFcdjcVrK2Uiw05N/DGkU13jW1QNK4qOS8W8dafg6WyDiA/bajiKbrogILpMbzz1ADvPx+NCTJpiSn9XG+l+FJsahLDRxc5SF+no5RxCKD1sQQREp+lgLwc893g47KxNj8fO45HYeeI2nO0ssW5KOBoHmI5oXN2FKX6PtfvkmOLUn6+1QLMg9b+/RFdk0R1Zbre+6ATrwk002hRNi/jKXZhDPO2kz1UnbsmeHje1ZzDe7Fs9j8gW4lshwpWbEIf/Pqkx7K2LxMorDt/DtNVXFT7qRNPiM8KcHZEKn16NPaROyFYWZpJgesSCCEREFbV3/35sA+kJFsomnuAinuQiN09HKyx8upHazSDis4mVhbkU39Ts0dkncDU2HT+Oa4iBzb1MrXzWSwIkQAIkQAIkQAIkoCUC1xMjMUwSTfujZ0gXLDm7kqJpLbFlGBIggYoToGi64sw4ggRIgARIgARIoIIEhIA6fccOpP35J/ISZI/WFGZdvz4c+veHbdu2NdaBmqJp2VpQNF3Bi1pL7hRNawkkw6glQNG0DAtF07xBSiMgF00rn+/eyB39m3mif7gHnGz1S+S07OBdvL32miLdD55qByf7kl35TGHFP1x2GKkZss6C/7zREo38TFuMZexrvvpYrFrxtKi7Yz0XhYC6nrfhbGwqvmbvrLuGpQfuSodfG9YCAZ7GIQav7LX55s//IT+/AEemt0GQu21lw3BcNRLYe/khdl1IwL8XH+JmXIZiJld7SzzWwE3qDC82JJmSKQumG4d44ImuoSYpmJav+crdV3D8Sqz0eeqb0aEm3XH6l33RmLHxhoTGxtIcN2Z3glkputvim+SWP9cY4vOpMG2Lpk9EpmDAN6fV3qa732yFBr7V93t19MJzEO8jchNc2tR2hoW5mSR0lneQlp9XJ5oWIubG0w8jKzdfpQZnW0skZ+aqHAtws8HR6W1LcBe8RRfw4jEszc0Q6G4LG0sziK7ccSk5ks8v4xuhX7jpdVqWd/0WT3gRT3qhkQAJkAAJkAAJkAAJkEBlCMhF00FO/uge0hm/nl2FdUN+Rl1X03uaYmX4cQwJkIB2CVA0rV2ejEYCJEACJEACJFAGASGYFsJpIaAWQmplEwJq2w4dJAG1XceOOuNI0bQMNUXTOrvkVCaiaLpmuJvKrBRNy1aaomlTueIrV6fominE00v+i0FC4eO9RSRfF2v0DxfiaU+0r6sf3ZxFtz3RdU+YrbUlPpnQoXJFG8GohVvP4mp0olSJeJz8xC4BRlAVSyiPwK8H7uKH3VElOk/LxwlR2YDmntJj45W7VZYXVx/O95t3GqfvpJj8vS1fiw+WHkZaZg6+Hh2KkW1MS3irD9djRXMQwundl0oKqO2szPFYQzeMbe8nPc3BmE1ZMN061EcSTNMAuXBasJg3OhSjTPR+HvJ9BI7cSJIuCXEv/PZckzIvj46zjuNW4WaE0e18MWdUfclf26JpEbPNx0cR/TBLJR/RbTmimp/kIerr/82ZEuJo5UQEK/H+IkydaFocF8Lr8YsulBA9K8cRIuo1U5qiaaD6DUkHryXhuaUXy8xFHm/647UxpVugyd3ej3x2HDceZGD+kw0xpCVF0yZ3AbBgEiABEiABEiABEtASgeuJtzBsw3MIcvZHt+BHsPTcaqwd/DPquVE0rSXEDEMCJFABAhRNVwAWXUmABEiABEiABLRHIPP4cWTs3Yv0vXuRGxWlEti6USPY9+gB++7dYRUcrL1J1UTShmg659YtpK5dqzZPCy8vOD/9tHQuPyUFyUuXIvPUKeRcuQIzGxuIWsWX85NPwtzZudxac+/cQcbhw8i5ehXZly8jPzkZsLCAdYMGsKpXD3adOsE6tOL/QKst0XRBZiZiX3wRZpaW0hfMzWFmbQ1zJydYBgTAukkT2DRqBHNX13JrTduxA9lnz6r4pW7ahPy0okdBi5j2jz5aaizB2GXSJIl1eZZ15gyyTp9G9rVryL54EcjLk9bEqn59qSu6uCYtPDzKC6M4L9Y7ffduKZaInX3pEix8fGATFgbHgQNh16ULRI1x776rGCPqCdiyReM5KuuY9NNPsmun0Aqys5FS7BoW15JVrdIfx2sZHAynkSPLTaEgK0vG4coViYFYUzNHR9iEh0vXqk2TJrBt167cONXpIGrPvXWr3Ck0rVkESpw/HwUZRV333F5/HcjPR9quXUjbulV638uLi4Olvz+sQkNh27o1HAcMkO4ZTUyMzzhwQNqAIq7b3OhoWNWpA5umTeE0Zox0zVZWNF3l95m8PDz8+mugoEBRilhz10mTyqwvdf165NyQdWCTzMJCun/NHTTvICvuuzvF3hMomtbkiqLP7fhMLDkQgyX/3UV2sW51neq7YnALL6mrmqNN0WPDdU1t2PwIHLouE9wEezvhlSHNdZ2C3sy36eAN7D8bLeXzSo8gvN2Pf7GuN4tTzYmkZeVh8X/iXo3BvaRstbOJzsRCOD2gmSeaBztVc0baCe//v/1SIF93B0wb0VI7QQ04yud/HEdccgaGtPLB/LEV/7ONAZdu8KmXJqA2N4Mknh7X0Q+NAzT/bGcIQJQF03X9XDB5YLghpK2zHCmcBoKn/YfcfNmfjT4fXk+6D8qyDzfdwE97ZZ9zRIfkY++3lX4W3d3H/XJeMTRqTmeIe0vZYhKz0HrmUcWh/W+3Rl1vu1Kn+3lfND4o7IItd9LVZ6ukjFxMWX4Juy8VdZwWOYR42mHGgNq4m5SF99Zfl9IqTTQtzonPA5OXX8KJW8kKzuK46Bbdo7E7vn4iFM52ZT9BJiM7H59vv4VVR2NLdKlWhvdy9yC809/0Pnd2mnVceqrA92MbYGgrb529f3AiEiABEiABEiABEiAB4yJw7eEtDN8oE013De6EZefWUDRtXEvMakjAoAhQNG1Qy8VkSYAETJGA6EIqRESSiEqIDwu/l/qzhYWKT5ljLCwqFFej+UWOpcRVO77Qt9y6isUtNRdTvEgMvOaC3FyZeHrPHul7fmqqoiIhuhXCabmAujpK1YZoOvPIEcROnqw2PSH2C9q/H0JYHTtxIkS3bXUmxLTe8+bBumHDUstM3bAB8R9/XC4G1xdegPMzz8hEyxqatkTTQsgZPXBgubMKwbDb1KmwCin9H5sSZs0qIeQtN7Aah4BNm2AZFFTqUCH0fvjtt0hZubLM8GItPT78ULomy7Pc27dx/9VXkRMZWaqr06hRsGnWrEZE09EDBkgi26qYhbs7AnftKjOEuO7j3noL2VevluknxMJub75ZIXFsVXIvPlasVcZ+mVipLLPr0AHe8+eX5yadv9O5s4rAP2j3bsTPnCkJyEszIZz2mDkTlr6+Zc4hchU5l2Ves2cj48gR2WeIQnMaPhzuSiJ9deO19T4j7qnkX39VmcJt2jQ4jxmjNu3MEycQ+9xzKuck3t99p7GQXAymaFqjy5NOZRC4EJOG5YfuYuPJBxBCDmULdLORhNPiq0mA+k511Qm31UdHJfGIsFah3hjdtUF1TqfXsY9djsWqPVekHJ9/NAAfDKqj1/kyOe0TEJ3h/zwbL3XdLC62Up7NULpP137zgNQls2GwOyb2bax9YAYW8ev1pxD1IBV+rrY4MaONgWXPdOUEShNQi07wY9v7okM9F3Ss61KumFGfiSoLpv09HCTBtJ215n8O1+fatJmbsnD6q1GhGNOOHeS1ybeqsQ5fT8LQ+RGKMEffbwvxuVdXJvTkV2PTkZKZi0Z+DnAo3KQoNkolpufCydZCOmZRXCFeLEGxZzcyPkMSUYsNVEJwXhkTv4+v389QPIXG1socovt2oLutJMQ2Revw6TFExmfi2zENMLw1RdOmeA2wZhIgARIgARIgARLQBgG5aDrYOQCPBnXA8vNrsXbwT6jnVlsb4RmDBEiABCpEgKLpCuGiMwmQAAlUL4H0v/9Gypo1cBw6FLatWiFp0SKp46LoyEvTkIBcWF4BMbboZKmRaFsuWNcwFbpVnEBBTg7yExORl5iI/CRZJ0W5mdvbw9zDA0KoaWZlBZtWreD6/PMVn6TYiOoWTYvpgvbswYNp08q9l0WHWN+lS0vUJLoAx73zTpliy+KDRCyfRYs0Fk5rSzQtOt7ee+YZjdfFY/p06T1PnelCNJ0bE4P7r7yi2t22nOxF53C3MgSrgsH9l19WEcyWFlJ0nE7dvFlxWledpnUhmhbi4Aeiu7KGJmr3WbCgTIG7hqEq7KYL0bTrlClIXLCg3Nyk9wEhNjZT/4+xKatWIWH27HLjCAf7nj0hPlvIrSzRtLbfZ0S8e089VUIw7792rdQRW9lE9/iY4cORFxtb9J7v4AD/jRsr1N1dDKZoWqNLg04aEBBd+jaeeiCJp89FF23okg/t38wTA5t5Qnw3L+V+1WAajV0yc/JR560DCv++bULQvWXpG4I0DmygjrEP0/Hl6hNS9uM7+WHWsHoGWgnT1gYBcY9uj4jHn2fjcPleutqQQjwlNjyIR8oLQZa+WcP3DiE5IxcdwvwwrDOv54Vbz+JqdKK0TDFzO+vbcjGfShD492ICdpxLwI7z8bifXNQl3sPRSuoKP6C5FzrUdalE5JobQsF0xdgrC6dnjQjF+A4UTleMYPV4C1Fy969O4m6ibGPeI/VdsXpy0+qZjFENlkD7T49BPJnnmzGhGNGa967BLiQTJwESIAESIAESIIEaJnDt4U0M3zgJQjTdJag9fju/DmsG/4T6FE3X8MpwehIwTQIUTZvmurNqEiABPSQghNGx4rH1AESXWLvOnXF37Fg9zJQpkYB+EBCdir2//rrKyWhFNH3sGGLLEHB7fvwx4t5/X5Gr6FgsOkuLTRHFzXfJEqn7sLKlrF6NhM8/r3Ct7u+8A6cRIzQapy3RdPo//+DBG29oNKfcyW/VKljXr19ijBCGCoFoVa2sTtNC1FtW59/S5i4tZ+TnI2bkSLVrK9ZcrL3yuovXQjAqN12JpmOGDCmzC7YmzEU9gX/+qdZVdIyP7ttXI+G4cgD7xx6D19y5mkyvVR9diKbF2mra3dvz88/h0KtXiRrz7t9HVJ8+amsXQmRxLRUXHitfX2WJpqvjfUZc60IMrWyim77vsmUqGzpEB+7UjRtV/Ly//x52HTtWeJ0pmq4wMg7QgMCWM3HYePK+1NW2uNXztkP/cJl4ujq7T1+8m4buX55UTD+uZyOE1/HUIHvjdXlv8UFk5eRhdDtfzBlV8nOE8VbOysoisOOcEE/LBNQpmXlqXYe09JbE0z3C3PUGZvMPjuB+SjZMfUOEfEGW/X0RETfipJcUTevNZaqVREQn2W0R8dhw8j72X5EJ4+XWsZ4LHm/mJYmohZhan01schi2IAJCOB3g6YAXBrDDtCbrJRdOi27cy58PR8fa+reJRZM6jMFHdFPediYOn22/heiHMsG0sN1vtkIDX3tjKJE1aJFAu0+O4U5CJr4eHYqRbSia1iJahiIBEiABEiABEiABkyJw9eFNjCgUTXcObIffL6ynaNqkrgAWSwL6RYCiaf1aD2ZDAiRgwgSUH0vv+sorcBk/XhLW5SUkmDAVPSxdPOsxLw8FeXkQ4kjpu/LPmh4TMfLzVceWF6eUMerilHlMKY7CT11sUasem9OoUXB/660qZ6gN0XTxJO6/+CIyDh1SHBYC74x9+yBEk0IQal2vntRFNlON2Fp0o3WZOFExtjTxqcf770tdZM0dHSE6uoqNF/EzZqi8ZwhBbsCff0o+5Zm2RNPifshPT4foGo6cHOm7eC2EnDm3byN1w4YSgmIh9vRbuVKjrth3x4xB9qVLinLc330XQghaGcs6cwb3JkxQGSrWSLC1ad4cZtbWEPzTtm8vIVq369AB3vPnl5g2Y/9+CAGuson6vL/5Rlp/YYJJyooVePjNNyXG60o0XXxiUeedLl1UDvuvXg0rca1WwpJ++gmJP/4mkqEXAAAgAElEQVSoyqFWLbgLtmFh0jWRefAg4mbMKBFddFsX3ZZ1auL9WI0lLV6s0h26tHVXN/ZO585qReOuL70Eh/79YentLXVFTl6xAoKXstm2aQOfhQtLhBXXTHKxbvQO/frB/e23Ffe56NL/cO5cpG7ZUmJ8aaLp6nyfUSfGFpvDXAo3iqm7Z8rr5l7WtUHRtE7vHJOb7MydFKlL5t/n43E+pmjDixxEz8buMgF1uKfiseLagrQ9Ig4Tf72oCPfy4Oao5eOkrfAGGeeHLRG4HpOEoa288f3YBtVSQ0RUKmKTsvH0ovPSI+HFl3gsu4U5FK9LPW6miX/JWLL4qseLjhUdl32okP4H+cf2AhQU/Vx4TnIrEGc09ZfFUPGXBVGdSxFfnb/spGpusjwKQxWLr86/IrWUzC0zNx8PUrKRkJoD0UUzN7/kn22c7Szh5WQFL0driIcKCROdbl/vXatarqeygsrFSKO7NkCrUD72fs3eqzhy6Z6EjKJpnV+OOptQiKaFeHrDyQcQAk65BbjaYEq3QEx4xF9nuVR0ouELInDwWhLsbSwlwbS/B8W/mjDMyM7FD5sjEBOfBncnW+x+owW8HC01GUofLRDYfPqB1C34XHQaxGfL4r8bX+0RhLf6hWhhJoYwNgJtPj4qievnPhGKJ9pSNG1s68t6SIAESIAESIAESEBXBJRF048EtsWKCxuwZvBC1HdTfTqprvLhPCRAAqZNgKJp015/Vk8CJKBHBJRF026vvw5ndpnWo9Ux0VSEAFwu7C5HaK3Wr1AELonMC+PIxebKx9SdFxsG5F+592T/WC6ZuTmsgoJgGRwMz88+g7l91bvf6EI0LVK3cHeH7++/w9JH9R8X7k+dKgmq5SY6Q4sO0XJTJz71X7sWQohb3MQmi5hBg1SEmpqKirUmmtbgdhEi0YdffaXiWWrn5mLxtCmavvfss8g6dUoxg1WtWvBbsQJmdnYlqlAncFcnKr73zDPIOn1aMV50Y/ZfuRLmLiUfNS3E2HHTp6vMZQyi6fzERNzp1q1EXaK7sIWbm8pxwUowUzbb9u3hs2CBBldS9bskLVqERCVxfFVF015ffgn77t1LJF6807J4vwjctUvFT4ih73TtqnJM6swt7iW50kvprLruzaWJpqv1faagANL73P79qve8eD/085M6UStvEJM6Uf/6q7RpoTKmS9F0enYeTt9OLSFulMSOZiUFlcrHzOWCyEJRpVwgaW5mVpmyOaYGCAih187z8fj7fAJuJ2SqZBDgZqMQT7ep7ayV7Ob/G4VPt95UxHpzVCt4u1b9c5BWkquhIHLRtBCp/zy+kdazuHIvHY99cULrcRlQvwmIzQ9Ln22s8yS7fH4C1+6nY/KAcNT1L/m5UecJ1fCEWw7fxN4zUVIWhiSaFvsC8pU2GEg/iz0BKscLIDT8suOFPxdubij7uCyWkBZLGyGkmEo/a3S8KEYNL7HK9GJjw85z8dh/NRF3E4s63jb0c0CbECfMHqFfTxP4eV80Ptgoe2oU79mKX0nKwum+LQOw6En+43jFKVZuhP//VP9MphxleGtvfDO6gdjfTyOBEgRazzyKmMQs6eku4ikvNBIgARIgARIgARIgARKoDIGrD29gxMbnUcs5EB0DWuOPixuxetBChLrzz4WV4ckxJEACVSNA0XTV+HE0CZAACWiNgLJoWnSMdBo5UmuxGYgEDIGAEKWm79mDzMOHkXOzSBQkchciOofevSWxoWVgoFbL0ZVo2n3aNDiNGVMi9+JiRdGV2vvrrxV+xUW4ri++CJdnny2VgehgnKAkSBYbMMRGjPJMl6JpkUvslCnSWstNiODFGpdn2hJNF2Rl4XaHDirT+S5aBJsWLUpN4cH//iddo3ITXcOFaFVuBZmZuN2xo8p415dfhkuxbtYKh/x8xIwcqdJ52xhE05lHjiB28mQVDh4ffADHQYPUsi3OVTjVOnYMsLAo73Ko9vPaFE2L9zG/33+XuswXN3Wi/OCDB2Fma6twVcfVd8kS2DRrppZDbmwsovv2VTlXmmi6ut9n8uLjETN4sMqGDrFJQWyAKS6mDti4UTpeWdOlaPrHPdGYuVkmmNGWicvDopiQWibCNoNCaC3vQqvwUxJoS8eKXsvGqBNxF5+ncJxiHqU4Svloq05jiiMEcdfvZ+D6gwzcuJ+OjBzVzvVB7rao72OP2p52cLWXdVIM9bXHgGaeFcKw5L8YvLf+umLMjKfawdm+cpsLKjSxHjvLRdM9wtyxbKL2Ra7xqTn436orSM/Kl966zc3EW7hZse/iuFnheTOId3iN/NTFg+xXhIinGqPYHIVjRVPW3Lx85OQVSF+50nfZa9E5UnY8v/C4+te5+aWML4yhx8tfbam98FggZgysXW3xSwvcc85JnI9Ow4TeYWgc4qHz+fVtwu1Hb+HfU3ektAxBNP3boXt4c81VfcNoNPn0bOyBpc+G6UU9dxIy0XPOKSRn5GJI+2B0aqb7zvR6AaKKSSSkZGLWimNSlN+fD0fXBtwsUkWkGg0PnvZfie7SIZ52eKNPMIa05FMONIJook4tPzqCe0nZ+HJkfYxtT9G0iV4GLJsESIAESIAESIAEqkzgSsINjNwkE023D2iFVRc3UTRdZaoMQAIkUFkCFE1XlhzHkQAJkICWCSiLpj3efx+OQ4ZoeQaGIwH9I5AXG4u0f/5B+q5dKt155Zk69OoFeyGWLtZdVZuV6Eo0HbhjByy8vEqknnHoEETHYbnZtmgBx6FDFa/vdO6sIjQM+PPPEt2qlYPm3LqFGKXxxUXYpbHTtWg66ZdfkKjUTdjluefgWkxoqy5XbYmmi3PSRKycumkT4j/6SJGW27RpcFYSwufevo3owYNV0g786y9YeJf+j48JX36JlD/+UIzRJA9tXv/yWPmpqbjTpYtKaHWdtDWZO3X9esR/8omKa/CBA2o7eAunjAMHcP/ll1X8A7Zvh6Vvzf9DnDZF0y6TJsH1hRfUIsyNikL0wIGqDIrd66mbNyP+ww9Vr5XNm9WKsOVOMUOGICcyUjGmNNG0Lt5nhDj6/quvlnkJeX78MRz699fkMivVR5ei6Y0nH2DtiVj8e/FhlXLmYNMkUFER4J9n4/HskgsKWJ892wlWluamCa+warlounOoK1a90NSkWVRH8QUA8vILkJ9fgLx8IK9AfJd/ybrpKr8W52W+Jf2lh85Uk7/a/EQHXkUuBcgrkNUivuJTcnAvJRv3krKQmpmngs7Kwgzfjm2AQc1LfmavDsbKMR//5jRORqbgia6haB3Kx96v3HMFxy/HSogq+n5Z3WulLj5F09VHXWwmmdYnBK/1DKq+SSoQWd5l2t/VBv8b1bYCI+lanMAPmyNw/W4S/N1s8e+0FnC2k20uo1Ufgad/OQ8LCzMEuck29bUOcYLo6E4jgfIItPjwCGKTs/HFiPp4skPN/11NefnyPAmQAAmQAAmQAAmQgH4SUIimXQLRzq8lVl/ajNWDfkSoe139TJhZkQAJGDUBiqaNenlZHAmQgCERUBZNe86cCYfHHzek9JkrCVSIgBDPpQux9D//qAiCRRDbVq0ghL7iS3QirW7ThWha6jC7YkWFS8lPS4MQMyqb79KlZcYR3Y5jn39e4SMY+m/YUO7c2hZN51y/jvTdu5F77x7y7t5FbkwM8jMzYentDavatZF7/75Kp2nRhVh0Iy7PtCWazjh4EPdfekkxnegw7TZ1apnTZx46hMQff1T4OI0aBfe33lK8VtctuNbJk2XGTF6xAg+VOoMbg2g68fvvkbR4scbXYG50NKIHDFC9zhcvhk3z5uVdDtV+XpuiaY8ZM+BYTFQvL0DcJ9H9+qnUU3yDRPGu9KLzvteXX5bJ4P7UqcjYt6/omh0+HO7vvqsyRpfvMwmffYaUNWvU5izE0kI0XVXTpWha5CoXSck7w4rOzqJDbKmvpU7OSufVvdbAR+pGW95c0vly8ikeo8y5zRRdbOUdbKXvuUXdbZWPi663QqAoOt4qCytVX5d/Xgg3jc3srM1x/fNOFSpLiCmFqFKY6B4++7lHKjTeGJ3louneTTyw5Bn96EBqjJwNvabs3Hzsu5KI/VcT8dfZeIguscWtUz1XjGrrg+Gta67L5vAFETh4LQmDO9XFI038DR17lfP/ads5XImSbUoyBNG0vOB+X5/G6dspVa6fAUoS0JfrYMLiC9hxLh4v9Q1FSDA3OFTlWp279iRi4tOkEPNGh2JUG/KsCk+OJYHqJNDsgyN4kJKNz4fXw7iOftU5FWOTAAmQAAmQAAmQAAkYMQG5aDrEJRBt/FpgzaUtWDXoRzSgaNqIV52lkYD+EqBoWn/XhpmRAAmYGAEV0fTnn0N02KWRgDEREJ195ULp7EuXVEqzbtRIEknbd+kC8bMuTReiadEp22vOnAqXlXPtGmJGjqzwOOUB5g4OCNq/v9wY2hJNZ58/j8Sff1YRapY7OQDHgQPhodRFt7Qx2hJNp6xdi4RZszRJrVQf+5494TV7tuJ82rZtiHv/fcVrTQTrost43PTpijHGIJp+8NZbSP/7b0VNdh06wHv+/FI5CqH/7Y4dVc57fvIJHIqJiKu0WJUcrE3RtNfcubB/7DG1mWgimhZdzkW3c7k5PfEE3N98s8zKRMdv0flbMUaNaFqX7zMFGRkQ97By92uRm7ju/VauhHi/qqrpWjRd1Xw5vnwCKp1sS4iw5aLroi6yJUXZRedKirjF+LLOo5joW1nkLRunXgQOqYPt3aQs3EvOQlxKjqJQIaqv7WmH/e+0Lr94JY+YxCy0nnlUOuJoZ4UPx7Wv0HhjdJaLpp/p7I9PhrAbiTGucWVrEp0Q9xcKpXddSMDDtKJ7UDmmENw/0dYH4ntN25wdkZiz4zZ6t66Fnq2CazqdGp//qzUncS9BJqbUF7GsplAKCoACFCBffJe+lH5WOi66tSvOQ42vGFfiuGyMFFuDOYrmFt6F4wrnleWnbg7l40rzycdJORU7XiJ2YW3ScXmdQHJGLi7EpOHi3TRciU1XQVrX2w51vexQx8sOrvZWKuPq+9pjQDNPTZegWv2m/nEFq4/FYuyjddCiYUC1zmXMwYVYWoim5cYNUMa82qzNGAiEzziMuNQcfDasHp7uRNG0MawpayABEiABEiABEiCBmiBwOeE6Rm16ASEuQWjt2wxrL2+laLomFoJzkgAJSAQomuaFQAIkQAJ6QkBZNC3ElUJkSSMBYyCQ/u+/SNuxQxJMQzybu9CEoFTqKN25M2xbV0w4pE0uuhBNO40YAfd33qlw2tkXL+Lu2LEVHqc8wMLdHYG7dpUbQxui6czjxxE7aVK5c6lz0LVouniH58okXbwzbsrKlUj44ouia7xOHfivXVtm6LS//kKcUudfYxBN33/1VYhu8nITQmEhGC7V8vMRWew9wGP6dDgOHVqZZdHqGG2Kpr3nzYPdo4+qzU8T0fSD//0P6Xv2KMY7jx0Lt9dfL7NeTUTTunyfEck+nDsXyb/9ppJ3ecL6iiwqRdMVoUVfbRMQwsy/LyRAiDTF96ycos89Q1p6Y0hLL/QIc6/UtEJ0Fvj6f9JYTxc7vP1EzX12qlQB1TBILpp+f0BtTO4aWA0zMKQhERBCnq1n4rD7UgL2Xk6E6DBdmomO0qKztOgwrS8mukyLbtONa3lgQh92Tp+x9DDSM3MQ6u+MPdOa6csyMY9KEBDibNGZWf6VlJGriNKtkRu6NnSH+C42FBmC/bwvGh9svIFaXg54eWhLQ0hZL3OU/w53sLFEWlYuJnYJwMzBdfQyVyZFAiQANHn/MBLScv7P3n1AN1n1YQB/uveedDDKbIGyRRQBRYYyFFyIIoqf8iEoCn4qDhQHTjYoKiBLtsgegsjes4wyy2zp3rtp+p37xoYkTdukTduM557DwSb3veN33wTUJ//gq8GN8UpXfiMG7wkKUIACFKAABShAgaoJqIamOwRG4o9Lm7HiiZ/QwrtJ1QbkVRSgAAWqIcDQdDXweCkFKEABQwqohqb9Z86EU1d+5bYhfTlW7QqI0Jqoupu9cSNEIK+02fj4KCpKd+8u/W4MrTZC0x4jR8Jz5Ei9tyvPzMTtcirT6jqYriHc6oamRQXZO717Q56jqAhX2kTlWIf27SHC21Z2dpDn5UF26xYKzp5V61fboWkR6hXh3uo0N42qvaK6sqiyXNp0sTfH0HTqt98ia+VKpYN9y5aot2RJudTFKSm406uX2vP+s2bB6cEHq3M8BrnWmELTqd98g6xVq5T70rz/tG1Yl9B0bb7PFF64gLsvvqj1bHw++QSugwZV+9wYmq42IQfQUyA9V4Zd0anYdTFN+l38XNqCvRxQGpYOr1f9SuptPz2CxKxChPq5Yuzgdnqu1Py6f7viOJIy8jD3pRYY2NbP/DbIHekksP9KuhSWFr9EmKe8Fuhhjyfa+eHJdv5oE+qq09i13Slo3D5WkgcgK5bjg3kHJP6n7g/BrGcb1fZRcD4DCBy7nolt/4alY5LylCO6O9pKHyIa1MEf9zVyN8BMtTuEqJZ93xfHkJkvw3M9mqFT84DaXYAZzHYtLgMiNC2au5OtVIF82vPN8FwnWprB8XILZirQ8uNDSMuVSd/uIr7lhY0CFKAABShAAQpQgAJVEVANTbcPaI21l7dgxcCf0MKHoemqePIaClCgegIMTVfPj1dTgAIUMJiAamg64Kef4Ni5s8HG5kAUqA2B4rQ05B8+jLxDh5C7cydK8vOlaa3s7aXqqs6iqnT37rB2Na6QQm2Epj1Hj4bHq69W6RhuP/SQWhDZf/p02IeH6z6WjY0UWK6saYam/WfMkKqA69qyN2xAymefqXX3/fJLuPTpA9jYlBkma80apE6erHxc59D0sGEoPH9eeZ3X2LFwHz5c12Uq+xXduIE4jUrGwRs3Sverrs3KyUntfi44cwbxr7yidnmDk/e+cljbuEYTms7JgbjXVFvgokVwaN1aVw5lP1FFWFQTLm0iOB+qUnlac0BtlY5FhW67sLqvNGZMoenMhQuRNnOmkk988ES8H1TUdAlNi+tr431GfKDi7pAhkMXGlrvkoLVrYdewod73nOoFDE1Xi48X6yhQUVBaDCFCYCIMJkJhIhxmqNZ36ilE3clGw0B3jHnCsiuv5hfK8PFvhyTajWPbokMDN0MxcxwTEIjPKMSmKBGUTsLRmMwKV3x/mIcUlha/PJ0N93qsCaYBM8/gxI1MvPNUOwT7Gte/M9XEfssbMzUrH5OXHZOenjykJV6+r/J/l6nN9XGu8gUSMguxWXyIISoZh69lqHVsW98NA9r4Sh9yER8qMuU2ZftNTNl+C072tnjn6XbwdnM05e3U6trzCmWYtuYUxOu8tIV4OWDnu+2lADUbBShgnALhHx2C+KaAz59sjP90Y2jaOE+Jq6IABShAAQpQgALGL3Ax5SqGbBiFRh6haBvQCn9e3srQtPEfG1dIAbMVYGjabI+WG6MABUxNQDU0HTh/PhzasXqcqZ2hpa43e+1a5O7Zg/wjR1BSWKhkcOzYEc49e0oVpW3r1TNaHmMPTSeMHIn8Y4rQgGhuzz4L7w8+MLjnnUcfRXFqqnJc748+gttTT+k8j2Y402PECHiOGVPu9SJgLYLWpU3X0HTSuHHI3b373nWDB8Pn4491Xmdpx5KiItzS+HCK37ffwlmj4rE+A8sSEhD72GNql9RbuhT2EeV/xXr2+vVImTRJeY0u1an1WZPOfUtKcLNDB7XuvpMnw6VvX52HKO2Y+88/SBo/Xu26wAUL4NC2rdax0ufMgQgnq7b6+/fDytlZ77kNfYExhaZztm9H8oQJyi2KMHrIzp2wcig/dJL8ySdS1X/l+4dGdfTSx2vjfUbzNa/trOybNkXgkiV6fXhBcxyGpg39KuB4pQLXk/Ow/3I69l1Jx77L6VJoQLX5utqhb2sf9G3lg0fCaybg997qK1h6KB6+Hk74YEhHiz6cK3fS8fNmxbdWnPy0M0QVYTbzF9hzKU2qKL3xTLJUmbS85uJgg/5tfPFkOz90b+5lMjCf/HkN8/fFYXDXJnigpfH++1NNg95IyMTsdWekaXZ92AUtfBmkrGnz6o7/z8U0bI5KlgLTqn8+OtsrXosD2vqiZw392VjdtVf1+vu+OIo7aQUI8nHBqIGRUoCarXKB9QdjsO+s+oco17wRiQeaeFR+MXtQgAJ1JtDio0PS370mPRmG17oF19k6ODEFKEABClCAAhSggGkL3AtN10cb/wisu7INywf+hHBWmjbtg+XqKWCiAgxNm+jBcdkUoID5CaiFphcvhkOrVua3Se7ILAVKKxSLsKddkyZSlVCnrl3hqBHANNbNG3toOm3GDGQuWqTGJ1Vwfvxxg5LGjxiBgtOnlWO6Pf88vP/3P53n0AwziyCzq0Yl59LBSnJzcadPH7UK2rqGpkWlXVFxt7SJKtrBGzZUKWB797nnUHjlinIsEUINWLAAIrhZpVZcjJudOqldKgwqCnWnfv01slavVl5TZ6FpAHeHDkXhxYvKtTg//DD8pkzRm6Lo2jXEPfOM2nUijC5C6ZpN271gExCAkK1b9Z63Ji4wptC0tkrmlQXbYwcMUKvs7FZOaLqm32c0A9/irLw//BDWzs5I1vjQg/sLL8BLI3Svz9kyNK2PFvtWJnDqVhb2Xk6XwtIHrqZr7d67pY8yLF3TVWy3nk3Bq79dkIJZX7zSpbLlm/Xzu07dxpajN2BjbYXbP3Q1671a+uZi0wqkoLSoKn3iZlaFHJ0auaNfpK/0yxQr2a46loC3l19GsxAvvN7Pcv97wJ6oWGw8FIMGAe44+H4bWFn6i8BI938jOQ+bo1KksPTpW+qvzXb13aSwtPgV6m2eVZjPx+ag1xTFtwp1ah6A53o0M9KTMp5lXYvLwE8bo9QWNL5PfYzv08B4FqnnSs7FZmP233ekq0Y/EoLWIeb/LQFHYjJw+nZ2uVIiUGvNN2497yTj797sw4PIzi/Gp0+EYWR3hqaN/8S4QgpQgAIUoAAFKGCcAsrQtGd9RPqFY/2V7Vg+8EeE+1Tx/8sa5za5KgpQwEQEGJo2kYPiMilAAfMXUA1N11u+HPbNm5v/prlDsxDIP34cNr6+UljaFJuxh6aLU1IQ9+STagFj4ew5ciRcn3kGIjRsiCYq2Ipgo2oL+OUXiIrhak0uhywuDiLcC6t7/ycs9fvvkbV8ubKr04MPwn/GDMDaWv3yzEwkvvkmCs4qqkOWNl1D06KyuahqrdpcHnsM3u+9B2sP/apTieroCaNGqY0lgtNe//ufVGHZyl7/qpWaIWgxeHkh97xDh5A4erTa/HUZmk7+6CPkaISVvd55B25DhsDKzk6v20yccd6BA2rXeLzyiqL6+L/3jVzcC2+/rRbWFxd4T5gAN43QtV6TG7CzMYWmIZcj7qmnUHTzpnKH4vXvP3s27Fu0UN91SQkyfv0V6XPnqj1eXmi6Jt9nZLGxEOFt1eZ4//0ImD1ben/Q9t7jP2sWxHtIVRpD01VR4zWqAiIovSs6DbuiUyH+WVsTYbA+rRRh6WYBtVcVX1TvFF+NLdqnwzrDzVn/P6fM5bSX/n0Rp68mob6PIw5/pP6BJXPZo6Xv4+/o1H/D0snIKSgul0O8HkVYuldLbzzYxNOk2RIyC9Hlq2PIL5JjzJNt0DDA3aT3U9XFz9tyDhdvp2FAx2D8PDSsqsPwuhoSkCpK/1tVuqi4RDmLeC0+Eu4lfduC+GdLaCuPJeCd5ZelrfbuUB+9O5pu+LemzyuvUIbJvx+D+L20PdspANOfN92wuUxeIv29rPTPKPEtB9FfdYGtmSeGn/3pLPZf0f5hQnG2N7/vCjsbpqZr+jVV2+M3nXBQutcnDmyE//YIqe3pOR8FKEABClCAAhSggJkIRKdcwfMb3kCYZ3208m2BDVf/YmjaTM6W26CAKQowNG2Kp8Y1U4ACZimgGpoOWrMGdmH8n4NmedDclNEJVDs0LZcjbdYstX3lbNuG4oQE5WMObdvCoU0btT7uQ4ZAVNTVpeVs2oTkiRO1dnXu0UMRYLa1hY2nJ+TZ2VKoufDSJTg99BC8xo7VZQpkrVyJVC2VgJ179oRdgwYokctReP488o8dk8YL2b4dNn5+yrGz169HyqRJanOJ+UVIU/QTayo4dw65O3YoK9+KgLI8J+eeU+vWsA0JgV3jxvAYMULrumW3byP2iSfKPCfGEkFM28BAWLm6okQ4JCaiOD4eLgMHwu2pp7SOl/LZZ8jesKHMcyKM6ti5M2zr1QNKSmAl1pqVhaLr1yEqKYtKuMJes2kLiIo+otKyQ0QErJycUJKfj7z9+yE+cKDZ6jI0nbNlS5mqv2J94j51bN9eshX3mQg7i/tb7NXns89gHx5eZh+iYrWoXK3ZxL1k10zxP8bzDx4s82EAMVfw+vVVCqzrdKNr6ZS5bBmKk5K0Xi6qO6tWYBfrE4F6zSYMXHr3Vnv49kMPqe3Pf9o0OHXvrv2+jo9HrEb1+OCtW2Gr8R4h3luSP/ywzBjS6ywgQHqtiXtehN9Vw9WlF5QXmhbP18T7TIlMhoRXXy3zIYngLVsU9xMg3U9xgwejODVVuS/xeg5atw42Pj56HytD03qT8YJ/BRbsj8MfxxPLDUq3qOeCXhHeeDTCWwpo1lXr+PlRxKUX4PHODfFI29C6Wkadz/vD6hOIT81Fl8Ye+GN0ZJ2vhwswvEDQuH1aBxUV3UUos0dzL+m12MDHvKrYTlx3DfP2xuH+8EA83c3yquxk5xXh86VHIJeX4Ktnw/HK/b6Gv7k4YpUFnpoThUPXMpTXi9dgt2aeFhWU1sSbuC4G8/bGSg+LatOi6jRbWQFRYVpUmi5t/+kWjM+fNO3/7ik+XNdv+r1v6xJ72/x2W7P/0ABD0zX3Cs/Mk0H4imZtbYVfhocjxMuh5ibUY+QmHxxEbmExPhnQCNJIkhMAACAASURBVKMeZmhaDzp2pQAFKEABClCAAhRQEVANTUf4NsemqzuwbOCPiGClad4nFKBAHQgwNF0H6JySAhSggDYB1dB08IYNUnCQjQIUqHmBaoemi4txs5P+FQ7rLVtWtjpsedstKUHiW2+VqdxbmY5Tly7wnzOnsm7S8yW5ubjTp0+ZEGt5FwcuWgSH1q2VTxcnJyNu0CCdr3fs1AklRUVlqgyLAUVgOWTnznLXnfHLL2Uq6Fa0yYqCovKMDKl6r2pgUxcwEUZ3Hz5ca1dRCVtUxNal2TdtisIrV5Rd6zI0LcLhCSNHag1zl7cX36+/hkufPtodPv0U2Rs36sKg7OP7zTdlwsd6DVCFziLcLULe1WmuTz4JH40PNtREaBrFxYh7+mmtgWht69e8vyp6LYjzN/T7TPpPP0kVr1WbCNqLyvKqTXwYQ9x7qk16/xIfSNGoVl/ZOTE0XZkQn1cVEFVdlx6Kx+pjCbiVml8Gp0mAsxTMFEFpEQozhvbEzDM4diMT9f3d8NagtsawpFpfQ1ZuISYtOSLN+8OzTTH0fsWHMNjMS+CTP6/hbkYhGvk6opGvExr5OaGhryPqeRhHeKimtKPuZKPv1FPS8P97tgMCvGqvmn1N7UmfcY9Ex2P13isI9XPF/vfbwU79S2v0GYp9a0DgvdVXEJtWgG7NvdC9mSfEB4rYgLeXX8aqY4oPTY8aEInGQfp9A5K5G/51/Cb+OnFLuc1pzzfDc51MP1xeLC9BC41K0xe/6gIbM680feBqOm6nFCjP8/TtLCw+eFf5MytNV/0VnZxdhMiJh5UD/DkmEp3DjOP9JOz9A9I3YXzUvxFGP8L/Z1H1U+aVFKAABShAAQpQwLIF7oWmGyDcpyk2X9uJZQPmIMLXdL+FyLJPlLungGkLMDRt2ufH1VOAAmYkoBqaDtm6VecKtGZEwK1QoE4ETCI0LWSKiyGqOadNm6ZzMFlUnhXvJ7o2EfQVgV9dmrZwa96hQ0gcPbrSy0UV2YBff0X6nDlag+CVhaZLCguR+OabyqrXlU0oKlAH/Phjud1EdW4RxM5curSyoZTPuw4eDJ+PP9baX4TBxTllrVhR4XjifPy+/hrxKlW16zQ0DaDoxg0kjBqlVim9ok14vvkmPF55RXuX4mJkzJ+vU8Bd3BO+kydL1dFru5lUaFq8FaSkIOm991BwShGoKq85desG5+7dkfLFF8ouFYamDfw+IyqpJ7z+utryxPn6T58OWJX9uua0GTOQuWiRWn9R0d39hRf0uiUYmtaLy+I7Pz0nCgdVKmYKkPsauaNvax/c39gDbUPdjM5o1JKLWH9KUR1fhKZFeNrSWvStVMzfel7a9oUvu0BUHmajgDkJPDbtNM7czkLXVkF48sHG5rS1Svcya91p3EzIwv8GtsA7Pe59q02lF7IDBepYoLQKt5O9LUYNjESQDwPl4kjO3UjBwu0XpNNxd7TFpEFhZhGYLr3dou/mYM6uO9KPIkgaboEfJNh+LgWvLFCcsWgMTVf9zciYQ9ON3juAApkcH/ZriDE9Lffbbqp+urySAhSgAAUoQAEKUEAIXEi5jKEbRiPMswFa+DTBlmt/MzTNW4MCFKgzAYam64yeE1OAAhRQF1ALTe/cKVVaZaMABWpeoNqhabkcNzt21HuhQWvXwq5hQ72vK05LQ8a8ecg/cgRFMTEVXi+Fj//6S69KrQWnTyNl0qQKK9mKCtOeb7wBx86dy8wvQrcZP/+MnO3bta7NuVcveL72GuyaNEHS++8jd8eOMv0qC01LF5SUIGfrVmStWaO1WrXqoHZhYQhas6ZS66KrV5H+668ovHABsljFVyyX11weewy+X31VYZ+cLVuQsWCB1nNy7NgR3h98ANvAQNzq2lU5Tl2HpiXa/HxkLl4s+RbdvFnhHt2HDoXXu+9W2Ef8+Zb+449aQ77irB06dID3+PGw8fev9IxqooMI9oqAb3WaNofYAQPU7iP/adPg1L271mnE6/pOz55qz4Xu2gVrT+2VbUtkMmTMnYvszZu1Btxd+vWT7q+CqCi1DzJUGpr+dwXVfp8pKUHswIFq+xfBePG+Z+OnPQAlPgwRP2yYWuV1sZyQHTtg4+Oj8/EwNK0zFTsCuByfi41nkhDg7oAW9ZwREeQCZ3sbo7aZvPkGZv99W1pj7w710btjA6Neb00sbs3eKzgcHY/HI30x7+XwmpiCY1KgTgU2nknGyEXR0hree64D/D0to9r0ySuJWLbrEmxtrBE9+UG42NXpMXByCuglkJknw+A5UbgQlwMGpxV0cSk5+GlDFPIKZVJg+o/RkWgZzDC5XjeWCXRmaNpwh2TMoekG/9uPouISfPB4Q7z1KEPThjt1jkQBClCAAhSgAAUsS+BC8mUM3TgajT0boJl3Y2yN2YXfB8xBS1aatqwbgbulgJEIMDRtJAfBZVCAAhRQDU2H7tkDazfLqxrHu4ACdSGgGZqubA2BCxbAoW3byrrVzvPFxZAlJEB25w5EiFJUo7ZydISNlxds6tWDCClWtckzM6XArAggioCytasrrN3dYRsSAiu7yhMMJXl5irXdvQsra2vFtUFBsPa499WixYmJKCkoAOztpTGlX//+M6x1/y5uEfItvHoVYs1Sk8kkBytnZymULH0IRY/xxBAiwCmLi0NxUhLEXmBjAysnJ9j6+UnfBCDWqWsT6yu6dg3FmZmwdnKSzsY24N7XEYuguXhcrNfa2Vmay1iaCM/KbtyAqMYtrevfe0yco7AV56pzk8slU1l8vHSJCLPzA0I665XbUZ6RId3/0n3v4gK7+vWV5yLuPXEPi3tX/BL3mb6vBXHmNfU+U9XdSxXM58zR+fKg1ath19iyKnXqjMOOJidw6lYW+k0/La3b1ckObw9uB09XB5PbR1UXnJlbiCmrTyAnX4bZLzTH4A5184Gbqq6f11FAV4FxKy5jxdEEtG3shxcfbaHrZSbdb+6ms7gam46HW/rh91ctY88mfWBcfBkBBqfvkYjAtKgwnZqVL30obcbzzU0+MJ2aU4RL8bnl3vnig3dtQl3Lff7wtQyUaDwb7OWA+t6O0qNHr2dC/D0vNbsITQKc0TnMXflc6WWGGENzgbdT83E+Nge3UvNRKJOjZbCrdFb+brr9Nw9DhKZl8hJcTcjF2dgcJGQUwMXBRrGOIBfpn7W1M7ezkVtYLD0V6u2IEK/y/z58NTEXSVlFUl97Gyt0aKj+3zGWH4mHFaxgY2MlPS++xSQ8SDeDYnmJdHaiie9TEt9WIy8B/o5OxcW7OcjOL0azQGdpzoY+irNWbar7SMoqxH8XX1Q+PenJMLQK1n5PdWl877+t1cbbcf1390Oc0/uPNcDYXvVrY0rOQQEKUIACFKAABShghgKqoemm3mHYFvMPfh8wGy19m5vhbrklClDA2AUYmjb2E+L6KEABixFQDU3XP3QIVg6WE36wmEPmRo1SwKRD00YpykVRgAIUqFmB9NmzpSruujaGpnWVYj9TEejx3QmpSrZoj7QNxeOd9f/mClPZq+Y6D5yPw5/7r8HRzhonP+0shVrYKGCOArui0/Dir+ekrY3o2xIRDcz7m6jOXU/Bwr8uSPtdPrIVujf3Msdj5Z4sQIDBafUK0yIwvXZ0JNydTP/P6/WnkjBqyb1Aq+btXM/TAScm3lfuXR40bl+Z5/pF+uLn4eEY9us5/HMxrczznz0Rhte7BysfN8QYpYPFZxTi5fnnEXUnW+uaRzwUhE8HhsHORkSBy2/VDU2LwPFLv55HZr6szCS21lb4/tmmeO6+ex86L+3UdMJB5BQoQtMikLzxrTblLrLdZ0eQkFkoPd880Bn/vNdB2Te7oBjNJhzUeq2okD6kcwD+17dBueHtrPxiNP/w3vXi76cDZp5GbFpBmTHFWX48oBHEvkpbh8+P4m562b6VvV3GTnkIVhUfTWVD6PV8yPj9kJeUSBbv9GZoWi88dqYABShAAQpQgAIUUAqcT76EFzaOQWPPhmji1RDbr+9maJr3BwUoUGcCDE3XGT0npgAFKKAuoBqabnD8uP6VIAlKAQpUSYCh6Sqx8SIKUIACdSbA0HSd0XNiIxH4cdcdfLnpurQaR3tbjB3cFn4eTkayuppdxoItZ3HhdrpUYVpUmmajgDkLvLvqCpYdjkeIryveHNQWNiohK3Pb94Jt53HhZioGdayHOUObmNv2uB8LExDB6U5fHENWvgxO9rZ47uFmaNXQxyIURIXpnzZEIa9QJlWYNpfAtDi8mghNh9dzwbP3BWDS+phy74/9EzoizE/x9zxtoWl9xxDjnLiZhUGzzkiVgytqTfydsfv9Dqjoj5/qhKYX7IvDx39eq/S1IQLcXw5S/+YgcZ24vrRFf9UFHlrC+VcSctH92xPKfppB9MsJueih8ry2xYhq13ve74Agz7IFTjRD0+JDP3sulQ3Al46rGTo2ldB08Lh9UqX0d/s0wLg+DE1XetOyAwUoQAEKUIACFKCAVoHS0LQITId5NsBf1/dg6YDZaMVK07xjKECBOhBgaLoO0DklBShAAW0CytC0jQ0aHDtGJApQoJYEstetQ8b8+TrP5j9jBuzCwnTuz44UoAAFKGBYgczFi5G1erXOgwYuWAAbPz+d+7MjBYxdQFSZFtWmS1u31sEY+ID5/93kRkImZq87I21bBKZFcJqNAuYskJJdhE5fHEV+kRzdIoMxsIt5vs43HIzB3rOx8HC2w5axbdDo33CgOZ8t92b+AudjczBoThSy/62e+1yPZujUvGy1XHOSUA1Mh3g5YOe77c2iwnTpGZ2LzcaC/fdCuuLxozGZiEnKk7pUVmlahHzl8hKpAvGOC6nSNSKM28DHERfictA5zAP3NXLHxjPJuJGsGFO0lx6oh2+eVnyYxBBjiHG6fn1cuW7xswhHd2rkjhKUYPu5VKTlFCnn//aZJhjWpV65t2pVQ9NJWYXoMOmoWnC7a1NPNPF3QmqODFuiktWe2/tBB2mdpS06Lgc9fzip/PmHZ5ti6P2BZdb5w7abmPrXLeXjZz+/Hz6udsqfxfl9vfkGCmRy6c9bUXlaVH4urUxd2rFVsCu2jWtXJkCuGZou7d++gRsa+jph5/lUtSraosr0ha+6wNXBRuo6c+dtxGcoKk2Lytmrjycq1/ZohDfEa0lbm/xU7X3AqKQECB6vqJQuAtMiOM1GAQpQgAIUoAAFKECBqgicS76EFzeOkapMN/Kojx039mJp/1lo5deiKsPxGgpQgALVEmBoulp8vJgCFKCA4QRKQ9NWjo6of1D71wIabjaORAEKUIACFKAABShAAQqYqsDQn89h979V7GxtrKVq0/W8XUx1Ozqt++/j17H1xB0pVPTnmEidrmEnCpi6wJTtNzFluyLs9VKvcESG+Zr6ltTWL8LSIjQt2of9G2LMI6FmtT9uxrIFVh5LwDvLLysRzDk4rRqYdne0xR+jI9Ey2Lz/XiIOVoRtZ/19WzrjykLTpTfC0euZeHKW4kNgookQ7ZieoXjvsXtB1N5TTkGEtEXT9vee6oyhWTF7Qr+GeLPnvffe3MJiDPv1PA5dy5DmF8HuK18/UO6Luaqh6XdWXMbKownKccU906Wxh/LnO2kF6DP1lDLA3bulDxa+GqG2DvHBIhFCL89JPP7A5OPKEHqHhu7Y+FYbnd6YRKj7y43X1ULMS19rhUfCvdSu1xaaXvSflugV4S31E4HjCX9cxeKDd5XXrX+zjRRS12zJ2UWInHhY+bD4+644/7puxfIShL67X1rGO73rQ1TLZqMABShAAQpQgAIUoEBVBFRD0w3dQ7Hz5j6GpqsCyWsoQAGDCDA0bRBGDkIBClCg+gKloWlrd3eE7t5d/QE5AgUoQAEKUIACFKAABShglgJrjifirWWXlHt7IKIeBj9UexXnahs1IS0XP288g8w8GX59ORz9Is0rOFrbnpzPdARk8hL0mXoa0XHZ8PNwwn8HtIaHi/aqk6azK8VKbyVmYeafp6V/blvfDRveaiOFB9koYE4ClhCcziuU4acNURDBaUsKTIv71BCh6QB3e5z6rLPabS/Cuj/+c0d6rL63Iw5/3Entec3QtD5jDJ93XlnpurwQsai03OHzo8o5z0zqDD83e60vzaqGpptOOChVVhbt3b4NMK53/TLjrzuZhDeWXpQe17ZHUaX5my03lNddmvwA3BwVFZxFi0svQEeVfUwb0gzP3adfxfdu35zA1cRcabz3H2+IsY+qf7hHMzQtwtIiNK3aLsXn4mGVb4mZ+lwzDOlcdh3GGpqWFZeg/v8Uoem3e9VXC/ib0/s190IBClCAAhSgAAUoUPMC55Iu4sVNb6KJVyPUdw/Grpv7saT/LLRmpemax+cMFKBAGQGGpnlTUIACFDASgdLQtI2PD0J27DCSVXEZFKAABShAAQpQgAIUoIAxCnzy5zXM33fva+Jfe7wVmoeqV78zxnVXZU3r9l7C/uhEta+or8o4vIYCpigQdTsbQ345h/ScIrQJ88WwXuGmuI0ya/5s8WFk5xVJj899qQUGtvUzi31xExTQFPjtYAI+WmOeFactOTAtztkQoWltIdTzsTk4eC1dupVCvRzRt7WP2m2lGZrWZwzVEHBFIWLVKs7r3myD+7RURhaLqkpoukAmR6P3Dij3dGLifVKlbs2mGUiOnfIQrFQ+W5OQWYh2nx1RXqa5n7m77+DzDdeVz4uK2aJydnmtqLgEsekFSMgogI21FRr6OuGHbTeVVaLFn1PizyvVprnGrwY3xitdg8pMIfYr9i3axIGN8N8eIWX6GGtoulAmR8N/z0uExkV4nI0CFKAABShAAQpQgAJVETibdBHDNr2Jpl6NEOIWhH9uHcCS/jPR2s88/ltXVUx4DQUoUHcCDE3XnT1npgAFKKAmUBqatg0MRPCWLdShAAUoQAEKUIACFKAABShQrkBeoRzPzT2L4zcypT6+7k54vX8reLs5mpXauZgkLNyhqDIoKjGKSoNsFLA0gf1X0vHsT2elbZtDcFpUmBaVpkUb0zMUH/ZjAMvS7mlL2++sPQn4er15BactPTAt7mFDhKaXvtYKj4Tr96E3zdC0PmOohneDvRzg5Wyn9eV4LjZb+fj055vh2U7aKzRXJTStWXm5VbBruW8JquvQ9vfAx6efxulbij9PujT2wB+jI5Vj9Z5yCqXXC2PhpNlEUHrN8QRM/esWYtMKKnxr6t7cC8tHqo+hGZoWVaZFtWnNplpZ29RC0/lFcoS9rwi5v/VoKD5gaNrS/gjjfilAAQpQgAIUoIDBBFRD08Fu9bD71kGGpg2my4EoQAF9BRia1leM/SlAAQrUkIAyNB0aiuD162toFg5LAQpQgAIUoAAFKEABCpiLwOFrGVJwWgQ+RBOVpkXFaXNp+YXF+H7lMWTkFmF8nwYY36fsV7eby165DwpUJrAlKhn/WRgtdTPl4PRfx2/irxO3pH0Mf7Aevn6qSWVb5/MUMAuByTsSMHureQSnVQPTEUEuWDs6Eu5OtmZxTvpswhCh6b/Gt0NFoWFt69EMTeszRtC4ffpsUepr6ND0oWsZeGpOlN7r0BaaXnEkAeNW3ntdlVaTTs0pQqtPDivnWPBKRJmK3bmFxeg3/TREiFuXpktoWoSqRT/NZsqhaeHU5IOD0pbe7BmKCfygky63C/tQgAIUoAAFKEABCmgROJsUjWGb3kJTrzAEuQZgz+1DWNx/JiJZaZr3CwUoUAcCDE3XATqnpAAFKKBNoDQ0bRcWhqA1a4hEAQpQgAIUoAAFKEABClCgUoGfd8di0oYYZb8HWwZhUNfGlV5nCh22HryMv88mSNWlt41rxyrTpnBoXGONCszfH49P1l6R5jDF4PTGQ9exJ+qOtP7B7f0x+8XmNerFwSlgbAKfb0/A3O2mHZxmYPreXWWI0PTJTzsj0EO/b9HQDE3rM0bLTw4jLadIuQl3x8rD7j+91AIPt9BeDbsqlabjMwrRftIR5RocbK0hflXWjn96H1wdbNS65RQUQwSSS9uMoc3xTEd/LDl0F++vvio9bGtthWvfPgg7Gyu1az/84yoWHrirfEz8fbNbM0/4uSnOIza9AHsvpyu9ajs0LT6McH9jj8pYavx5VePRj4Tgo/6NanxOTkABClCAAhSgAAUoYJ4CUUnReGnTW2jmHYZAF3/svX0Yi/vNRKR/uHlumLuiAAWMWoChaaM+Hi6OAhSwJIHS0LR9ixaot2yZJW2de6UABShAAQpQgAIUoAAFqiHw2foY/LInVjnCwC5h6BYZXI0R6/7SE9FxWL73mrSQqUOaYch92r8Wvu5XyhVQoHYF3l19FcsOKUJephScXrP3Kg5HK9bdM9wbC1+NgI21eoCtdiU5GwXqRmDitgTM+8s0g9MMTKvfM4YITZ+Z1FkZ0tX1jtQMTeszxqDZUTgSkyFNNai9P+ZU88Mru6LT8OKv55RLPz2pM/z/DR1XtJ/67+6HTK74ppSKKlnrYvLy/Av463yK1LVrU0+sGtUaqvsc3MEfs18o+yGdRu8dQIFMLl03rEs9fPtM2W8+EFWsRTVr0Wo6NJ2VX4zmH94LgP88PBwD2vjqQlCjfVTX9cYjIfiYoeka9ebgFKAABShAAQpQwJwFVEPTAc5+2HfnCBb3m4FI/whz3jb3RgEKGKkAQ9NGejBcFgUoYHkCpaFph9atEbhokeUBcMcUoAAFKEABClCAAhSgQJUF3lp2CWuOJyqvH9G3JSIaeFd5vLq8cN/pm1h/5Ja0hO+eaYoXuwTW5XI4NwWMTuDpH8/i4NV0aV0iOP3Eg43h7qxfpdLa2pSsWI5Vu6/g5FXF+1PHhu5Y/J+W8HSuvLppba2R81CgtgU+2pqA5XtikF8ok6Z+rkczdGpu3B8OUg1Mi+rEO95th1Bvx9qmM6r5TDE0/fGf17BgX5zSccNbbaT35aq26Lgc9PzhpN7jdf36OGKS8qTrfF3t8M97HeDjalelZfwdnYphv55XXiuC220/vVfJes0bkXigiXrFZs0K1evfbINOjdQdSkqANp8eRnK2ojJ3TYemxRyqYfIRDwXhy0F1/+0xGXkyhH90SDIY9XAIPhnAStNVulF5EQUoQAEKUIACFKAAohIv4KXNY6VK0/7Ovth/5ygW9ZuBNgxN8+6gAAXqQICh6TpA55QUoAAFtAmUhqYd27dHwLx5RKIABShAAQpQgAIUoAAFKKCXwIu/nseu6FTlNaYQwtLc4IEzN/HnYUVgenyf+hjfp4FeBuxMAUsRGL/qKpYfVlRuDvByRr/OjYzugxKpWflYu+8qLt5Ok9bZMsgF80dEoL6FBy0t5R7lPisWmH0kB1P/jDKJ4LR4LS/cfgFxKTkQgek/RkeiZbCLxR+xKYamY9MK0OWrY8oqz7bWVvjsiTC80CUQDrbWamdaLC+BqAUt+pTXVAO1ok/zQGdsG9dOOZao5HwuNgdtQ13Vvl1g45lkjFwUrRy2nqcDJg9ujN4tfWClMV2hTA57jbWprkess8kHB5VVo0W4ec8lxZ87Lg42uDz5gTJjiiLXIeP3KYcZ0jkAU59rpvxZjDl66SVsOJ2kfKw2QtOqYXIx8dyXWmBgW786fa2l5crQ8mNFaPq/PUIwcSBD03V6IJycAhSgAAUoQAEKmLDAmcQLGC6FphvDz8kbB2KPMTRtwufJpVPA1AUYmjb1E+T6KUABsxHIP3IECaNGwaFtWwQuWGA2++JGKEABClCAAhSgAAUoQIHaE3j6xygcvKr42nXRukTUw1MPlf268dpbke4zHYq6iT8OMTCtuxh7WrrAT7tj8cWGGCVD7w710bujcXzQ4NilBBw8F4vbyTnS+kZ0DcL/+jaABytMW/pty/2rCEw/lIOZ6407OC2C0j9tiIKoNG2pgekbKfl48ZdzZe7dhMxCiIrFpS3Mz0mtz3uPNZACr3N338HSQ/HSc6K/uK60iQ+R2NooUsKi4vP05+8Fd1UHM8QYpeP99M8dfLHxutpaRTA6yNMBAR72KCiSS2sUv6YNaYbn7qu4CvoDk4/jRrKianRpEyFokbUWIW3RDn7UCQ191CuTP/vTWey/ovjWhNImgtuigrmoOp2VL8Od1AJk5stwYuJ9EGOW195ffRVLDik+SKTaXukahK8Ga6/W3O2bE7iamKvsHuzlgIeaeSIlqwhHYjKleQPc7dXOSzgJo5X/bQ1x3ln5xWj+4UHlGMtHtpIqUmu2phMOKu8VEToW4WNtTZzz5xvUz0aYiLXZ2VghJbtIqnz924gI9GnlU66HIZ9IzSlCq08OS0OO7B6MT58IM+TwHIsCFKAABShAAQpQwIIESkPTzb0bw8fJCwdjj2NRv+lo49/SghS4VQpQwFgEGJo2lpPgOihAAYsXyNu/H4lvvQWHVq0QuHixxXsQgAIUoAAFKEABClCAAhSomsCY3y9h7YlE5cWtG/lieO/wqg1WC1cVyeTYczIG204pwi6sMF0L6JzCbAT+vpAKUXU6MVMRTIto4C1VnRbVp+uinb2egkMX4nD5jiII18TfWQpLD2jrWxfL4ZwUMHqB+WdlUihZhJNFM6ZviWBgWnH7XLybg0e+P6n3vfT5k2H4T7dgfLT2Gn7bH1fp9a2CXfHX+HZa+xlijNKBRZXlieuuYcG+ytc0+pEQfNS/4srCm84k43WVqtHaNqAtTCzCv6MWX8SBq+rBaW3Xr36jNR5s4lmu4dk72egz9VSZ54WncNXWjsRkYNDsqArPZd2bbfDkrDNl+mx9px3ahLoaPDQtzkZUm9YMoWsuoKLgdaU3mp4dxDlFTlSEpl/rHoxJDE3rKcjuFKAABShAAQpQgAKlAmcSz2P45rchQtNejp44HHcCC/tNR1uGpnmTUIACdSDA0HQdoHNKClCAAtoEcnfvRtK4cbBv3hz1li8nEgUoQAEKUIACFKAABShAgSoLiNC0CE+XtiZBUQFS3AAAIABJREFUnnju4Wbwci2/Sl+VJ6vGhXcT07Dh0HVciVeExRiYrgYmL7VYgZikPLy35hoOXkmTDNyd7dGzfShaNfSFh4t9rbhcjU3HgfNxEKHp0vbC/YFSYNrfvXbWUCsb5SQUqAGBjVdlmLTSuILT+gSmp2y/hfOx2fjg8YZoFlg3H9iogWNRDikCrKKasr7t22eaYFiXepi0PgY/74mt9PL2DdywaWxbrf0MMYbmwJcTcjFhzVWcvJmFAplc67yiyrSoNl1ZW3cyCe+suKx1HFGh/OfhLbRWYBbj7jifikkbYnArJR8ykRrW0ma/0ByDO/hXuIwOnx/F3XTFB4hEE1WiT33WucJr9l1Ox4Q/rkL8OaraRHXnj/o3lELv9d/dX2ZdpaHp3MJiNPmg8krTLT85jLScImkKUflaVMAur4kxJ2++gZVHE9Qqmav2H/FQEL4cpL2CdmVnpe/ziVmFaPvpEeky4SE+DMBGAQpQgAIUoAAFKECBqgicTjyPl/8NTXs6euBI3EmGpqsCyWsoQAGDCDA0bRBGDkIBClCg+gK5O3Yg6f33Yde4MYJWr67+gByBAhSgAAUoQAEKUIACFLBogaMxmXh5wXmk58okh2BfV/Tt1ADh9b3r3MXWGjhx7iaW7LulXAsD03V+LFyAiQssPRSPBQfu4mJctrQTBzsbtG7kI4WnWzXyqZHd3UzMwvGLd3EoOkE5fqi3oxSWfrpjxQG3GlkQB6WAiQocjpVh8sYYHL+seC3VZcVpfQLTYq1vL7+MVccSpMD0Ly+Fm2Vw2kRvK52XnZEnw9WEXOTL5LC2soKnky3q+zjCxcFG5zFEx7j0Ain8XFxSAg8nWwR5OsDbxU7nMeIzCnEnLV8KCzvaWcPPzR7izxQ7Gyudx6hKx9ScItxMyUdBkRw+rnZo6OuknPN2aj5sra1gb2strUn8XtPrKd3DrdR8xKYVoFheAisrwNXBFsFeDvB11d20Kh6q14gzaT9JEZp+9aEgfFFLYe3qrpvXU4ACFKAABShAAQoYn0BpaLqFdxO4O7jh6N1T+O3xaWgX0Mr4FssVUYACZi/A0LTZHzE3SAEKmIpAzpYtSP74Y9g1aICgP/80lWVznRSgAAUoQAEKUIACFKCAEQuI4Mr7a65izyVFBVrRIsN88UBEPTQJLv9rzmtqS/bWgCw3C+sO38LeS6nSNKIK7Zs9Q6UgBhsFKFB9ARGenr//Li7dVYSnpdeZp7MUnI6o740AL2c4OdhWaSJZsRwXb6fhWmw6rt9Nx52UXOU4LYNc8EKXehjQxlcKnbFRgAL6CVzPBMYuu6wMTndqHiCFp2uz6RuYFmu7FJ+L1xZG42piLoPTtXlYnIsCtSAgqneLKt6ijegahC8H106F61rYGqegAAUoQAEKUIACFKhlgdMJ5/HylrfRwqcJ3OxdcezuaYama/kMOB0FKHBPgKFp3g0UoAAFjEQge8MGpHz2GWyDghC8aZORrIrLoAAFKEABClCAAhSgAAVMXaBQJsfUv25h/r44ta/57tDUHw+0rIcGAe41vkXbEhluxSbj+NVk7FUJcA+5LwBvPhqKRr5ONb4GTkABSxMQ4enFB+/iXOy98LQwENUqRYhahKfFryAfFwT5uMLbzQHi/aJI+lWs/GeUFCMlPQfX4jJw5kaa2vuIrY0VOjVyx9ieoejW3MvSiLlfChhcID4HmL47AYv/viyNXZvB6aoEpksBzsfl4PWF0bienMfgtMHvCg5IgboTEJWuO32hCE2/0jUIXzE0XXeHwZkpQAEKUIACFKCAiQucSjiHV7a8I4WmXe1ccDz+DEPTJn6mXD4FTFmAoWlTPj2unQIUMCuBrD/+QOpXX8HG3x8h27aZ1d64GQpQgAIUoAAFKEABClCg7gUux+di3r44LD10V20x94cHIqKBD5qGeMLOxtpgC7W2AjLSM3D+ejL2X0yG+Hrv0tYq2FUKS4uKtGwUoEDNChy9nomjMRk4HJOBXdH3qs5XZ1Y/N3s808kfz3YMkAKSbBSggOEE0guAH/clYPaW2gtOVycwXbrzqNvZeG1RNG6n5jM4bbjbgSNRoE4FxOu585fHpDW8/GA9TH6qSZ2uh5NTgAIUoAAFKEABCpiugGpo2sXOGSfio7Dg8aloH9DadDfFlVOAAiYrwNC0yR4dF04BCpibQNaKFUj97jvYeHsjZOdOc9se90MBClCAAhSgAAUoQAEKGInA4WsZUnh6S1Sy2opcHG3RPNQbzUO8pAC1u7O93ivOyslHYW4uMrNycTImFYeuZZQZ461HQ/Fmz1C4ONjoPT4voAAFqieQmS/D0ZhM7LmUJgWoRVXYylrTAGf4udmhd0sfqSp8Ax9H1PdxhKOd4T5kUdka+DwFLFFgz7UcvDovCrkFMqki/KiBkXCytzU4RV6hDJN/Pwbxu7ujLf4YHYmWwS5VmufkzSy8vigacekFDE5XSZAXUcC4BG6l5OP+rxSh6ZceqIdvnmZo2rhOiKuhAAUoQAEKUIACpiNQGpoO92kKJ1tHnEw4y9C06RwfV0oBsxNgaNrsjpQbogAFTFUgc+lSpE2dCms3N4Tu2WOq2+C6KUABClCAAhSgAAUoQAETERAVIfdeTsPey+nYfyW9zKpbBLmhbSMvFMqtUGJlDWsbGzjZW8PRzgZOdtZwtrdBfFouMrJyEJuci5ikXKRkF2nd/SPhXni4hTfE7yJ0yUYBChiHgExegvwiucavYulnUXk+IsgFdjZWxrFYroICFihwPjYHg+ZEITu/ZoLTIij904YoiErT4vX+24gIhHo7Vkv62PVMKTidkFnI4HS1JHkxBepe4EZyHh6YfFxayLAH6uFbhqbr/lC4AgpQgAIUoAAFKGCiAiIkPWLLOIjQtKOtA0SImpWmTfQwuWwKmIEAQ9NmcIjcAgUoYB4CGb/9hvRZs2Dl5IT6Bw6Yx6a4CwpQgAIUoAAFKEABClDAJASSs4rwd3Sq9Gv3xTRkFxRXe909I7zRt5UP+rTyga+rXbXH4wAUoAAFKEABSxQQwemxyy/hQlyOQStOawam146OhLuTYSpZi2+1eG1RtPRhqmaBzvjlpXDpdzYKUMC0BGKS8tD1a0Vo+sUugfjumaamtQGulgIUoAAFKEABClDAaARUQ9MONvY4nXge8x+bgg6BkUazRi6EAhSwHAGGpi3nrLlTClDAyAUyfvkF6XPnwsrWFvWPHjXy1XJ5FKAABShAAQpQgAIUoIA5Cxy6liGFs9JzZSiWl+BuRgHuphfgbkah9M8lJYC/uz0C3Oyl3xW/7JQ/P9jUE7bWrE5rzvcI90YBClCAArUncDs1H71+OIXMfBmCfV0wvHcEvN2qVxH6p41RuBaXIVWYNmRgulRFfIuFqDgt/i7B4HTt3SuciQKGFLiamIdu3yhC0y/cH4jvn2Vo2pC+HIsCFKAABShAAQpYksDJ+LMYsXUcInyawtbGDlGJFxiatqQbgHulgJEJMDRtZAfC5VCAApYrkLd/P1K++grFCQlocPKk5UJw5xSgAAUoQAEKUIACFKAABShAAQpQgAIUoICagKg43WuK4r8ZOjnYYtSASKnydFXayt2XcexSAtwdbbHj3XYI9a5eALu8Ney5lCYFp7PyixmcrspB8RoK1LHAlYRcdP/2hLSKoZ0D8cNzDE3X8ZFwegpQgAIUoAAFKGCyAifio/Dq1vGI8G0GGysbnE2KZmjaZE+TC6eA6QswNG36Z8gdUIACZiSQvXattBvXwYPNaFfcCgUoQAEKUIACFKAABShAAQpQgAIUoAAFKFBdgZXHEvDO8svSMC4OthhZheD02r2XcTBaEZj+Y3QkWgZXLXit6152RafitUXRyCuUMzitKxr7UcBIBC7F5+Lh7xSh6ec7B2DKc82MZGVcBgUoQAEKUIACFKCAqQmohqatraxxLuki5j02BR0DI01tK1wvBShgBgIMTZvBIXILFKAABShAAQpQgAIUoAAFKEABClCAAhSgAAUoQAEKmL/AlO03MWX7LWmjro62GNKjGVo08NFp4xv2X8be8wlS3wWvRKBva92u02nwCjr9dT4Fry+6iEIZg9PVteT1FKhNgei7Oej5vaLC/XP3BWDaEIama9Ofc1GAAhSgAAUoQAFzElANTVvBCueTL2HeYz+gY2Abc9om90IBCpiIAEPTJnJQXCYFKEABQwgUnD6NzCVLkPvPP3B7+mm4DR0Ku4YNDTE0x6AABShAAQpQgAIUoAAFKEABClCAAhSgAAVqQeDt5Zex6pgi/Cza2H7NEFY/AEVy7ZN7OwKrd1/G1jOKa6Y93wzPdQqohZXem2Lr2RS8tjAa8pISVpyuVXlORoGqC5yPy0GvHxSh6Wc7BWD68wxNV12TV1KAAhSgAAUoQAHLFjgefwb/2fouWvo2w0MhnTH39BJWmrbsW4K7p0CdCjA0Xaf8nJwClisgz8lBSV5ehQBW9vawdne3XCQD77wkNxd3+vSBsC9t9i1bot6SJQaeicNRgAIUoAAFKEABClCAAhSgAAUoQAEKUIACNSmgGZwWgcaXHw6DzNoWeTJAXgL4OgLWJTL8siMGK/8NWddFYLrUYdOZZLy+KFr6sVmgM355KVz6nY0CFDBOgXOx2eg95ZS0uGc6BmDGUIamjfOkuCoKUIACFKAABShg/AKqoemuIZ3x8+klmP/YFHQIjDT+xXOFFKCA2QkwNG12R8oNUcA0BGIHDIAsNrbSxVq7uMAuLAx2jRrB9emn4dCqVaXXmGKHwosXURQTo1y6fUSEwStA52zejORPPinDE7RqFeyaNDFFNq6ZAhSgAAUoQAEKUIACFKAABShAAQpQgAIWK6AZnG4Z7ILpQ5pD/C7a+dgcjPjtAm6n5ks/G0Ol2HWnkvDGkovSehictthblxs3EYGo29noO00Rmn66oz9mDm1uIivnMilAAQpQgAIUoAAFjE3gXmi6OR4M6YRfTi9laNrYDonroYAFCTA0bUGHza1SwJgEbj/0kFrFY13X5jpgADzHjIGNn5+ul5hEv7SZM5G5cKFyrc69esHv228Nuva8ffuQOHZsmTGDN2+Gbb16Bp2Lg1GAAhSgAAUoQAEKUIACFKAABShAAQpQgAI1L/Dr3lh8uu5eMQYxY9/WPtLE286mKBcwvk99jO/ToOYXpMMMf5xIxJu/X5J6MjitAxi7UKCOBE7fysLj009Lsz/VwR+zXmBouo6OgtNSgAIUoAAFKEABkxdQC00Hd8QvZ35naNrkT5UboIDpCjA0bbpnx5VTwKQFqhqaFpu2a9AA9VauhJW9vUkbqC6+NkLTKC7Gnf79UZyQoJzauUcP+E2dajaO3AgFKEABClCAAhSgAAUoQAEKUIACFKAABSxNQFSUHrv8Ei7E5ZTZekSQC2Y8f6/6tLHYrDyWgHeWX5aWw+C0sZwK10EBdYGTN7PQf4YiND2ovT/mvMjQNO8RClCAAhSgAAUoQIGqCaiGph8I7ohfz/yOBY9PRfuA1lUbkFdRgAIUqIYAQ9PVwOOlFKBA1QU0Q9MO7drBysFBbcCi69fVAr6qT3q8+io8R4+u+gKM7MpaCU0DKE5JQfaGDSg4fhyimrVL796wcnY2Mg0uhwIUoAAFKEABClCAAhSgAAUoQAEKUIACFNBXQFSW3nYuBe5Otgj1dkDLIFc80MRD32Fqrf+yw/F4d9UVaT4Gp2uNnRNRQGeBEzcyMWDmGan/k+398OOLLXS+lh0pQAEKUIACFKAABSigKqAamu4S3AHzzixjaJq3CAUoUGcCDE3XGT0npoBlC2iGpkO2b4eNn18ZlJLCQmQtX460GTPKPBe8dStsAwLMArK2QtNmgcVNUIACFKAABShAAQpQgAIUoAAFKEABClCAAmYhsOTgXby/5qq0FwanzeJIuQkzEjh2PRNPzFKEpp9o54efhjE0bUbHy61QgAIUoAAFKECBWhUoDU238m2O+4M6YF4UQ9O1egCcjAIUUBNgaJo3BAUoUCcCuoamSxeXvXYtUr78Um2t/jNmwOmhh+pk/YaelKFpQ4tyPApQgAIUoAAFKEABClCAAhSgAAUoQAEKUMAUBH7bH4eP1l6TlsrgtCmcGNdoKQJHYjIwaHaUtN2Bbf0w9yWGpi3l7LlPClCAAhSgAAUoYGgB1dB056D2mB+1HL89Pg3tAloZeiqORwEKUKBSAYamKyViBwpQoCYE9A1NQy5H7BNPQBYbq1yO1zvvwH3YsAqXl3foEArPnUPhxYsouHABKCqCffPmsG/RAnbNmsH5kUdgZWdX4RgF584hd9s2ZR9rT094/Oc/5V6TtXo1ZDdvKp936d9fmq+0FV64gJwtW9Suzz95UlqjanMfOrTCdbkNGQLbkBCtfbLWrIHsxo1Kj862fn24Pftspf1Eh/Q5c1CSl3fPf/x4iHPJ2bkTOZs2QXbnDoqTk2EbFCTZOnbsCNcBAwBra53GF9fnHTgA4V1w+rR01nZhYXBo3RpuQ4fCvmlTFF2/juw//lCOZ9+6NVz69NFpfHaiAAUoQAEKUIACFKAABShAAQpQgAIUoAAFjFNg3t5YTFwXIy2OwWnjPCOuyvIEDl3LwFNzFKHpAW188fPwcMtD4I4pQAEKUIACFKAABQwioBqavi+oHRZErWBo2iCyHIQCFKiKAEPTVVHjNRSgQLUF9A5NA0h6/33k7tihnNv1ySfhM3Gi1rXIs7KQOnkycrZvr3Ct9i1bwu/rr8sNH4uLRcA5+eOPlePYBgcjeOPGcsdNHD0aIqxd2jxHj4bHq68qfxZrSp4wodqGPp99BteBA7WOkzh2LPL27at0DqcuXeA/Z06l/UQHzTML/ecfpHz+OXL/+afc60Vw2ufzz2EbGFjhHGKtYs0VNb9vv5WeFvdBaXN7+ml4f/ihTutnJwpQgAIUoAAFKEABClCAAhSgAAUoQAEKUMB4BX7eHYtJGxicNt4T4sosTeDg1Qw8/aMiNN2/jS9+YWja0m4B7pcCFKAABShAAQoYTEAZmvZrgfsC22LBWYamDYbLgShAAb0FGJrWm4wXUIAChhCoSmhaBHSz161TTu86eDB8VMLMpU8U3biBhFGjUJyQoPNS/WfNgtODD2rtz9C0gkXzzDzfeAPpP/5YqbGoFB24cCFgZaW1b9bKlUj9NxBd2WAigC4qXpc2hqYrE+PzFKAABShAAQpQgAIUoAAFKEABClCAAhQwHYE5u+7gq03XpQWz4rTpnBtXap4C+6+k49mfzkqbezzSF/NeZqVp8zxp7ooCFKAABShAAQrUvIBqaLpTYBv8dnYlFvabjrb+LWt+cs5AAQpQQEOAoWneEhSgQJ0IVCU0HT9iBApOn1au13PUKHi89lqZ9Se88QbyDx/Wa1823t4I3rQJVo6OZa4zdGhaVMtWrZas10JVOvt+/jlc+vfXenltVJoWFbdlsbE6Ld/3m2/g0rt3mb7FiYm407ev1jHswsIgz8lRC79rzsnQtE787EQBClCAAhSgAAUoQAEKUIACFKAABShAAZMRmLnzNr7ZckNaL4PTJnNsXKgZCuy9nI4hcxWh6cda+2D+KxFmuEtuiQIUoAAFKEABClCgNgRUQ9MdAyOx8OwqhqZrA55zUIACWgUYmuaNQQEK1ImAvqHp/BMnkKARkPb98ku4PP642vrzjx1DwsiRZfbk+/XXcLr/fljZ26Pg3DkkT5xYphK119ixcB8+vMy1hg5NawNPmzkTmaIa87/NuVcv+OlYfVnrAcrlWh/OWLBArTq0U5cu8Fep3FzRzaB5ZqV9PceMgUu/frD194c8KwuZy5Yh45df1IZy7NQJAT//XGb4tBkzkLlokdrj4ky9P/gA1q6u0uPyjAykTZ2K7I0by1zP0HSdvHw5KQUoQAEKUIACFKAABShAAQpQgAIUoAAFalRg6l+38MO2m9IcDE7XKDUHp0C5ArsvpWHoz+ek5/u28sGCEQxN83ahAAUoQAEKUIACFKiagGpoukNgJBadXYVF/aajDStNVw2UV1GAAtUSYGi6Wny8mAIUqKqAPqHpvH37kDJpEopTU9WmC929G9bu7mqPxQ8fjoKzisoHpa3e0qWwj1D/j3nFSUm4+9JLasFpaxcXBG/bBvG7ajPJ0HQ5B5Mxfz7SVULS1Q1N+33/PZx79iwzW8rnnyN73Trl46KSd8jOnWr9RBj69sMPqz3m3KMH/H74AbC2rnRM0YGh6aq+AnkdBShAAQpQgAIUoAAFKEABClCAAhSgAAWMW+D7bTcx7a9b0iIZnDbus+LqzFPgn4tpeOEXRWi6Tysf/MbQtHkeNHdFAQpQgAIUoAAFakGgNDTd2q8F2ge0xqJzq7Go3wy08ecH82qBn1NQgAIaAgxN85agAAXqREAzNC2qC1s7OyvXUiKXozg5GUXXrkEWG1tmja5PPAGfTz9Ve7wkLw+3HnxQ7TFRAdn3iy+07jFr2TKkioCuSgv87Tc4tGmj9hhD0woOzTOzb9EC9X7/HbCyKuOrreJ3/YMHYeXoqOybf+QIEkaNqtS/tIMsIQGxjz2m1p+h6Tp5+XJSClCAAhSgAAUoQAEKUIACFKAABShAAQrUisA3W25g5s7b0lwMTtcKOSehgFLg7+hUDPv1vPRz75beWPhqS+pQgAIUoAAFKEABClCgSgKqoel2Aa2xmKHpKjnyIgpQwDACDE0bxpGjUIACegpoBnD1udyxUyf4ffcdrD081C4run4dcU89pfZYwLx5cGzfXuvw8sxM3O7RQ+0538mT4dK3r9pjDE0rODTPzOP11+H53/9qtZXduYPYgQPVngveuhW2AQHKx7I3bEDKZ58pf7YNDkbwhg1aQ9ilneIGDULRTcXXcorG0LQ+rxz2pQAFKEABClCAAhSgAAUoQAEKUIACFKCA6Ql8uek6ftx1R1o4g9Omd35csekK7LiQiuHzFKHpXhHeWPQfhqZN9zS5cgpQgAIUoAAFKFC3Aqqh6bYBrbDk3Bos7jcDkaw0XbcHw9kpYKECDE1b6MFz2xSoa4GqhqbdX3wRXmPHAjY2ZbaQd/AgEseMUXs8ZMcO2Pj4lLvdO48+iuLUVOXznqNHw+PVV9X6MzSt4NA8M5+JE+H65JNabWXx8Yh9/HG15zRD0xm//IL0uXOVfZx79oTf999XeGsmvv028vbuVfZhaLquX8mcnwIUoAAFKEABClCAAhSgAAUoQAEKUIACNS8waUMMft6t+EZCBqdr3pszUEAIbD+XglcWXJAwekZ4YwlD07wxKEABClCAAhSgAAWqKHAvNB2Otv4RWHL+DyzuPxORfuFVHJGXUYACFKi6AEPTVbfjlRSgQDUEqhKadu7VC37fflvurFlr1iB18mS15xscPw5YW5d7TfyIESg4fVr5vAgBizCwamNoWqGheWZ+U6fCWaNSd6mbLqHplEmTkL1+vZLabcgQeL/3XoV3VcqXXyJ77dp71zz9NLw//LAadyIvpQAFKEABClCAAhSgAAUoQAEKUIACFKAABUxBYOK6a5i3N05aKoPTpnBiXKOpC2w7m4IRvylC04+Ee2Ppa6w0bepnyvVTgAIUoAAFKECBuhJQDU238Y/AUoam6+ooOC8FKACAoWneBhSgQJ0IaAZw/WfMgI23t9paMhYuRO7ffysfs3ZxQfDmzbB2d9e65sylS5E2daracw1OnqxwfwkjRyL/2DFlH5c+feD79ddq1zA0reAoc2bTpsGpe3etvrqEppPGjUPu7t3K691feAFe48dXeF4MTdfJy5WTUoACFKAABShAAQpQgAIUoAAFKEABClDAKAQ+/OMaFh5gcNooDoOLMHuBLVHJ+M/CaGmfD7fwwu+vtzL7PXODFKAABShAAQpQgAI1I6Aamo70D8fv59diSf+ZaM1K0zUDzlEpQIEKBRia5g1CAQrUiYBmADdk+3bY+PmpraXo6lXEPfus2mOeI0fCY+RIrWvO3bkTSRqVikP37YMIW5fX4gYNQtHNm8qn3V9+GV5vvaXWXd/Q9N2hQ1F48aJyDM/Ro+Hx6qsVOqfNnInMhQuVfSqrql3VQ8uYPx/pc+YoL3fq0gX+Kj9XNK6hQ9Op33yDrFWrlFO66VA1mqHpqp48r6MABShAAQpQgAIUoAAFKEABClCAAhSggHkIvL/6KpYcuitthhWnzeNMuQvjFNh0JhmvL1KEpns098KykQxNG+dJcVUUoAAFKEABClDA+AVKQ9ORfuFo7dcCv1/4k6Fp4z82rpACZivA0LTZHi03RgHjFtAlNC12oFmNWDwW+s8/sPbwKLPBwvPncXfYMLXHg9auhV3Dhtox5HLc7NhR7Tmfjz+G6+DBao/pG5rW3Jsuoen0WbOQ8dtvynmdHnwQ/rNmGfwQjSk0LULiIixe2py6dYP/9OkV7pmhaYPfEhyQAhSgAAUoQAEKUIACFKAABShAAQpQgAImJ/DuyitYdiReWjeD0yZ3fFywiQhsOJ2E/y5WFIjp3twLyxmaNpGT4zIpQAEKUIACFKCA8QmohqZb+bXAMik0PUsKULNRgAIUqG0BhqZrW5zzUYACkoCuoemCs2cRP3y4mprHa6/Bc9SoMpLFqam48+ijao97jhkDjxEjtKrnHTqExNGj1Z4TVZdF9WXVlrN9O5InTFA+JCpXiwrW2po8PR23H3lEfQ06VJrOWrECqd99p7zOJiAAIVu3GvxuMabQtDbXkJ07YeXgUO6+kz/5BDmbNyuf16U6tcEROSAFKEABClCAAhSgAAUoQAEKUIACFKAABShQ5wJvL7+MVccSpHUwOF3nx8EFmKHA+lNJGLVEEZp+qJknVv63tRnukluiAAUoQAEKUIACFKgNAdXQdEu/5lh+YR2W9p8FEaBmowAFKFDbAgxN17Y456MABSQBXUPTom/CyJHIP3ZMTS501y5Ye3qqa5aU4Ha3bpDn5Cgft/H2RvCWLbCFLCuZAAAgAElEQVSyty8jnzh2LPI0ws/BGzfCNjhYra+24Hbw+vWwDQ0tM6ZmKFl00KXStFiHWI9qq7dsGexbGPYviMYUmi44cwbxr7yitmffyZPh0rdvua+S2AEDIIuNVT7P0DTfUChAAQpQgAIUoAAFKEABClCAAhSgAAUoYLkCb/5+CX+cSJQAGJy23PuAO68ZgT9PJmH0UkVoumtTT6waxdB0zUhzVApQgAIUoAAFKGD+Aqqh6Qjf5lgRzdC0+Z86d0gB4xVgaNp4z4Yro4BZC+gTms4/cgQJGpWlRfVoUUVas2UuXoy06dPVHna8/374ff89RIVoqcnlSJ0yBVnLl6v1c+7VC37ffltmzOKUFNzp1avsmN99B2tXV+XjeQcOIPmDD9RC2+JJXULTRdevI+6pp9TmsG/aFL7ffgu7hg0Ndi8YU2hanIPYc9HNm8r9iZC7/+zZZcPiJSXI+PVXpM+dq2bB0LTBbg0ORAEKUIACFKAABShAAQpQgAIUoAAFKEABkxQQlXBFRVzRGJw2ySPkoo1UQHwgQXwwQbQHm3hi9RsMTRvpUXFZFKAABShAAQpQwOgFlKFp/whE+DTFiuj1WDpgNlr5Njf6tXOBFKCA+QkwNG1+Z8odUcAkBPQJTYsN3R02DIXnz6vtLWTnToiQrWoryc9HbP/+KE5NVXtcBKYdOnSAtZsbCk6fVqtWXNoxaO1a7QHlkhLEDhxY5hoxt1O3brBydETR1avIP35cGkrMpVrtWvxs37o17Bo1gufrr8Paw6PMGZXIZLj7zDNqAeLSTk5dukhVrW28vCDPz4c8NRWyu3elitg+n35aZqzMZctQnKT4nwSaTVR3FvsvbTYBAVorO9uHh8Old2+1yzXPzH/aNDh17651Hll8PGIff1ztueCtW2EbEKD2WM62bUj+8MMyY4gwtFibjZ8fiuPjkbN1q1YbhqZN4uXORVKAAhSgAAUoQAEKUIACFKAABShAAQpQoEYFXl8UjU1nkqU5GJyuUWoObkECq48nYuwyRWj6gSYeWPNGpAXtnlulAAUoQAEKUIACFDCkgGpoOtynCVZGb8DvA2ajJUPThmTmWBSggI4CDE3rCMVuFKCAYQX0DU3n7t6NpHHj1Bbh/vLL8HrrrTILy96wASmffabXgt2eeQbeEyaUe03uP/8gafz4SscUQWrnRx5B1po1WvsGr18vBaC1NRG6Tnj99UrnKO0gQtPBGzeW6X936FAUXlR8ZV5Vm+uTT8Jn4kS1y2siNI3iYsQ9/bTWQLQua2doWhcl9qEABShAAQpQgAIUoAAFKEABClCAAhSggHkLyEtK8NrCaGw9myJtlMFp8z5v7q52BFYdS8Dbyy9Lk3Vp7IE/RjM0XTvynIUCFKAABShAAQqYn0BpaLqNfwSaezfBqosMTZvfKXNHFDAdAYamTeesuFIKmJWAvqFpyOWIe+qpMuHakB07YOPjU8ZGhJxTJk5Uq/hcHqAIXru/9BJgbV2hcfKECcjZvr3CPv4zZqDwwgWk//yz1n4VhabFBemzZyNjwQKdz7rBsWOAjY1af5MKTQMoTklB0nvvoeDUqQr3LVX1trGBONvS5v7CC/DSIcyuMyg7UoACFKAABShAAQpQgAIUoAAFKEABClCAAiYpUFgsgtMXsOO84lsIGZw2yWPkoo1IYMXRBIxboQhN3x/mgbVjGJo2ouPhUihAAQpQgAIUoIBJCaiHphtj1cWN+H3AHLT0bWZS++BiKUAB8xBgaNo8zpG7oIDJCWiGpkN374a1u3uF+8jZsgXJH3+s1sfr7bcVgWctTXb3LtKmTpXCuMWpiv9QrtocO3aE5+jRcGjTRme/nM2bkfrNN2XC2KLqswjvOvfogawVK5D63Xdax6wsNC0uKjh7Fhnz5yNv795K1xXy11+w8fVV6yeqVYuq1dVp7kOHwuvdd9WGiB0wALLYWOVj/tOmwal7d63TFKel4U7PnmrPhe7aBWtPT639S2QyZMydi+zNm1GckFCmj0u/fvD+4AMkvvGG5FPavN99F25Dh1Znq7yWAhSgAAUoQAEKUIACFKAABShAAQpQgAIUMBOBvEI5Xlt0Abui06QdMThtJgfLbdSJwLIj8Xh35RVp7vvC3LFujO7/L6VOFsxJKUABClCAAhSgAAWMVkA1NN3MOwyrL27CsgFzEMHQtNGeGRdGAXMWYGjanE+Xe6MABZQCoppxUUwMYGUF23r1pF+VVZYul6+kBMXJySi6cUPqYuPlBduGDWFlayv9LM/MhDw9HbCzg5W9PaxUftesCl3hERUXozAmBvLUVJQUFwNFRYC9PaydnWHj5wfbgIAyVabN4cjlGRkovHwZJbm5sPbxgV39+spAvWbY3m/qVCmozkYBClCAAhSgAAUoQAEKUIACFKAABShAAQpQQAhk5xfjtUXR2HOJwWneERSojsDvh+Pxv1WK0HSnRu5Y/yZD09Xx5LUUoAAFKEABClDAkgVUQ9NNvcKw5hJD05Z8P3DvFKhrAYam6/oEOD8FKEABCugkICpMxw8frta33ooVsG/Gr2vRCZCdKEABClCAAhSgAAUoQAEKUIACFKAABShgIQLpuTIpOH3gSrq0Y1actpCD5zYNKrDk4F28v+aqNGbHhu7Y8BZD0wYF5mAUoAAFKEABClDAggTUQ9ONsOb/7N0HdJTVusbxJ70nhJ7QUVAEQYoIVsSC7YiogA259nZQUUGx4FEUPIooeiwICoh0VDooXXoXEJDeIZSEBJKQnrv2FzPMpJCemST/vVYWZGaXd/++Od674OHNjlkad/fXuqxKowqkwFURQMBVBAhNu8qToA4EEEAAgdwF0tN1/JlnlLBuncOcOkuWyD0oCDkEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMBBIDI2WU+N2q5Ve2Os1wlO8wFBoGACo5cfU7+fM0LTresFacZLVxRsA2YjgAACCCCAAAIIIPCPwPnQdFNdHFpfP++YpfF3f60mhKb5jCCAgBMECE07AZ0jEUAAAQTyL5ASEaHI999XwqpVDouCunVT5TfeyP9GzEQAAQQQQAABBBBAAAEEEEAAAQQQQACBCiVw4kySFZxeu/+MdW+C0xXq8XPZIgqMXHZUb/2yx9qlVb0gzSQ0XURRliOAAAIIIIAAAhVXwD40fVGlevpl52xC0xX348DNEXC6AKFppz8CCkAAAQQqrkBabKxSDh1S2rlzSjdf8fG23yfv36+kbduUuGVLjkC158+XR+XKFRePmyOAAAIIIIAAAggggAACCCCAAAIIIIBAngJHoxOt4PTGg2etuQSn8yRjAgKWwA9Lj+rtXzNC0y3rBmnWy3Sa5qOBAAIIIIAAAgggUDiBzND0FdWbqkGluvp15xyNv/sbNalyceE2ZBUCCCBQBAFC00XAYykCCCCAQNEEzi1ZohO9exd4k9BXX1Xwww8XeB0LEEAAAQQQQAABBBBAAAEEEEAAAQQQQKDiCRyKSrCC05sPx1qXJzhd8T4D3LjgAiP+OKL+U/daC1vUCdKc3oSmC67ICgQQQAABBBBAAAEjkFNoesLd3+hSQtN8QBBAwAkChKadgM6RCCCAAAIZAglr1+r4M8/km8N0lq785pvy79gx32uYiAACCCCAAAIIIIAAAggggAACCCCAAAII7Dt1zgpObzsaZ2EQnOYzgcCFBb5bckT/mZYRmm5eJ1Bze7eEDAEEEEAAAQQQQACBQgnYh6brh9TR1F1zRWi6UJQsQgCBYhAgNF0MiGyBAAIIIFA4gcS//lLEo4/mudj70kvl266dQv7v/+QeHJznfCYggAACCCCAAAIIIIAAAggggAACCCCAAAJZBXafiLeC0zsi4q23CE7zGUEgd4FvFx/R+9MzQtOX1w7Ub68QmubzggACCCCAAAIIIFA4AfvQdL2Q2pq26zdN7PyNLql8ceE2ZBUCCCBQBAFC00XAYykCCCCAQNEEUk+cUPR338nNx+f8l5eX3Hx95R4YKK+GDeV98cXW9wwEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKCoAiYw/dSobdp94py1FcHpooqyvrwKfLPosAbM2Gddr1mtQP3+KqHp8vqsuRcCCCCAAAIIIFDSArbQdI2mqhecGZr+VpdUvqikj2Z/BBBAIJsAoWk+FAgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAhVGYOvROKvj9P5TBKcrzEPnogUW+GrhYX04MyM03TQ8QPNea1XgPViAAAIIIIAAAggggIAROB+abqa6QeGavvt3TexMaJpPBwIIOEeA0LRz3DkVAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDASQKbD8dawelDUQlWBXScdtKD4FiXFfhywSENmrXfqq9JeIAWEJp22WdFYQgggAACCCCAgKsLZIamW9ZoptpBYZqxe54mdf5Wjek07eqPjvoQKJcChKbL5WPlUggg4KoCqSdPKuXwYSUfPmz9ar5So6LkUamS3P/5yvy9+dWnRQu5+fq66nWoCwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQKLMCGw+c1VOjt+todKJ1B4LTZfZRUngJCAydf0j/nZ0Rmr40LEAL+9BpugSY2RIBBBBAAAEEEKgQAjmHpoepceWGFeL+XBIBBFxLgNC0az0PqkEAgXIsEDNsmKKHDSvwDX1bt5Zvu3byu/pqeTdpUuD1LEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBDIWWDtvjNWcPrEmSRrAsFpPikIZAh8Pu+gPp5zwPr9JTX9tahva2gQQAABBBBAAAEEECiUgH1oulZgTc3cM1+TOhOaLhQmixBAoMgChKaLTMgGCCCAQC4C585JUVHWV9wff+jMvHlKOnjQYXK9KVMUv2GDTg4cmC9Gr4sukm/btgro1Ek+zZvnaw2TEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBHIXWLUnxgpOR8YmW5MITvNpQUAa8vtBDZ6bEZpuXMNfi18nNM3nAgEEEEAAAQQQQKBwAvah6fDAmpq1Z74m3zNMjULpNF04UVYhgEBRBAhNF0WPtQgggEBWgeRkKTJSKX//rfjlyxW/aZMSd+8uESf/G29U4N13y++GG0pkfzZFAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQqisCyXdFWcDomPsW6MsHpivLkuWduAiYwbYLTZjSq4a8lhKb5sCCAAAIIIIAAAggUUsAxNF1Ds/Ys0OR7vlOj0AaF3JFlCCCAQOEFCE0X3o6VCCCAwHmB5GQlrVih+EWLlLBlixL37ctRp9Jdd8nn4ouVlpCg9IQE61fr94mJSktMtH5NT0rK+H1SklJMAPvkyQtKe1SvrqBu3eTfoYO8GvKv8PhYIoAAAggggAACCCCAAAIIIIAAAggggAAChRFYsuO0nhq1XbGJqdZygtOFUWRNeRH4eM4BfT4vIzR9cXU//fFGm/JyNe6BAAIIIIAAAgggUMoC9qHpsIDqmr13IaHpUn4GHIcAAucFCE3zaUAAAQSKIpCcrOihQ5WwapUS9+7NtpNHaKiCb7rJCkh7hYXJv3XhfnydFZ4+cULJx48r+cQJnV24MNtZJjBd7eOPCU4X5XmyFgEEEEAAAQQQQAABBBBAAAEEEEAAAQQqtMDC7VFWcPpccprlQHC6Qn8cKvTl/zt7v4bOP2QZNKzmp2X9CE1X6A8El0cAAQQQQAABBIogkBmablXjctUIqKY5exdqyj3f6WI6TRdBlaUIIFBYAULThZVjHQIIVHiBhAULFDNihBJ27MhmkRmWDurQQW6ensVuFT1zpmKXLlVqTIzD3h7Vqqna4MHyufzyYj+TDRFAAAEEEEAAAQQQQAABBBBAAAEEEEAAgYog8PtWE5zepuTUdOu6BKcrwlPnjlkFBs3ary8XZISmG1Tz03JC03xIEEAAAQQQQAABBAopYB+arh5QVXP3LtKUe4br4tD6hdyRZQgggEDhBQhNF96OlQggUIEFogcPVsy4cdkEPCpVUtANN1hf7v7+JSpkAtNnly7NMTxd4+uv5duuXYmez+YIIIAAAggggAACCCCAAAIIIIAAAggggEB5FZizJdLqOJ2WTnC6vD5j7nVhgQ9n7tNXCw9bk+pX9dOKN+k0zWcGAQQQQAABBBBAoHACDqFp/yqau28xoenCUbIKAQSKQYDQdDEgsgUCCFQcgYS1axUzdKgStm1zuLSbh4eCOnZUcMeOMl2mS3PkFp4OfeUVBT/ySGmWwlkIIIAAAggggAACCCCAAAIIIIAAAggggEC5EZi56ZSeHr3ddh86TpebR8tF8iEwYMY+fbMoIzRdr4qvVr51ZT5WMQUBBBBAAAEEEEAAgewC9qHpav5V9Nu+xfq5y3BdVIlO03xeEECg9AUITZe+OScigEAZFYj+3/8U88MP2aoPaNfOCkt7163r1JtlhqdjZs601VF1wAAF3HmnU+vicAQQQAABBBBAAAEEEEAAAQQQQAABBBBAoKwKTN14Us+P+dtWPsHpsvokqbugAu9P36tvFx+xltWt7KtVbxOaLqgh8xFAAAEEEEAAAQQyBOxD01X9K+v3fUv0c5cRuqhSPYgQQACBUhcgNF3q5ByIAAJlUeDs6NGKGjrUoXSfBg0Uctdd8mva1KWudOa333T6119tNYVNnCjvRo1cqkaKQQABBBBAAAEEEEAAAQQQQAABBBBAAAEEyorAz+tPqNfYHbZyCU6XlSdHnUUR+M+0vfpuSUZouk5lX60mNF0UTtYigAACCCCAAAIVWsAhNO1XWb/vJzRdoT8QXB4BJwsQmnbyA+B4BBBwfYG4X3/VqQEDHAoNbN9eVXr2dNnizy5apKiJE636TLi75s8/u2ytFIYAAggggAACCCCAAAIIIIAAAggggAACCLi6wMS1x9V7/E5bmQSnXf2JUV9RBfpP3asRf2SEpmuF+mjtO22LuiXrEUAAAQQQQAABBCqogC00XfNyVfEN1bz9f+iXLiPUkE7TFfQTwbURcK4AoWnn+nM6Agi4uMC5RYt04tVXHaqs9vTT8m/VysUrl2KXL1fkmDFWnf5t2qjad9+5fM0UiAACCCCAAAIIIIAAAggggAACCCCAAAIIuKrAuFURem3SLlt5BKdd9UlRV3EIvP3rHv2w9Ki1VXglH63rT2i6OFzZAwEEEEAAAQQQqIgCmaHp1jWbK9Q3RPP3LyU0XRE/CNwZARcRIDTtIg+CMhBAwPUEEjds0ImXX1ZabKytuLpffCE3b2/XKzaXiuLWrNGpH36w3q3UtatC+vUrM7VTKAIIIIAAAggggAACCCCAAAIIIIAAAggg4GoCY1Yc0+tTdtvKIjjtak+IeopL4K1f9mjksozQdFiIj9a/S2i6uGzZBwEEEEAAAQQQqGgCDqFpnxDNP2BC09+rYaW6FY2C+yKAgAsIEJp2gYdQ2BIiIiL0+eef69ixY+rZs6c6duxY2K1YhwACWQSSd+/WiZdeUsqxY7Z3wt58U951y97/w3Zm/nydnjJFcndXzUGD5HPLLTxvBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgUIKmCCpCZRmjoIGp58Zvd1aOqxnk0JWwDIESl6g38+7NXp5xt+R1Azx1oZ3ryr5QzkBAQQQQAABBBBAoFwK2IemK/kEa8GBZfq1y/dqQGi6XD5vLoWAqwsUe2h6x44d+u6777R161Zt375dVapU0U033aTHH39cTZrwhz/F+YF455139OOPP1pbBgQEaOXKlQoJCSnOI9gLgQorEDVokM5Onmy7f5VHHlHgtdeWWY8TX32lc1u2yO+yy1T9iy+kypXL7F0oHAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQcLbAiD+OqP/UvbYyChKcDn9lqbVu4rOX67rGlZx9Fc5HIEcB01HddFY3o0awtzb+h9A0HxUEEEAAAQQQQACBwgnYh6ZDfIK08MBy/Xrv92oQUvYaFxZOgFUIIOBKAsUamp46dapeeumlXO931113qX///qpRo4YrGZTZWvr166dx48bZ6v/22291++23l9n7UDgCriKQvG+fjj34oNKTkqySfBs1Uo1XX3WV8gpVR/z69To5fLi1NrRrVwWb+3h7F2ovFiGAAAIIIIAAAggggAACCCCAAAIIIIAAAghIwxYf0XvTCx6cHjRrv75ccEj3ta6uLx++BEoEXFKgz6RdGrsqwqqtWpC3Nr1HaNolHxRFIYAAAggggAACZUDAPjQd7BOkRVZo+gc1CKlTBqqnRAQQKG8CxRaanjVrlp5//vk8fUxH5K+//lodOnTIcy4TLizQt29fTZw40Tbp7bff1lNPPVVgtuTkZO3cuVOhoaEKDw8v8HoWIFDeBE4PGaIzP/1ku1a1p5+Wf6tWZf6aER99pMT9++Xm46OagwbJm/8Ol/lnygUQQAABBBBAAAEEEEAAAQQQQAABBBBAwLkCXy08rA9n7rMVkZ+O00dOJ+rKAWusNXN6t1SLOoHOvQSnI5CDwGsTd2nc6ozQdNUgL21+rx1OCCCAAAIIIIAAAggUSsAhNO0dqEUHV2jqvT+oPqHpQnmyCAEEiiZQLKFpE7q96qqrFBkZme9qnnvuOb366qvy8vLK9xomOgq89tprmjx5su1F832vXr0KxPTXX3+pW7duiouLs9aZTuBPPPFEgfZgMgLlSSAlIkLHundX2tmz1rX8W7RQteeeKxdXPLt4saImTLDu4teihap/843k61su7sYlEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABZwl8Mf+QPpq933Z8foLT/abs1ugVx/RA2xoa8kBjZ5XOuQjkKvDKhJ2asOa49X6VQC9teZ/QNB8XBBBAAAEEEEAAgcIJZIam29RsrkDvQC0mNF04SFYhgECxCBRLaHru3Ll65plnClxQixYtNGLECFWvXr3Aa1kg9e7dW7/88ouNwnSZNt2mCzK6d++uVatWOSwZPXo0ncALgsjcciUQPWyYYoYNs92pxssvy/fSS8vFHdPOndOxDz5Qyj//wKXKiy8q8P/+r1zcjUsggAACCCCAAAIIIIAAAggggAACCCCAAALOFBjy+0ENnnvAVkJewek1+87oni83WfN/eaG52l0U4szyORuBbAIvj9+pSWszQtOVA7z01wBC03xMEEAAAQQQQAABBAoncD403UIB3v5acnClpt47UvVDahduQ1YhgAACRRAocmh6//796tGjhw4ePOhQRs+ePfXss88qISFBu3fv1tSpUzVr1qxspZrg9MSJE+Xn51eEa1TMpaar9PTp022Xr1u3rvr06aOoqCjt27dPR48etb7i4+NVuXJlBQQEqFq1atacmjVrWutyCk2bedOmTVOjRo0qJiy3rtACx595Rglr11oGge3bq0rPnuXKI3rGDMX8899iv+bNVf3rryV//3J1Ry6DAAIIIIAAAggggAACCCCAAAIIIIAAAgg4Q+CTuQf02e/n/74sr+B0jxFbtWBblO6+opq+fbR8NO9whjtnlozAS+N2aPK6E9bmof6e2vpB+5I5iF0RQAABBBBAAAEEyr2AQ2jay09LDq3StHtHqh6h6XL/7LkgAq4oUKTQ9LJly/T0008rLi4u291MB+TWrVs7vL5w4UI99thj2ebefffd+vLLL13RxyVrMkF0E4o2naa3b99e4Bq7deumTz75xFo3duxYvfnmm9n2CAsL0/Lly+Xh4VHg/VmAQFkVSI2M1OFbbrGVX/P11+XToEFZvU6OdaecPKmjAwYoPSnJej/s00/lfeON5eqOXAYBBBBAAAEEEEAAAQQQQAABBBBAAAEEEHCWwEez9+uL+Ydsx18oOD153XG9NG6nNXfMU011U5PKziqbcxHIJtBr7A79vD4jNF3J31PbCE3zKUEAAQQQQAABBBAopIB9aNrfy09/mND0fSNVL5hO04UkZRkCCBRBoNChadMdum/fvrke/ccff6hevXrZ3t+0aZNMSDrrGDdunK655poiXKV8Ll2/fr1M2DwyMtL6Ml279+7dW6TLmu7SH3/8sW2PBQsWWCHqrAHs3377TZdeSmeDImGz2EHgQKtW8gwPl+9VV8m7SRN5X3qpfJo1cxml+PnzdfKf/655Vq+uWu+/7zK1FWchUePG6ewff1hbhvzrX6r03nvFuT17IYAAAggggAACCCCAAAIIIIAAAggggAACFVrgg5n79PXCwzaD3ILTiSlpuvHj9dp/KkF3NK+qEf/XpEK7cXnXEnjhp7/164aTVlHBfp76+0M6TbvWE6IaBBBAAAEEEECg7AjYh6b9vHy19NBqTb9vlOoG1yo7l6BSBBAoNwKFCk2bLtKmy3FOo2XLlnrjjTfUrl27XJFMGNd0qLYfLVq00LRp0+Tm5lZucLNeJDk5WZs3b9aePXu0c+dO7dq1S/Hx8WrQoIE6duyo2267zWHJ/Pnz9cQTTxSLR926ddWkSRNdfvnlevLJJ+Xn5+ewb3p6ulavXq0lS5boyJEj8vb21nvvvaeAgIBiOZ9NEDACJjSddZjQtO8118jvmmucHqCOGjhQZ6dMsUoMvOYaVenRo1w+uPj163Vy+HDrbl41aih8wgQpJKRc3pVLIYAAAggggAACCCCAAAIIIIAAAggggAACzhB4b/peDVt8xHZ0bsFp+87Us16+Qi3rBjmjXM5EIJvAc2P+1rSNGaHpIF9P7RhIaJqPCQIIIIAAAggggEDhBBxC054+Wnp4DaHpwlGyCgEEikGgwKHp2bNn67nnnst2tAnkms7TJvybn/HBBx9o+D+hvcz5kyZN0lVXXZWf5aU6Jy4uTu7u7tmCxvkpIjExUcuXL9fcuXM1c+ZMmb1yG1OmTNGVV15pe/u+++7TunXr8nOMwxwTdO7QoYPatGkjE2I33aKzhqQLvCkLECgGgbQzZ5SwapXOrfERFPcAACAASURBVF5t/Zpy7JjDriZA7dehg/xvvFFeDRoUw4kF2+LI7bcr5fhxa1HVxx5TgAv+96hgN8p5durp0zrcr5/tzap9+yrggQeKY2v2QAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEPhHoP+vezRi6VGbR07B6W1H43Tz4A3WnCeuC9eALhfhh4BLCDz749+a/mdGaDrQx0M7B13tEnVRBAIIIIAAAggggEDZE7APTft6+mgZoemy9xCpGIFyJFCg0PSBAwd0/fXXZ7u+6RJtAs++vr75pklJSVHbtm0VGRlpW/Pss8+qn12QL9+blcDETZs26fPPP7e6L2cGnatUqaJrr71WDz/8cJ7h7h07dmj69OkaOXLkBYPS9qWbIHkPu862d955p/766698365Ro0b6/vvvZbpKl0bHbtMle8WKFTp69KiioqLk4+OjGqZrbXi4Fdj29PTMd+0XmhgdHa1t27ZZXbnDwsLUtGnTYtmXTZwskJqqcyZAvXy59ZVy6JBDQSY4bb78brxR7qXQ8Tx5924d7dbNVkOdIUPk7u/vZKSSO/7YwIFKOnjQOsD/yitVbdiwkjuMnRFAAAEEEEAAAQQQQAABBBBAAAEEEEAAgQoq8ObPezRq+YWD00+O2q7Zm0+pkr+nFvRppbAQnwqqxbVdSeDp0ds1c9Mpq6QAHw/tIjTtSo+HWhBAAAEEEEAAgTIlYB+a9vH01vLDazXjvtGqExxepu5BsQggUD4EChSafuGFF6xuyfbDBInnzJljhWULOvr376/Ro0fblpmw79KlS7NtM2/ePK1fv94K45pOzKardUmN/fv369NPP7UCzxcaptv266+/ni2cvH37dj399NM6+E8YsSB1Tpw4Ue3atbMtGTp0qIYMGZJti1tvvVUNGzbUypUrZcLdmcO8nrV7d0HOz+/ciIgIffXVV/rxxx9zXWI+F927d9e9994rE+a+0DDmJnhtnmtoaKjDVONpQur24fpBgwbpoYceym+5zCsjApnh6awBao/q1a3wtP8tt8i3VasSu03cb7/p1D//aMPn4otV87XXSuwsV9g4avx4nV2yxFZKnSVL5B7Ej310hWdDDQgggAACCCCAAAIIIIAAAggggAACCCBQvgRen7xbY1ae/8mLWTtOm26+pquvGW/d1UAvdKxdvgC4TZkUyAzzm+L9vT20+yM6TZfJB0nRCCCAAAIIIICACwjYh6a9Pby14gihaRd4LJSAQIUVyHdo2gRlr7rqqmxQU6dOVcuWLQsFuHjxYvXs2dNh7Zo1axwC2Ob7rl27OsxZvny5atfO3x8YmQ7Ye/futcK7jRs3vmCdJijdq1evfN/lscce03/+8x/b/ISEBF199dUOAd+cNjOBYmNWs2ZNBQUFKSAgwOoO3blzZ4fp6enpGjFihGbMmCHTzfuaa67RddddZ803Y/bs2TLh7cxx1113WWHmkhpJSUlW5+yBAwcW6AhzVxP+NkHvrMN0qn7wwQetl829Zs2apQYNGljfm+dm7pTZ6dt+7fjx4y1rRvkUMAHmePO1eLHDBX3btVNAp04KuPVWufn5FevlY0aOVPSXX1p7VrrnHoXcdlux7u9qm8WvX6+Tw4fbygobO1beJfgPUlzt/tSDAAIIIIAAAggggAACCCCAAAIIIIAAAgiUpsCrE3dp/OoI25FZg9M3D96gbUfj1CQswOo2zUDA2QKPj9ymuVsyfmKwn5e79vz3GmeXxPkIIIAAAggggAACZVTAMTTtpRVH1mnG/aNVJ4hO02X0kVI2AmVaIN+haRNSfeONNxwu+8knn6hbt26FBoiJiVHz5s0d1i9atMghXJu1G7WZPGXKFKvjdF7DdKc2YenMsXr1aiuonNP47bffrA7RBR3r1q1TtWrVrGU5hcDt9zMB8UceeSTP8HZ+a8gaKDchYvOcCjNSUlK0a9cu+fr62kLL9vts3LhRffr0seYUZphA9A8//ODQSdvsk7Wb9scff2x1qDYBdBOYzu08EzI3nxVPT8/ClMOaMiKQtHWrTIDafKWePGmr2jM83ApP+3fqJO88/jFEfq8aNXCgzk6ZYk03XaZNt+nyPNLi4nTo1VdtV6z+ySfyu+mm8nxl7oYAAggggAACCCCAAAIIIIAAAggggAACCBSbwIxNp7QzIl6t6wepwyWOP0Uzt0NeHr9Tk9Yet71tH5z+ZtFhDZixz3rvq0cuUZdW1YutVjZCoDACj/2wTb/9lRGa9vFy1z5C04VhZA0CCCCAAAIIIICAJPvQtJeHp1YeWa+Z949WbULTfD4QQMAJAvkOTb/wwguaOXOmrcSbb75Z33//fb5KNt2SBwwYoCZNmuj9999X27ZtrXXx8fHWa/bDPoRsXr/nnntkArv2w3RYbtq0aZ5nm47Iw4YNs80zNTz66KPZ1i1cuFCma3TWYToj9+3bV61atdKOHTs0btw4zZkzx2Ha4MGDbZ2wLxSaNmf36NFDbm5uedad3wnbt2/XbXbdcE29JkhcmGH/fLN2D89PoLxjx45q06aNdfTKlSu1dOnSHMv47LPPHILsWcP4Jpj/7LPPWu6mS/iFxi+//KLWrVsX5rqsKWMCaWfOWMHp+AULlLBmjUP1/jffbHWeNr8WZZz49791bsUKa4vw/v3lFV7+/zXbsQ8+UNLhw9adK/furaAePYpCyFoEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKDCCNz39Wat3B1j3ffaRpXU7coaur9N3kHnXmN36Of1J2xOmcHp0ABP3Tx4o06eTdLNl1XWj0/m/fdgFQabizpFoOeIrZq3Lco629vTXfs/ptO0Ux4EhyKAAAIIIIAAAuVAwD407enuqVVHTWj6R9UOCisHt+MKCCBQ1gTyHZq+88479ddff9nul9kROK8Lb9myxeoYnDlMx2ETLq5evboVhjahaPv3tm3b5rDlddddp4MHD9peMx2GlyxZInd397yOVta1r732mnr16uWwLiIiQldddVW2ve6//3599NFH8vLycnjPdIs29WeOrl27ygSnzUhOTrYC1FlD3plzTVjchKcvvfTSPGvPz4QDBw7o+uuvt02tUqWKNmzYkJ+l2ebYW3Xu3FlffPGFbU7W8HnWxSY8b0L09uPQoUP66quvcux8/fnnn6tLly7W9KyB7FdeeUVRUVEaNWpUnvd4/vnn9frrr+c5jwnlSyBxyxbFL1xofaUcOmS7nOk47X/rrVaA2rN27QJf+ui99yp5/35rXe1Bg+QRmr/OIAU+yIUWnPzmG8Vv2mRVFNy9u0L535MLPR1KQQABBBBAAAEEEEAAAQQQQAABBBBAAAFXFjBdpj+avV9z/+nEa2ptVitQ3a6srq5X1lCIX+4/KfO5MX9r2sbzP10xMzg9ce1xmY7TZkzt1UJtGwS7MgG1lXOBHsO3asH2jNC0l4ebDnxybTm/MddDAAEEEEAAAQQQKCkBx9C0h1Yd3UBouqSw2RcBBPIUyHdo2nRbjozM+BFMZnzyySfq1q1bngeMHj1a/fv3d5h3ww036LvvvtN9993nEMQ23YpHjhxpm3v27Fk1a9bMYa0J1b700kt5nnv69GldccUVDvNyqrlPnz7ZOhqbYPWrr76arSv09OnTs4Wuswaxz5w5oyeeeEJrsnTDtS/EdLt+5plnVLsQwU77fYozNH3jjTdq79691vam+/fcuXNtR5nu0CbonHWEhYVpzJgxatSoUa7Pw+xpgub2wXcT7jadqE2AfsKECXkGn8080638008/lelEnjmK0lk7zw8QE1xeID0lJSM8PX++9ZU53Hx9ra7T/jfdJP8bbsj3PQ62a6f0pCRrft2hQ+Xm45PvtWV1YuSPPyr2n+7a/tdeq2p2/1iirN6JuhFAAAEEEEAAAQQQQAABBBBAAAEEEEAAgdIUWLYrWpPWHteUdee7R9cO9bE6T5vwdL0qvjmW8/To7Zq56ZTtPROcfv32+npiZEZzoZ5Xh2nQ/ReX5lU4CwEHgYe/+0uL/j5tvebp7qaDgwlN8xFBAAEEEEAAAQQQKJyAfWjaw91dq49u1Kz7f1QtOk0XDpRVCCBQJIF8h6Zvu+02bd++3XZY79699fLLL+d5uAm7mu7KWYcJwsbFxTm8bALTJjidOU6dOqXWrVs7zDEhXfvuyrkV8NZbb+mnn35yeNt0yg4KCrK9tnXrVt1xxx05btGiRQt16tRJJhicmpqqKVOmaNWqVdnmjhs3Ttdc4/jjqFJSUmRef+eddy7o0717dz333HNq0KBBno45TdizZ4+DlzHN2qk7vxtnfb4mkJ05FixYoMcff9xhKxN8njlzpsLDw/M8wnTzNs72ofvhw4fr1ltv1f/+9z8rgJ/bMHeaPHmymjZtqqz3NWv279+fLdyeZ0F2E5K2b1dabKyUmqr0tDSHX5WWpvTUVJlfHX6f29x/1tvm2s3LcX/7fTLPMa/983vrV/OVnl6QK1WsuW5ucvP2tsLOqZGRSo2IUFp8vM3A3d9fHjVrKnzKlDxdDrZvr/TERMndXfW+/jrP+eVhwulfftGZ33+3ruLVsGG+nMrDvbkDAggggAACCCCAAAIIIIAAAggggAACCCBQ3AJ/HYnVpLUnNHntccWcS7G2D/TxsMLT5qt5nUCHI9PSpadGbdOcLecbFpngdP0qfvp9a6SCfT21oE8r1Qot/w0+ivtZsF/xCDw07C8t3pERmvZwd9MhQtPFA8suCCCAAAIIIIBABRSwD027u7lrzTETmh6jWkE1K6AGV0YAAWcL5Ds0/eKLL2ratGm2ek034lmzZsnDw+OCd5g6dWq+OkObcPKyZcvk6Xn+x5UdP35cbdu2ddjfnJm1+3TWAv7880917tzZ4WXT3frHH390eO3BBx/Uin+6rBbmQZjaxo8f71Cz/T4mLDxw4EAHt5zOuffee63wdOPGjQtUhgmxm7Bz5sgrNJ2WlqbExET5+fllO8d0g168eLHt9Q0bNsgEo80wr5v37ce3336r22+/Pd/1Zg1Hv/DCC+rbt68VqDfB+txG1udt3xHbrNm0aZMqVaqU7zrsJybv3auj999fqLUsKlsC9TZsyLPgQx07Ki06Wu6BgaozeHCe88vDBBOYNsFpM0xn7borV5aHa3EHBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDAaQKHTydanadNePpAZIKtjgfa1tCDV9XUlQ2Cba8lp6bryVHbNG9rlO0106Xa7GHGm3fW179vquO0u3BwxRZ44Nst+mNndMbfIbhJRz69rmKDcHsEEEAAAQQQQACBQgs4hqbdtObYn5rVdYxqBRKaLjQqCxFAoNAC+Q5NDx06VEOGDHE4yHQI7tat2wUPj4mJUfv27bN1lc666N13383Wzdisbd68ucPUH374QTfddFOuZ5ouzyYwbbpK2w/T1dh0N84cu3fvzrZPTt2vczvIzP39999Vu3btPPGPHj2qCRMmWOHgrN217Rffeeed6tWrl0wgPT8jp87L9h2i7fdYv369nnrqKavb84cffqhHHnnE4YjXXnvN6uicOUaPHq0OHTpY3+YUmt6yZYuCg8//wV5e9Wbd3wSmTXA6axjffp+vv/5axsR+ZA1ZL1y4UBdddFFex+f4furp04r+/HPFzphRqPUscn0B9+BgedaqpbCxY/Ms9siddyrl2DF5VqumWjl0x89zgzI4IXbFCkX+849JTFfuOsuWlcFbUDICCCCAAAIIIIAAAggggAACCCCAAAIIIOB6ArGJqVZ4etKa49p8ONZWYJdW1fVQuxq65uKMhjDnktOsjtMLt2d09DUjwMdDcYmpahIWYHWbLumxZMdpJSSnWcc0CQ9Q3cq+FzwyJS1dC7adD3rnZ01J34H9i1+g2zdbtGxXRmjajKNDCE0XvzI7IoAAAggggAACFUPAPjTt5uamtcf+1OyuYxROaLpifAC4JQIuJpDv0LQJIWcNsJrgcP/+/XX//ffn2m3Z3PePP/5Qjx49cr262Wft2rUyv2Yd9erVc3gpa/g56/x33nknW0dpM8d0RTbdkTPHF198oU8//dT2fcOGDa3O2SNHjtRXX311wXDz1Vdfrc8++0w1axbsX7uYLs8mgGzOzhrqtr+HCTSbe/j6XvgPpc6ePZut6/bWrVsVGOj4I97M3o899phMwNiMunXraunSpQ50//3vf2VCypkjsxO0+T6n0HRu4eycHvK+fftsAezM97/77jt16tRJuXX7Np+XDz74INt2kyZNUp8+fWyvF7TjddYNU44eVdTHHys9Lk5KT1d6err1q+3LLPjn+2zvmdfzev+fOTmuzTzHOiLLuXbfW6dc6P0sddjPte1rv4eL/UeopMrxbdNGNb77Lt/bm67jpvu4d716CuvXL9/ryvLE+E2bdPKbb6wreNasqVqzZ5fl61A7AggggAACCCCAAAIIIIAAAggggAACCCDgkgLjVkdo3KoIbThw1lbfnc2r6qF2NXXjpaEyAeunRm2XCS9nHV8+fInua129xO5l/oqhzYA1Ohad0d36svAAzX/twkHtMSuP6fXJu201zendUi3qZP+7qRIrmo1LReD+rzdrxe4YPXp1mBWq//zBgv3E3FIpkkMQQAABBBBAAAEEyoSAfWjaFGy+n931J4UH1igT9VMkAgiUL4F8h6bNte2Dt/YMpjPym2++qSuvvFJ+fn4OQgcPHpTpDm3CyLmNV155RS+99FKOb3ft2lVr1qxxeO/zzz9Xly5dHF4zHZxN8Nd0SM5phIWFadWqVba3nnjiCc2fP9/2/UcffWQFeM0we/36668y3ZlNR+XY2Fir+3OrVq10/fXXW0FlDw+PQn8STJB10aJFMvfYtGlTjvu0bNlS48aNk7+//wXPyRoqX758eY7dry+77DKHIHjW0LN5Ru+9957trBYtWmj69OnW92bPhx56yKGOGTNmZOsCnlOhq1evtp7tsWPHbG9XqVJF8+bNk/m1Xbt2Du+ZSY0aNdLMmTNzDI2bz4L5TGQO0z377bffLvSzqJAL7QPZuQTEcwyKG6zc1uYQ3L5QyDzXkPg/Z+T0fmpUlBI3bFDChg1KzPK/G6+LLpLP5ZdbX57mH1qkp8urYUN5hIbm+xEf69FDSVu3yqd+fdV84418ryvLE89t3aoTX35pXcG7cWOFTZhQlq9D7QgggAACCCCAAAIIIIAAAggggAACCCCAgEsLTFl3wgpPr9obY6vzlqaV9Ui7MLVtGKwnR23XcrvOvmZS2wbBmtqrRYnea/bmU9bZmePnF5qr/UUhOZ6Zmpau5u+u1um4ZOv9axtV0qTnLi/R+tjcOQIDZuzTzE2nNOX5y1Unj+7jzqmQUxFAAAEEEEAAAQTKioB9aDpd6VofsZnQdFl5eNSJQDkUKFBoeuPGjbrnnnsuyNCmTRurk/HJkydlgrkmNJ3XeP/999WzZ88cp02bNk0vvvhitveee+45mWBx5cqV9eeff+qbb75RZGTkBY/65Zdf1Lp1a2vOzTffrF27dtnmjxkzxgpEF9cwHZG9vLysDsuhuQQ3TTB0yZIlGjJkSI7h6by6aptab7zxRu3du9dWtgmnd+zY0eEaK1eu1AMPPGB7zYS+TVdt+2FC0P/+979tL5mu39u2bbO+Nx2yjbUJk2cOE0L/6aefdPHFF+dIZmwnTJigESNGZHt/7Nixuvbaa5WcnJzj+ilTplgB/JzG6dOndcUVV1zwLsX1DNnH+QJpMTE6t3y5zi1bpvglS5R+7pytKJ9mzeR3ww3yv+kmedWvX+Rijz/9tBLWrZO7v7/qDBlS5P3KwgaxS5cqcuxYq9SCduYuC/ejRgQQQAABBBBAAAEEEEAAAQQQQAABBBBAwBUFpm08KdN9eunOaFt5Jjzd+Ypq+mmlY6jaTPjsgcbq3rZkO7BdPXCd9p/K+DP4K+oGafbL5/8uxt5w0trjenn8TttLC/q0UpOw7D9J1hXdqQkBBBBAAAEEEEAAAQScI5BTaHpO158URqdp5zwQTkWgggsUKDRtrEzAd+jQocXKZjoOL1u2LMeuymlpaerevXu2btN5FWD2TEhIcAj6tm3bVpMnT7aWXnfddQ6B7h49euiDDz7Ia9t8vX/48GFdc801trmmE/Ljjz+ea3dqE542Xa9NeDxryDyvMPcjjzyipUuX2s4yAfNXX33V9v3Ro0d17733OnRzNl3Bn3nmGYe7mC7cxjlzNGzY0OqGnTkGDx6sL//pSGu/0OxtukWbDuOnTp2SOW/BggUOQW77+QMGDNCjjz5qvZQ1AG1eMx3ETQfuC42sgXcTms8tmJ6vB8YklxKwBaVXrLAC0+b7zOFzxRXya99evu3by4Smi3NEf/mlYv7piF/7v/+VR0jOXTSK80xn7xU9bZpi5syxyvC/8UZV+/RTZ5fE+QgggAACCCCAAAIIIIAAAggggAACCCCAQIURmLMl0uo8vWB7lO3O1zcO1dHoRO0+EW97LcTPU9N6tVDjmhf+6aRFgVu4/bQeGf6XbYsZL7ZQ6/rBDluaHzrZ6r3VOn4myXr9lssqa/STTYtyLGsRQAABBBBAAAEEEECgAgjYh6bT0tO04fgWzek6VmGB1SvA7bkiAgi4mkCBQ9MmxGzCsyY8XZzj2WefVb9+/XLc8syZM+rdu7cVLM7PaNSokUaNGqXZs2frww8/dFhiwtl16tSxgrumy7P92LBhg0zYuqhj+fLleuihhxy2MZ2aX3jhBasLtIeHR45HREVFWR2Y7Ts65xXmNgFp05nZfhhHc4/169dr/PjxDu+ZDtKm83RIlkDo/v37dcMNN9jm2gfMzYsm4Hz77bc7hK8L6mS6Tt9yyy22ZUlJSerUqZNDwNrY1a5d+4Jbm3D78OHDbXM++eQTdevWraDlMN/FBOIXLVL8ggXZgtK+bdvagtLejRuXWNXnlizRid69rf1rvPKKfEvwrBK7RAE3PjVypOJWr7ZWBXburCrvvlvAHZiOAAIIIIAAAggggAACCCCAAAIIIIAAAgggUFSB+duirM7Tc7ec/4mqJigdcy7FtnXDan764bHLSjQ4fdMnG7T9WMZPHb2qYYh+/Xdzh6tN3XBSz//0t+21Zf3ayNSV24hPStX2o3HaciROsQkpqh7srcvCA3RJzQB5ebjliy06PkXrD5xRZGxyxldcsrzc3VQ50EtVArxULchb1zWulK+9mIQAAggggAACCCCAAALOEbAPTaemp2rj8b80p9tYhQUQmnbOE+FUBCq2QIFD05lce/fuVf/+/R26HOdFaboiP//88/L19dU999yTravy119/rTvvvDPHbVJTU60OxF988UWux5hA8FNPPWWd4ePjY3Wa7tmzp0wX5cwxd+5cNWnSRCbAa7oe248WLVpowoQJOXa8zutu9u+vXbtW999/f45L6tatqzZt2lihZhNcdnNzkwkPb926NcdQuAkqf/vtt7keP3HiRPXt2zff5Zlu1sYk6zBh+KuvvtoWijYdpD/77DOHaaZGE+KOjDz/h3Z5HWzu+dhjj1ldrKtXz/5/6CIiItS1a1frs/Dyyy9b4fi8huloff3119vC5eZz+MQTT+S1jPddUCB5717Fz5uns7/8otSTJ20V+l13nfzatbM6SnvVr18qladEROjIHXdYZ1V5+GEFXnddqZzrzEMiBg9W4u7dVgnBPXooNB//+3NmvZyNAAIIIIAAAggggAACCCCAAAIIIIAAAgiUZ4ElO05r/Orjmv7n+T8vt7+v6TQ948UrFOSbc3Oeotqs3BOj+77abNvmt1da6vLagdb3pst02w/W6MjpROv7zi2r6Zsel+Z65I8rjumNKRl//px11Aj21g+PX6aWdYNyXW8sBv92UOv3n7ngtXw83bXv4/M//bWoBqxHAAEEEEAAAQQQQACB4hfIKTQ9t9tY1SQ0XfzY7IgAAnkKFDo0nbnzihUrNH36dP3555/avn279bIJL9eoUUO1atVS48aNZTo/m5Cr+T5z7NmzR//6178cuiqb96ZNm6Yrrrgi18LNOT/88IPWrFljBXzNWTfffLPVJfm2226zvrcfZ8+e1ciRIzV58mSFhobq119/tTo9Jycn69Zbb3XocmzWmU7Qn376qSpXrpwnnplgukPv27dPJ/8JfJoaTFj7rrvu0q5du/K1x4UmvfTSS3rllVdynZKYmKj27dvnK8hsul8PHDjQCmrnNCZNmqQ+ffpYb5lu4nfffXe2aeZu5nnPmzdPpiu0fVds+8kdOnSwgtLG2NPT84IOJoRtwtNNm+b/R7iZGnr16qWwsDDrM2M+b4yyIxA/f75iZ8zQuaVLbUV7N2mioPvvt4LSnjVrOuUyRzp3VsqhQwru1EmhXbo4pYbSPPTIm28qJSrjxz5W7tdPQV27lubxnIUAAggggAACCCCAAAIIIIAAAggggAACCCCQg4AJL49bFaGf15/I9u7ivq1LtNv0XUP/1IYDZ61zTQfnic9ebv1+9uZTenJUxt8DmrH2nbaqFeqT4/N7fOQ2h67ZuT3kEf/XRHc0r5rt7dV7Y9Tlf+fD2xf6kJhO16bjNQMBBBBAAAEEEEAAAQRcV8A+NJ2SlqI/T2zV3G7jVDOgmusWTWUIIFBuBYocmi6KzMqVK/XAAw84bGE6E5tAbO3atYuydb7WLlu2TA8//HC2uSZ43a9fP6tbtJ+f448VS09P199//60lS5ZYweF169Y5rDeB7ptuuknnzp3T//73P+ursMN0xDZB5uDg4Atu8fPPP18wWG32eeONN2SCzHkNE/42IfArr7wy13B15h6mO/WBAwcUHx+vlJQUa35gYKAVjjedvkt6mMB6fsPtJV0L++dPIHbqVMVOn67EP/+0FniGhyvwX/9SUPfucq/k/B+fd+rNNxU3d678W7dWtaeeyt+lyvCsA88+a6u+zqJFcg8JKcO3oXQEEEAAAQQQQAABBBBAAAEELwLnTQAAIABJREFUEEAAAQQQQKB8Cazbf0bDFh/RrM2nrIs9dm24Prz3ohK95MaDZ3Xn5xl/hm/Gwj6tdGlYgK4euE77T52zXnvgqhoa0r1xjnXM2xqlnt9vtb3n6e6m2y6vompB3tp9Il5Ld0bb3gsN8NKf/7lKXh7nm/2YjtYNX1+uxJQ027wAHw9d1TBYYSE+CvTxsP4+KiE5Vadik63aXrm1bomasDkCCCCAAAIIIIAAAggUTcA+NJ2clqJNhKaLBspqBBAokoBTQ9OmctMB+rXXXnO4hOkebILA9p2pi3TLCyweMmSIhg4dmusM0yX7sssuk5eXl/bv3291086tu7LZJGuHZtNtetSoUfrpp58KdIW+ffvq8ccfzxbazmkTE17+6quvNHjw4PN/gBQQINNZ2nS8vlDn7gIVxWQEiiCQsG6djj/9tLWDCUuHPPGEAl2sm/OZ0aN1euhQeVapovB335Wbt3cRbuzaS2OXLlXk2LFWkX433KDqn33m2gVTHQIIIIAAAggggAACCCCAAAIIIIAAAgggUEEFRi07qglrjmvuKy1LRaDbN1u0bFdGuPmWyyqrx9VhenTE+SD0n+9dpepB2f/8PC1dunLAGh2LTrTW1gj21syXrnDoSG32NftnDhMCN2HwzGGC2SagnTm6t62hT7s3lnvOP0S1VDw4BAEEEEAAAQQQQAABBIom4BiaTtamE9v0W7dxqkGn6aLBshoBBAol4PTQtKn6s88+0+eff+5wAROcnjJlSql0nDadrXv16lUoQPtFpuZx48apYcOG2fYyQeudO3dq9+7d2rt3r/bs2aPExESFhIQoNDTU6phsfm3atKmaN29uhbQLOmJiYqz9/f39ZcLenp6eBd2C+QiUqED0sGG27tIlelAhN09YtUrHn3/eWl2lZ08Ftm9fyJ1cf1nEf/+rxH37rEKrfvCBAu64w/WLpkIEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBEhfYfjRONw3eYDvHhJ+Pn0myvn/8unB90CXnbtd7T57TtYPOB56nv9hCbepn/2mqL4/fqUlrj1v73d+mur546BLbWWv3nVHnLzfZvu9zWz31ppN0iT9zDkAAAQQQQAABBBBAoCQF7EPTSalJ2nxyO6HpkgRnbwQQuKCAS4Sm09PT1adPH6vrtP0w4eNFixaVyiPcunWr3nrrLW3cuLHA55mw9G233abevXtbIWgGAgiUXYETzz2nc6tXy69ZM1X/97/L7kUuUHnSwYM6NnCgNcPN01O1582TO//tKpfPmkshgAACCCCAAAIIIIAAAggggAACCCCAAAKFEeg5YqvmbYtyWOrp7qZN712l0ICcG/8s2B6lHsMzOlKbOVsHtMvx6NmbT+nJUdut966oG6TZL19hmxcdn6LL3l7psO6Smv7q0qq6rmtcSc1rB8qDttOFeaSsQQABBBBAAAEEEEDAaQL2oenE1CRtObldv3cfr+r+VZ1WEwcjgEDFFXCJ0LThT0lJ0fjx4zVo0CCZrsyZ48CBA6X6dNavX6/hw4drzpw5uZ4bEBCgDh066IYbblD79u1Vt27dUq2RwxBAoOQE4ufP18m+fa0Dwt54Q97165fcYU7a+fTkyTqzYIF1uukwbTpNMxBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyBTYc+KcrvvofNdo8/rzHWvr7bsa5Io0/I8jenfqXut9E7C+NCwgx7kRMYk6FZtsvVc10Eub33cMV78ycacmrM7oRJ11mH2vbBBs1XJTk8o8MAQQQAABBBBAAAEEECgDAvah6YTURP118m9C02XguVEiAuVVwGVC05nAMTExmjhxonbs2KFrr71WXbp0cYp9amqqIiIidPDgQZ06dUpeXl4KDw9XnTp1FBoa6pSaOBQBBEpH4Pj//Z8SNm9W8K23KvTee0vn0FI85chbbyklMtI60QSmTXCagQACCCCAAAIIIIAAAggggAACCCCAAAIIIICAvcCzP/6t6X+etF4yYeWtH7RXkK9Hrkgfzd6vL+YfKhBiTqHp9HTp1w0nNGDGPh0/k5TrfqYD9bePNpH5lYEAAggggAACCCCAAAKuK+AQmk5J0F+ndhCadt3HRWUIlHsBlwtNl3txLogAAi4vEDdtmk699548q1RR+Lvvys3b2+Vrzm+BcevW6dSIERl/yB0WprBx4+QeEpLf5cxDAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQqCACo5Yf05s/77Zu2yQsQAv6tLrgzSetPa6Xx++0zQn29cxT6pIwf03r1SLHeSY8veHgWc3bGqmF20/rryOx2ebVCPbWmnfaysvDLc+zmIAAAggggAACCCCAAALOEbAPTZ9LSdDWUzs0r/sEVfOv4pyCOBUBBCq0AKHpCv34uTwCCOQmENG9uxJ37VLlBx5QUIcO5Qbq+JAhStiZ8YfWld96S0H33Vdu7sZFEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBIpPoKCh6U2HYnX7ZxttBazr31bhlXyKraCklDT9sTNa70/fp90n4m37zundUi3qBBbbOWyEAAIIIIAAAggggAACxStgH5qOTzmnbad2EpouXmJ2QwCBAggQmi4AFlMRQKDiCMSOG6fIwYOtC4e/8468atUq85ePnjlTMTNnWvfw79BB1YYMKfN34gIIIIAAAggggAACCCCAAAIIIIAAAggggAACJSNQ0NB0XGKqGvVbYSvmhktCNfbpZnIv5ibQK/fE6L6vNtvO+abHpercslrJILArAggggAACCCCAAAIIFFnAITSdHK9tkbsITRdZlQ0QQKCwAoSmCyvHOgQQKPcCUW+9pbNz5sgjOFi1Bg6Um2feP0rQVVFMd2nTZdoMNy8v1fj+e/k0a+aq5VIXAggggAACCCCAAAIIIIAAAggggAACCCCAgJMFChqaNuW+OnGXxq+OsFXe/qIQDehykS4LD8h2m8SUNPl4uju8npyariPRiaoT6iOPHNLWUXHJenHcDi3cftq2blqvFrqyQbCTtTgeAQQQQAABBBBAAAEEchOwD03HJcdre+Quze8+UVX9K4OGAAIIlLoAoelSJ+dABBAoSwLHe/RQwtat8mnQQDVff70sle5QqwlMm+C0GZWeeUYhzzxTZu9C4QgggAACCCCAAAIIIIAAAggggAACCCCAAAIlL1CY0LTpNt36vTU6k5DiUGBogJdqVfKRj5e7YuJTdCgqQf4+Hto6oJ3DvLX7zqjzl5us14J9PVXJ31OBvh5KSklXdHyyTsUmO8w3c9a/21YBPh4lD8IJCCCAAAIIIIAAAgggUCgB+9B0bHKc/o7crfkPTFRVP0LThQJlEQIIFEmA0HSR+FiMAALlXSBp61Yd69HDumZAmzaq+uSTZe7K0TNnKmbmTKtu013adJk23aYZCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgjkJlCY0LTZa+PBs3py1HYdi07ME/fQ4GsdOkpPXndCL43bkee6zAm/v9pSzWoF5ns+ExFAAAEEEEAAAQQQQKD0BRxC00lx+juK0HTpPwVORACBTAFC03wWEEAAgTwEYr79VtHffWfNCunUSZW6dCkzZrErVypy9OiMet3dVW3wYPl36FBm6qdQBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAecIFDY0bapNTUvXt4uPaNjiw9m6Q9vfZn3/tgqr5GN7acQfR9R/6t4LXtjH012dW1bTE9eF6/LaBKad8+ngVAQQQAABBBBAAAEE8i9gH5o+mxSrHVF7tOCBSariF5r/TZiJAAIIFJMAoeligmQbBBAo3wLHn3pKCevXW5f0a95c1Z9/3uUvbLpLmy7TZrj5+qraRx/J7/rrXb5uCkQAAQQQQAABBBBAAAEEEEAAAQQQQAABBBAoPwImQH0oKkHHYpKUkpouP2931arko5ohPnJzy37P5NR0HYxMUGRcspJS0qwJHu5uqhroZX1V8vfKcV35EeMmCCCAAAIIIIAAAgiULwH70PSZpLPaGbWX0HT5esTcBoEyJUBoukw9LopFAAFnCSTv3auTffvK/GqGd+3aqt6rlzxCQpxV0gXPjRwzRrHLl1tz3ENCVG3QIPm2a+eStVIUAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQPkUcAhNJ57VztOEpsvnk+ZWCJQNAULTZeM5USUCCLiAQNbgtEelSqrSo4f8mjZ1geoySkiJjFTUuHE6t3Wr9b1H9eqqOnCgfFu1cpkaKQQBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEECgYgjYh6ZjEs9o1+l9WvjAZFX2q1QxALglAgi4lAChaZd6HBSDAAKuLpA1OO3m4aFKnTsr+NZbnV563Jo1ivntNyUfOWLV4lmnjqp++KF8mjVzem0UgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIFDxBOxD09GJMdp9er8WPjhZlX0JTVe8TwM3RsD5AoSmnf8MqAABBMqYQNbgtCnf56KLrOC0f4sWpX6bxN27dWb+fMX/+aftbO9LL1WV//xH3o0bl3o9HIgAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgBGwD02fTozRHkLTfDAQQMCJAoSmnYjP0QggUHYFTHD69Jdf6tySJQ6XCLz6agV36iSvGjVK/HKpp09bYekzCxbYznIPDlZwjx7Wl5u3d4nXwAEIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCOQm4BCaTojWnugDWvTgZIXSaZoPDQIIOEGA0LQT0DkSAQTKj8CZ0aMV/c03Sk9Ksl3KPTBQJjwd0KaNvOvWLdbLpsXF6dzWrTr311/Wr+b7zBF4770KfuQRedWvX6xnshkCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAoURsA9NRyVEa68Vmp6iUN+QwmzHGgQQQKBIAoSmi8THYgQQQEBK3LzZCk4nrF6djcO/RQv5t2ljBajl5lYorgsFpc2G/h07KvC+++TXvn2h9mcRAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIlIWAfmo5MOK190QcJTZcENHsigEC+BAhN54uJSQgggEDeAtHDhilu2jSlRERkm+xVs6YVnvauU8f68qxcOdcNk48dU9LBgw5faYmJDvPdg4MVcMcd1pdPs2Z5F8cMBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQRKWcAhNH0uSvtiDmnxg1NUiU7TpfwkOA4BBIwAoWk+BwgggEAxCqTFxCh22jTrK3nfvlx39qxaVd716sm7bl2lnjmjtLNnlXr2rJKPHLG+z214N2qkgNtvt8LSHtWrF2PlbIUAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQPEK2IemT52L0n4Tmn7oZ1XyCS7eg9gNAQQQyIcAoel8IDEFAQQQKKhAemKiYqdPV9zs2UrauVPp584VdAu5BwbK+5JL5NW4sfVr5leBN2IBAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAJOELAPTZ88F6kDMYcJTTvhOXAkAghkCBCa5pOAAAIIlLBAenKyknftssLTSbt2Wb938/WVm5+f3P38rF/Nl2fNmvK66CKrGo8qVeRVv34JV8b2CCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgiUnIBDaDr+lA6cOaIlD/2sEDpNlxw6OyOAQK4ChKb5cCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQLEL2IemT8Sf0kErNP2LQnyCiv0sNkQAAQTyEiA0nZcQ7yOAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQIEF7EPTx+NP6tCZo/rjoV8UTGi6wJYsQACBogsQmi66ITsggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEAWAfvQdETcSR0+S2iaDwkCCDhPgNC08+w5GQEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAIFyK+AYmj6hw2eP6Y+Hf1Wwd2C5vTMXQwAB1xUgNO26z4bKEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEECizAvah6WNxx3XkbISWPvyrgghNl9lnSuEIlGUBQtNl+elROwIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIuKmAfmj4ae1xHYwlNu+ijoiwEKoQAoekK8Zi5JAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAKlK+AYmo6QCU4vfXiqgrwDSrcQTkMAAQQkEZrmY4AAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAsUuYB+aPhJ7TMdiT2jZw1MVSGi62K3ZEAEE8hYgNJ23ETMQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKCAAvah6cNnjykijtB0AQmZjgACxShAaLoYMdkKAQQQKIpA7C+/KPKDDxR4772q9Mwz8qhWrSjbsRYBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAacKOIamjyoi7qSWPTJVgV4BTq2LwxFAoGIKEJqumM+dWyOAgAsKxAwbpuhhw6zKagwfLt/WrV2wSkpCAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCB/Avah6UNnj+p43Ektf2SaArz887cBsxBAAIFiFCA0XYyYbIUAAggURcA+NF3zhx/kc8UVRdmOtQgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIOFXAPjR98MwRnYg/RWjaqU+EwxGo2AKEpiv28+f2CCDgQgIOoelRo+TTvLkLVUcpCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggUTMA+NH3gzGGdjI8kNF0wQmYjgEAxChCaLkZMtkIAAQSKIuAQmh4zRj5NmxZlO9YigAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIOBUgZxC0ysemS5/Lz+n1sXhCCBQMQUITVfM586tEUDABQXsQ9NhY8fKu0kTF6ySkhBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKCsCizZcVqHohJ1V6uamr31tJbuiNbfR88qOSVNSSlpSk5NtX5vfZ+apsTkNDWo6qewEG/Vq+qny8IDrK8mYQGq5O9ZVhmoGwEEEEAAAQQQQKAUBexD0/tjDunUuSgRmi7FB8BRCCDgIEBomg8EAggg4CICDqHpCRPk3bixi1RGGQgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIFCWBeZsidRXi49qw77oYruGCU/feGmoOrespma1AottXzZCAAEEEEAAAQQQKF8C9qHpfTEHFXnutFb0mC5/TzpNl68nzW0QKBsChKbLxnOiSgQQqAAC9qHp8EmT5HXxxRXg1lwRAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEESkIgJS1dY1Ye18S1J7T5YEy2I7re0Eh1qwcprHKAjkXG6WhUnM7EJSk0yEehQb6qHOijIH9v27qPJqzTqZhzOZZqwtOt6gVZXalvu7yK/L09SuJK7IkAAggggAACCCBQBgVyCk2v7DFDfp6+ZfA2lIwAAmVdgNB0WX+C1I8AAuVGwCE0/fPP8mrQoNzcjYsggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIBA6QhExibr+xXH9fO64zp0Kr50DrU7pXmdYPXr3Eg3NPQv9bM5EAEEEEAAAQQQQMD1BOxD03ujDygqIVqEpl3vOVERAhVFgNB0RXnS3BMBBFxewCE0/euv8qpXz+VrpkAEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwDYE9J85pzKrjmrzuuE7HJuVYlJ+Pp0ICvBXo5y1vT3d5e3rIy/5Xr8J3iN52IFKHT8bazr2qURX1v7OuWtYNdA0gqkAAAQQQQAABBBBwioB9aHpP9AGdJjTtlOfAoQggkCFAaJpPAgIIIOAiAvah6VrTp8uzdm0XqYwyEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAwFUFdh6P1w9Lj2rCmuNKSkmzyqwW4qfa1QJVu1qQalcNtILSwQE+VlC6JMfSLUd0NDJOa3cct44x5z3Zoa7evqNOSR7L3ggggAACCCCAAAIuLOAYmt6v0wkxWtVjpnw9fVy4akpDAIHyKkBourw+We6FAAJlTsAhND1rljzDwsrcHSgYAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEESkfgXFKavl18WN8uPqKzCSnWoReHh+iaZuG6vEHV0ikil1N2HYmWCVBvOxBlzWhRL0Rv3l5X1zWu5NS6OBwBBBBAAAEEEECg9AXsQ9O7T+9TdOIZQtOl/xg4EQEE/hEgNM1HAQEEEHARAfvQdO05c+RRo4aLVEYZCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4EoCU9adsALT247GWWWFVwnQtc3C1fbSmq5UpjbuPmmFpw+eOGvV9XzH2nr55roK9PVwqTopBgEEEEAAAQQQQKDkBOxD07tO71MMoemSw2ZnBBDIU4DQdJ5ETEAAAQRKR8AhNP377/Ko6twuEKVza05BAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACB/ArsPhGvQbP2a86WSGtJaJCPFZa+tmm4PDzc87tNqc+bvGSXVv8dYZ3brFag/vfwJWpc07/U6+BABBBAAAEEEEAAgdIXsA9N7zy9V2cSz2r1o7Pk4+Fd+sVwIgIIVHgBQtMV/iMAAAIIuIqAQ2h6/nx5VK7sKqVRBwIIIIAAAggggAACCCCAAAIIIIAAAggggAACCDhZYPK6Exo0a58iYpKsSlpeXE13X91QQX5lI2wyZ81+Ldh4yKrdBKYX923tZFGORwABBBBAAAEEECgNAYfQdNQenUmKJTRdGvCcgQACOQoQmuaDgQACCLiIgH1ous6iRXIPCXGRyigDAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEnCnw1i97NHLZUVsJV15SQ907NHZmSYU6++9DpzVi9l/WWoLThSJkEQIIIIAAAgggUOYE7EPTO6L26Cyh6TL3DCkYgfIkQGi6PD1N7oIAAmVawCE0vWSJ3IOCyvR9KB4BBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQSKLvDS+J2avPa4baOrm4bp3msvLvrGTtzhtWFLrdPvb1NdXzx0iRMr4WgEEEAAAQQQQACBkhawD03/HbVbsUlxWvPoLHl7lI2fmFLSPuyPAAKlK0BounS9OQ0BBBDIVcAhNL1smdz9/dFCAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBCizwn2l79d2SIzaBG1rU1r/aNSjzIsci4/TplA3WPZ64LlwDulxU5u/EBRBAAAEEEEAAAQRyFnAITUfuVmyyCU3PlreHF2QIIIBAqQsQmi51cg5EAAEEchawD03XXbFCbr6+UCGAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEAFFfhpZYT6Tt5lu/0treuqU5t65UZj/a4TGr9wh3WfVzvV06ud6pabu3ERBBBAAAEEEEAAgfMC9qHp7ZG7FJccr7WPzpYXoWk+Jggg4AQBQtNOQOdIBP6fvfuAj6pK+zj+pFdCCiRA6FU6gvSmIiJ2UVGxgn1dxYJdERVFXXXFV12xgx27iFhQFCkivfcOgRTSe30/5yYzzJ1ML8lk8jufZSEz957yPScRkv88gwAClgR0oelVqyQghFfUcVIQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEGisAue8vF42HcnXlt+nYzO5bmx3v6P44e/98sfGIxIdFiQLpvaTbi14F06/22QWhAACCCCAAAKNXsA0NL3txC4pLCuS1df/KCGB5GIa/eEAAIF6ECA0XQ/oDIkAAghYEjANTbdbvVokKAgoBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQaIQCplWmm0aFym3n95HmsRF+KfHWwi2y60iWXDkoSV6+sqtfrpFFIYAAAggggAACjVlAF5rO2CWF5So0vUhCAoMbMwtrRwCBehIgNF1P8AyLAAIImAvoQtPr1gGEAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACjVTAtMr0Fad3lYHdkvxW4nB6nsz+eoO2vg9v7iljusf77VpZGAIIIIAAAggg0BgFTEPTWzN2SlF5say5fpEEE5pujMeBNSNQ7wKEput9C5gAAgggUC1AaJqTgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgs2Zsitc7drEIO7t5DLR3Xxe5QPftkmW/afkBFdYmX+7b39fr0sEAEEEEAAAQQQaEwCpqHpLRk7pLi8hNB0YzoArBUBHxMgNO1jG8J0EECg8QoQmm68e8/KbQsc7N/fYaKIoUMl8fXXHb4LvrGCAAAgAElEQVSeCxFAAAEEGrfA8euvl5LNmx1CCIyKkjZ//eXQtVyEAAIIIIAAAggggAACCCCAgDsC07/dK+8sTZHkhEi5+bw+Eh0R4k53DeLejfsy5MNfq4Piz13WWa4b1rJBzJtJIoAAAggggAACCNgX0IWm03dIcYUKTf8kwYFB9m/mCgQQQMDDAoSmPQxKdwgggICrAoSmXZXjPn8XcCY0HT5woCTNmePvJKwPAQQQQMBDAseuvVZKt251qDdC0w4xcRECCCCAAAIIIIAAAggggIAHBEbMWiP70ovkhnE9pFf7BA/02DC6+O9X6+VoRr50aBYh39/VVxKi/T8s3jB2hlkigAACCCCAAALuCZiGpjenb5eSilJZe/1PEkRo2j1Y7kYAAZcECE27xMZNCCCAgOcFCE173pQe/UOA0LTv7mPJxo1S5EDV1eiLLpLgNm18dyF+MrPC33+X0m3bjKsJ7dFDIs88009W1/CXUbBwoeR+9plUHDsmTSZOlCZXXimBMTENZmH+er4ITTeYI8hEEUAAAQQQQAABBBBAAIFGI7Bib45c9vomiYkMlenXDm4061YL/WPjEfnh7/3aml+c2EUmDWnRqNbPYhFAAAEEEEAAAX8VMA1Nb0rfJqUVZbL2hp8kKIBK0/6656wLAV8WIDTty7vD3BBAoFEJEJpuVNvNYp0QMA9NB8XHi4RYrjASdfbZEnfPPU70zqXuCGTOmiV5X3xhtwtV/VtVAad5V+DEzJmS//XXxkGaXHaZxD/yiHcHpXeHBNQLDI5Pnqy7tumNN0rsHXc4dL8vXOSv5yv93nulZHv1W//WamVlUpGZaXyYStO+cBKZAwIIIIAAAggggAACCCDg/wKPf7NX3v0rRQZ0TZSrzujm/ws2WWF2QYm89MU6KSoplysGJcl/r+zaqNbPYhFAAAEEEEAAAX8VMA1Nb0zfJmVaaPpnCQoI9Nclsy4EEPBhAULTPrw5TA0BBBqXAKHpxrXfrNZxAfPQdJu//hIVXKPVv0BDD00XLV8ulTk5RsiIESMaVOVf8xPgK6HWqvJyKfzlF+P0AkJDqyteBzbeb3qcePJJyf/uO92WaQHcP/9sMC6+cr7q8iufqqSfNnWqcUhC03Wpz1gIIIAAAggggAACCCCAQOMV6P7oSskpKpdrx3aXvh2bNTqIr5ftkRVbj0nb+HD5+zEKMTS6A8CCEUAAAQQQQMAvBXSh6bStUlZZLutu+FkCCU375X6zKAR8XYDQtK/vEPNDAIFGI0BoutFsNQt1UoDQtJNgdXh5Qw9NH5s0SUp37DCKqarMqjpzQ22+EmqtzM+Xw6NG6RhbzZ8vIZ07N1Rat+edNXu25M6dq+snODlZkr//XiQgwO3+66IDXzlfdbFWwxiEputSm7EQQAABBBBAAAEEEEAAAQSUwOJtmXLdO1s1jHsuPVWSm0U3Opj1e9Ll49+qv2f3y32nSq/kxmfQ6DadBSOAAAIIIICA3wuYhqY3pG2VckLTfr/nLBABXxYgNO3Lu8PcEECgUQkQmm5U210vi60sLJT0qVMl9JRTJHLcOAnr1ate5uHsoISmnRWrw+srK0WqqmoNeHCgvgJM0pw5Em72WB3O0upQhKa9swuEpmu7lh86JEcvvlj3RPy0adJk0iTvbIIXeiU0Ldq7HKh3O6AhgAACCCCAAAIIIIAAAgi4LqC+Dx4QGSmRp58uwW3auN6Rn95527wd8v2GdG11j18zSJpGhfnpSq0v60Ruscz6dLV2wTMTOsnkEa383mBn5l5ZcmiFdIptJ2Pb64sR+P3iWSACCCCAAAIINAoB09D0+rQtUlFZIetu+EUCG0hxoUaxSSwSgUYkQGi6EW02S0UAAd8WIDTt2/vjD7MrXrNGUm+5xbgUFZqOGD5cIs48U0K7dPHZJRKa9tmtsTox8z0jNF03e+groVZC05b3u2zPHsn7+mupSE2V6Isvlohhw0SCgurmcHhgFF85Xx5YisNdUGnaYSouRAABBBBAAAEEEEAAAQQcFjD9vlF4//7aC+0jRo2S0O7dHe7Dny88/YV1sut4gbbE524aLsFBgf68XKtre+HztZKWXShje8bL3Bt7+q3B0sN/y5c7F4r6XbXTWvSVd8a/6LfrZWEIIIAAAggg0HgFdKHp1M1SUVUp62/4RQIITTfeQ8HKEahHAULT9YjP0AgggICpAKFpzkNdCOQvWCCFv/4qRcuW6YZTP5iIPPNM7VdgtG+93SGh6bo4GZ4dg9C0Zz0d7c1XQq2Eph3dsYZ1na+cr7pUIzRdl9qMhQACCCCAAAIIIIAAAo1FoHT3bsn/+mtR36esKiw0Ljv8tNMkYuRILUAd0q5dY+Gotc5uj66UvKJyCQsJkmemDGu0Dp/+vlPW7k6T0OBAOfDCcL9y2JW5T34/uEx+P7Rc1J8NrV9ST7m6xwQqTfvVbrMYBBBAAAEEEDAImIam16VulkoVmp78iwRIAEgIIIBAnQsQmq5zcgZEAAEELAsQmuZk1KVA6datkvfNN9oPKExbUPPmWnBaq0A9dKhPVEElNF2XJ8MzYxGa9oyjs734SqiV0LSzO9cwrveV81WXWoSm61KbsRBAAAEEEEAAAQQQQKCxCZQfPqwFpwt+/FHKU1J0y48YMaI6QD1ihAS3bNloaIrLKqXjg8u19cY3CZdHJg1sNGs3X+iKrcfk62V7tIe/uqOPDO3UtEFbHMo9KsuPrJZlR//Rfjdt4zqMlnM7jZHRbYY26DUyeQQQQAABBBBAwJaAaWh6beomqaqqIjTNkUEAgXoTIDRdb/QMjAACCOgFCE1zIupDoHT7dsn76qta4Wk1l+BWrSR86FAtPK1+BURE1McUxROh6cqcHMl5+23d/JveeqsENmlicU3Fq1aJCssZWljfvhI5dmyta0s2btQqdxtaeE3YvPzIEcn77DMp3rhRyg8ckICwMAnt1k1CunaVJhMmSHCbNlYt1bhqfEstbMAAiTzjDO2psgMHJHfePFHVedQYQc2aaf2HDxggTS67TCTQ/lt3qvmXbNggpXv2iDoLUlEhgTExEtKli4R26SKRZ50lQQkJTu+7p0LTVcXFknrHHRIQHKz9UmsKCA3V9i04OVlCe/WSsO7dJTA21u4cC37+WUo3b9Zdl//dd1JZUP12p9qZT06WyNGjrfal9rHpLbdo+2mzVVZK4Z9/StmuXVK6c6eUbN8uQTExEpiQIKGdOmm+Ueeco63F0abOVNHy5VKyZYu2Z+VHj0pIx44S1ru3NJk0Sduv+gq15rz1llTm5hqXUlVaKnlffqlbmnohhq0qUcFt20qTiRPtclSVlEjhkiVSqmx37ND2NCA6WsL69JHQrl0lrFcvCR882G4/3rwg66WXRKqq7A6hfvjryFwrTpyQ3PffP3lOa6wq8/K0r92FS5dqP1yuys8/+bk7ZoyEDxpkdw6GC7x5vtQPwYv+/lvKdu/WPh+0sxIUVP01sXNn7UU6au+sNfW1Lt/sPKmvddEXXmj1nqrycsl5801RX0MMTb0oKOb66x02URcSmnaKi4sRQAABBBBAAAEEEEAAAZcEtH/r//Zb9a8lS3R9BISEGMPT6t/RrnyfyqVJ1dNNBzKKZdiz1YHaNolNZOol/eppJvU/7PHMAnnxi3XaRN6b0kPO6eX89yjrexW2gtIJEXFybscztbB094Qu9T1VxkcAAQQQQAABBLwuoAtNH98o6idpGyaf/Dm71yfAAAgggICJAKFpjgMCCCDgIwKEpn1kIxrpNMr27av+wcTixVoI17ypQK4KTmsh6mHDtGBtXTVPhKZVoPDo+efrptz6559Fhegstdy5cyVr9mzjU+EDB0rSnDm1Ls3/5hs58fTTxsdjbrhBq9R9/LrrbPLEP/qoNLn0UovXZL/5pqgQqqUWNW6cNJs1Swtqpz/4oNUx1Hybv/CCBDa1XIFFBQmzXn1VC3bbaoFRUZIwY4ZEjhnj1HZ7KjStgpxHbQQjDZNSb9sad/fdEtK+vdV5Zj77bK0gr1OLqrk4+bvvbIbeK1JTJePxx6V4zRqb3auQc8LMmVrY2V4zD25aur75889L0apVuhdAqPB8/COP2Ove7eePXnCBFuJ2pwXFx0vrxYttdqHCsxkPPmjxa5TpjdEXXCBxDzwg6vzWRzM//9bmEDd1qkMhXrXulAkTjN2EnnKKJP3vf3Lsmmtsuje54gpRYwSEh9tk8Ob5Mv8aaW0isbfdJjFTplS/OMK8VVTI8SlTpMTsRQ8t5s3TQvKWmnqRTPb//qd7KvaOO6TpjTc6dSQITTvFxcUIIIAAAggggAACCCCAgNsCZXv2SMFvv0lxzQvHTTtU/843VqAeNUoCo6PdHs/XOli+J0cuf2OTNq0urWPl1vN6+9oU63Q+0z9YKYUl5fLKVV1l4sCkOh3b1cEqqipk8YG/5NcDS7XfzZuqJn1622FyRtthEhted99jd3U93IcAAggggAACCHhKwDQ0nVeaL8GBwfLxBa95qnv6QQABBJwSIDTtFBcXI4AAAt4TIDTtPVt6dk6g8I8/jAFqVenFvAXGxWnBYBWkjRgyxLnOXbi6IYWmVVizZNMmKTt40O5KE197TQugmzdboWkVlkx8/XVJufBCXYVkS4OpcKAKCZo3FSBPu+suUUF5R5uqzqrCl442T4WmVUVlFZZ0tCU89phEm4RLTe+ri9C0qhCePm2a3b0xnVfzl14yVg+3tM68zz+XzOefd4hAVUM3rXzuT6FpVWkq/b77HHJQF6mq4UlvvGEz4O5wZ05e6O3QtAqYq71WZ8NeazplisT++99WL/PW+VLVxjMefrhWhTBb81VV05PefddicFpVqz560UW621Xl8pafflorFK4qkB+bNEl3rfbCFxWidqACv+mNhKbtnTCeRwABBBBAAAEEEEAAAQS8J6DebUu965alALWqOK1eRK/eMUz97i/to3/S5YHPdmjL6dEuXqac09NflubSOt5auEV2HcmSJy/uKDePSnapj7q6aceJPcag9MHcI7phezfvroWkVVi6Y2zbupoS4yCAAAIIIIAAAj4lYBqafmToXRIUECjtmrb2qTkyGQQQaDwChKYbz16zUgQQ8HEBQtM+vkGNcHoqXFu4eLEWoDav8GngCO3ZUwtPqxB1SFvvfMO3IYWmVVDT0Yq7KvDX6ssvRYKCdKdLVZlWwWlLTVXUib3rLsmcNcv4tBqzqrxcVIVj06auTf7pp1rVdlXw1PytTh053i0//9yhqsiqL0+FptXZS7//fkemZ7zG2jxV8NiRkKm9waxVmlbVu1U184rMTHtd6J5XAdjkH36wWA24Ii1NjpxzjsX+Qjp21MLZpvuu9lw9Zmh1FZpOueQSh14oYAsmKClJWi9aZPGSyvx8OTp+vFNhdNVR5OmnS/OXX3ZqPzxxsbdD02qOysv8c97a3JMXLNBC5ObNm+crb/58yXzuOac54x9+WJpcfrnF+/K//15OzJihey7m6qslziRMr17oc+zKK3XnUX1etPrqKwlKTHR6PoSmnSbjBgQQQAABBBBAAAEEEEDAKwK2AtTqe2wRNeHp8P79vTJ+XXX6zE9H5fVfqgsd9O3UXK4965S6Gtonx/nk952ybnea3Deundw3zjvfe3Zn4TklefLn4ZVaRemlh//WddUmppWoqtIqLD2gRR93huFeBBBAAAEEEEDALwRS8lPlpkX3ydj2o+WegTf7xZpYBAIINFwBQtMNd++YOQII+JkAoWk/21A/W07x339LQU2AujInp/bqgoK08HTU2LHa755sDSk0bVi3qggd/+CDEtq1qwSEhor6wc6Jp5+uVd25xbvvStipp9rkMl+/qp5TtHSpVp04/qGHJKh5c+3+nHfekew33tD1lfTOO2L6w6KSjRvl+OTJumtUmDLh8cclrF8/ba4qoFrw44+1Ao8RQ4dqVa4daZ4KTUtFhVQWFkpVWZlIWZn2u/pYhUXLDh2S/G++qWWqwsQtP/vMYrVa87mrarSqKq2hxT/yiKigsSst94MPJOvVV3W3KjMV6Axp316rcFuRni45b78teSosb9JUFW9Vzdu8Zc2eLblz5+oejjr3XG3fDW9Bqz4fs15+WfIXLKh1f12Fps0HVmfosFmVp1bz50tI586u0IqlFxKoH4jGq3Pbo4d2JopXrJCM6dNr9d9i7lxRFYzrtFVWWhwu9fbbpXj1auNz1vbd/OayAwckxUIFdRUGTpg+XVQV5cCmTUW90CXrpZdEvVuAaVMV51Xl+bo6X9ZC7urrjKqQrc6uqkRdvGaNnJg+XfdCA+3FHosWWX2L5fQHH9RVU1drSpozRzNQzdLnTOLs2RIxcqRLR4DQtEts3IQAAggggAACCCCAAAIIeFXAVoA6rFcviTz7bIkaN874PTOvTsbDnT+58IjM+W2/1utpXZPkyjO6eniEhtXd9yv3ydJNR7Uq06ratC80FZRefuQfWXZ0tfa7+tjQWkYlyog2g2Vk60EysvVgCQgI8IUpMwcEEEAAAQQQQAABBBBAAAEzAULTHAkEEEDARwQITfvIRlibRlWVSFWVVKkwXM2fpebPVepj08etXKfdb35tZWX1Y2aP27zO5FpHx7Z0nZqzs2NX5uVJ6c6dWlC1/OBBrcKxeVPVPFVYOLRzZ616rgr1BrdpI+GnnebSJje00LQKTKugZkBIiG695YcOydGLL9Y9ljBjhkRfeKFNF0tVa1UYt/l//6uFnA1NhRCPjBmjq8bb7LnnJOrss43XHL/xRilZv974sQqetvzkEwmIiKg1BxXuTL31Vt3jjgZfPRaaduDE5H7yiWS9+KLuSkerYnsqNF2ZmytHzztPZ6/2NeGJJ0Qs/HDE9Ou9mrgKirb56y/dGlQY+vAZZ+ge0yonq7UGBtaSOfHUU5L/7be6x/0hNF2ZnS2HzzxTty4V9G8xb54ExcXpHi/ZsEGOT5mieyx8yBBJMnsxgQPHyiuXqM8nT4amk3/8UYJbtNDNVX0dOHbFFbpKy9pZNKvQ7M3zZSnkrqrqqxc0mDdVmT3loot0nzu2Xryg5p1y6aW6oLWqut3qiy+kbPduUV/jTFuTq66SeCer1ZveX9eh6fyvv9ZeXKE1868d9j525Z6AANH9+NZLYxhMtbG8PIapnVNrszcve89rSzP7YbinPzbzc8jT2TlY2SOPWjpwDpy2dKBPW2fPIUs3x7B4Ni0FKJzdMzvXWwxpeHgMU1vjWfH0GD5wNh06J/bW7cQ5ctnSiTGM/83zxH8PTP8Dqv69aqu58byxZzf60KZm735HrrHXh73nLYxRS85eH/aer6d11Np+9b0Qd86ED63D0jIcPpc+vg7j2szOlcW9s3f23H2+jqy074m5czbt3V9H67D2Nc3hs+nj67B0Nk13rmz/fu0d8ko3bxb1Z0NT32tRL7ANGzTI+M5p6t+l5t9LsH0I6v7Z1/9Kk2e+2akNPLRHS7l0pGsvQK/7mXtnxCUbDsvCVQdk4sAkeeWqug+QZxVnyxPLXpLcknzJLc3VAtI5JblSXllhXHBIYIjEhsdIXHhTiQ1rKoEBgdX/1q75P8Pf67R/gdd8YPqvccPj1U+Z/b/xFrM7Tv5l0fjvetO/++uurvk7aq3+zR43/fvnyX8D1vSkzaPWo9p0LT1e3bWFFZmZaHdbWovRykLvDszFOLKJn+mMLI1pOhdL+1N9v4mBJ1zNvpdgt38LrpbOmnGmDlgZ12XF6uSxdeAMWjvj3rQyWaPuxDlzBuv189Wyq0NnsMbbrc9tHziD9j5f/ekM2vt8Nf0qY+m/Gdq/qrT/Vf9NSP1e/de4mkdq/t1lfL7m73imH5vea+irugvdM8aPLfWlG1Obg8l8zPsymZ+j869ZmX5tNWu1vTYrFkYz+061LOzM/6Shnb0x2ytTs2oyS/41rk7sjW5PzcfUnrSxz2Zny2ZfFs6W4RzW2mezMTvFtpOx7Ud55y+O9IoAAgg4KUBo2kkwLkcAAQS8JWAeotO+W1Tzy/gDWpPHjM8b/lFr5Tl1nfYNM/Nf2jcCqh/XPW/yuL3ndM+b3afr28pz2v2G59Q/CtwIJFsK/+pCwbaCxqbhYWvXeWvjG0m/sbfeKk3NAriOLr2hhaZtVTU1Dy03vflmib39dpsUlkLTKuiswtnmzbz/uGnTJGbSpOpvoJSUyKGhQ3W32Kt0nX7vvbqqtc1ffllUcNdeq8vQtJpL6r/+JaoauqE1mzVLqyhkr3kqNG0eMNeq5f7wg1b911KrKiqSoxdcoAt+tvn9dwmMjTVeXrxqlajKxKatxfvvS1jfvhb7LE9NlaPjx+ue84fQtCUHFUaPvugiiw7mZ1Zd1E5Vdw4KsnccvP68J0PTMTfcIHF33WVxzuahZVVpW72Qw7R583yp4LoKsBuatUrXhufzPvlEMk1e+BBz9dVahXZrrfiffyT1ttt0T6vK+yXbtmlV6A0ttEsXLVwfEBbm8t7WdWja0td7lyfPjQgggAACCCCAAAIIIIAAAjoB9e5tia+84tMqX2zIlqnzNmtzHNU7WS4c5hvVlesLbfXOVPn8j11yds8E+eDGHnU+jUeXPi8L9y6u83EZEAEEEEAAAQQQ8EeB5pEJ0i6mtVza7TwZ31FfOMof18uaEEDAtwUITfv2/jA7BBBoRAKmoWlVNdE0+NOIGFiqHwqoisuJr7/eaCpNt12xQquwbamdmDFD8r//3viUI6FW8xCdqg7d6ptvLPavwocl27cbn4u+5BIJ799f+7jswAFJmTDB+Jyq1pu8YIHNE5f/3Xdy4sknjdeYhrBt3VjXoemcd96RbJNqwo6E0dX8PRWaVhWeVaVnQ3NkXzMef1wKFi403tPy448ltHt348fqnKjzYmjafqmzY+NtPVMuuURXYdiReXjjS05lfr4cHqV/pbijVcrN56Mq756YOVP3cNvlyy1WR1cXFS1fLml33qm73lJFZm+s216fngxNJ/3vfxI+eLDFIQt++EEypk+3+bnuzfN1eORIXeXo5EWLJDgpySqP+dcmR36InTV7tuSaBcHNB2j11VcS0qGDvW2x+Xxdh6bVeS9PTxf1d0IaAggggAACCCCAAAIIIICAZwUihg2TxNde82ynHu7tz32FctVra7Vezzy1jZw7qL2HR2hY3W0/lCnvLtoqAzo0lQV39qnTyc9e+668v+mzOh2TwRBAAAEEEEAAgcYgcO/AW+W6Xpc1hqWyRgQQ8GEBQtM+vDlMDQEEGpdA/oIFcuKJJ7RFq4Bp6aZNtatDG8JyzlaPNlSZVp27ea/F+2v6tfeczefra7tVZema6tLGatWGx2p+11XANnlOu960OrbhepM+tb5tPF5rzJr+LM7FvCK2ad9uzkN7Sx7TdZv058gcKzIypCItTSqysvRvdRwYKCGdO0urz1z/BnNDqjStKgy3+esvq6dZhT9VKM7QHAm1mq/fVoVZW59GRStWSNq//228JOzUUyXu7rttfuYVr1wp2W++eXK+V1wh8Q8+aPez1dOh6bK9e6VwyRIpP35cKo4dk/KUFKksLpbgxEQtFFmelqarNK2qEKtqxPaap0LT2a+9JjnvvWccTlXKjTz7bJvD57z5phStXGm8pvkLL0jkWWcZPzavFhw5Zow0/89/bPaZdvfdUrR0qVPny56RK897MjRtbmvrRQNqruVHj2pVvE1bi/fek7B+/VxZikfv8WRoutXXX0tIe8s/OC346SfJeOQR49wtvUDCW+ersqBAVGha528n3FxVXCzKxtDs7bG6rqq0VI5fe62U7t5tcY8Spk+X6Isvdnv/6jo0rSZcvGaNlKyt/gG51sxfKOHpj83ehtMrY5qtw/RdRry2zpoxT75xrhcsLeyP6dshe8TSzhnwiKWD58yjlg6M6balA2OYfn65ZOnkGIYz4ZSli2OYfgF02tLNMR2ydHMMhyw9NIZNSy+MYfdcemlM3bl0doyat2XVrCz82fD2tkZLwzV27tP1Z3zrV8tjWBvb0nyqp1n9NreWxrDal7X1ubAOS2MYZ+Tu3Ixvfeu+lUf2ztTbA1YW9850bxwcw+rarOyzrbPiqb3TzcnBdTj9eeekldHbmflYGcOje+fkOoxfC8zWYXPvnBjD5b2zMIZDZ9OB/XD765yHv+6efCtwk69NDqzD4n9XrNxXmZMjldnZUpGdLerfmbr/hoeFiVbU4aWXJGzgQN1zvvbBjoxyOfPZ6u8XEZoW2bA3XT5avEP6tGsqP02t29D0nuz9csW3t0tFVYXxmMSENZFmEXESGxYrwYFBJn91q3lHTe1Kw7trmvz55KPqacMVNVdXX1fzv+o/m/5/9VM2Hjfrz+T7BrVmVfOXPv3jJ/+9XGscwzuFmq5Lm4ppDybj11rbyb9lmv67RDd+9T8gatZo3c+aq/EOi2uz4ar7NoHZ+MZp23/c9N8/lvws7Jzxez0mOifXb2Krd7J4dc0ZtHBCalxrjV/Lu0bWmp/xeFoY38rZPHkE7ftVHydra7PyuO7zxez8OHRmTeZl5cxWT8kRV+fPrHF0u5/bjnwt0H8+GvrWfjfbU8Nj1j/PTV10PVV/BTDu08nrDI8Z+zTxNDxmsNTtptk5dHy++s9p/bhmX3NtztdEoea6kys2/xp38h2TDWs4uYcmFobzYuE86w1qxtYZnPzvRq0ezc5oldT8DVb96FnbmZP/tjV+bHzu5POmV538q1TtHkz/mqX6NvmXc/VHpv+UNh/b5GbTnk3nZT4j07+Pm46lG1sb1uqzNX9d1K3Q+N/t6ilZMDI8avJ8rR5OTq62g7FbC0Y1z7m/P9UdWd478+dqrnR5f07+Hb323lU/57n9qZ57z2bdJCLYcgEy4wbyBwQQQMDLAoSmvQxM9wgggIAzAqm33KJdnvTWW87cxrUI1L1AVZVWMblo2TLtV1VJiW4OUePHS9Q550iEWXjOlcpWFs8AACAASURBVIk2pNB0SMeO0urLL60u0xOhaRVabnLFFU5T5n35pWQ++6zT95neEDl2rDR//nm7fXgqNF26datkv/22Lghsd3ARib7wQkkwqdJs7R5PhabTH3xQCn/91ZGpWb0m/qGHpMnEicbnVYVvVenb0JpceaXEP/CAzTFcOV9uTdrKzZ4MTZvbRgwdqr2wyFpTPxg9NGyY7ulmM2dK1LnnemOpTvXpydB0m99/l8DYWIvjOxKa9tb5KtuzR1JMzrFTQDUX23vxiaFPa2M5+nXKkbnVR2jakXlxDQIIIIAAAggggAACCCCAgEjJpk2i/t2mfpXu2qUjCe3WTSJGjNB+hfXt22C40gpFBj6xXMoqKmVAl0S56sxuDWbu3pjoon8OyG/rD0uftk3lp7vrNjRtWM/vB5fLT/uXyC/7/zQuUYV8zmg3XEYkD5ThrQdJ07Am3lg+fSKAAAIIIIAAAggggAACCHhJgNC0l2DpFgEEEEAAAX8TUNVaDCHpouXLRVUUNW3BLVqIFpYeP16rLu2pVi+h6Q8+kKxXXzUuIXzgQEmaM6fWkvK/+UZOPP208fHQLl2k5eefW126K6FW8/U3e+45ibJTxdjSBHI/+USyXnzRrW2JOu88aWayXmudeSI0raqtGl5I4uyk6zo0bV7h2dn5qutVZWxVIdvQ0u+9Vwr/+MP4sapeHXfffTa7duV8uTJXe/d4MjSdNnWq9sNPQ4s8/XRp/vLL1qdQWSkHTztN93zCY49J9IQJ9qbt9ec9GppeskQCmza1OGdHQtPeOl+l27fLsauvdssyKD5eWi9ebL+Pqio5duWVtapNx955pzSdPNn+/Q5cQWjaASQuQQABBBBAAAEEEEAAAQTqWEAVBij85RftnXpMW0inTsagdPiAAXU8K88MV1kl0mfGasnMK5ZOrZrK7RfUT1DYM6txv5f3f94mWw+ckBGnNJP5t3R3v0M3etiVuVcW7VuiBaiP5acZe1KBaRWcJkDtBi63IoAAAggggAACCCCAAAJ1LEBouo7BGQ4BBBBAAIGGJqCCm4W//65V+63Mza01/bB+/Yxh6cDoaI8vrz5C01mzZ0vu3LnGtfhSaDrpzTclfNAgp53Nw39OdyAiTS67TOIfecTure6GpquKiuTI2WfXCuarCrRh/fuLClWqt1WtLCqS8kOHpGTzZt2c6jo0bX5e7AJZuKDZrFkSNW6c8ZnM556TvPnzjR87Yu+PoenM55+XPJMXIoT27CktP/zQKnHFiRNyZOxY3fOJ//d/EjF8uCvb4tF7fCk07a3zpf4bcfj0091yC05OluQFC+z2oc6FOh+WmnrxinoRi7uN0LS7gtyPAAIIIIAAAggggAACCHhOIPv117V3vqtITzd2GtKu3cmg9ODBnhusHnsa/eJG2Z2SKwkx4fLwVQPrcSb1P/QLn6+RtOwiuei0VvK/SZ3qf0IiUlBWWB2e3rdE1hzfqJsTAWqf2CImgQACCCCAAAIIIIAAAgjYFSA0bZeICxBAAAEEEGh8AqU7dmhBafWrbN8+iwBaVelzzpGIkSO9ClQfoemMxx6Tgh9/NK7Ll0LTLd57T1RQ3dlWduCApJhV21XBxIDQUIe7CoiIEEeC8eZ7ljh7tlPnRP0A7MSMGbp5NZs5szpUHBRUa76qwlDms88aH3c4NH3ttVK6davxvripUyXm+usd9jBcmP/tt3LiqaeM96mwZuJrrznVT2BMjASEhRnvyTWrdh4xapQkvvKKzT59JjRdUCCHzb4utJg7V8J693bKRF2c+9FHkmVSWVoF59uYVJ4279BSpeNWX34pIR07Oj22p2/wpdC0N8+X2nvTdyJQ5za0uxPVoIKCtBdG2Gqlu3fLsSuusHqJ2u+WH30kAeHhbm0joWm3+LgZAQQQQAABBBBAAAEEEHBboPzwYe17A3lffGHsKygxUSJHjZKI0aN94kXSbi/SrIM7Pt0n36w+KsFBgfLcTfX/InBPr8/R/gpLymX6Byu1yyef3l6eubCNo7fW2XUrU9Zq4Wn1q6SiVDcuAeo62wYGQgABBBBAAAEEEEAAAQScFiA07TQZNyCAAAIIIOCfApXZ2cagdNGKFRYXGT5kiLF6S0jbtnUC4YnQdEVqqhwZP14331bz50tI584W16DCeCqUZ2j+EJquKiuTQ2YVd5o//7xEmlXl9cSmHjnrLKnIzDR2Ff/oo9Lk0ksd7to8/Nt0yhSJ/fe/rd6vAtYqaG1ojoam0++9V1QldeN9EyZIwmOPOTxPw4Ul69fL8Rtv1N3XetEiCUpKcrovww0FP/8sGQ8/bLxfhYVbL16sC1abd57x+ONSsHCh8WFHqlO7PEFbN1ZVyUGzt8Ft9uyz2ossnG2FS5ZI+n336W6z9cIBVXUq5913dde3XbZMAiIjnR3a49f7Umjam+fLfJ1NJk6U+Ice8phnVXGxHLvqKik7eNBmn54Yl9C0x7aNjhBAAAEEEEAAAQQQQAABhwVUULpo+XLJX7hQ92L3yDPO0L6PFTl6tKgX9vtr+3j1Cbn/023a8mZcN0SiI0L8dak213XgeK689l11Jedp53eVe890/fts3gY8nJsii/ar8PTvsi/7UK3hDAHq0W2GyJh2IyQ4MNjbU6J/BBBAAAEEEEAAAQQQQAABGwKEpjkeCCCAAAIINHIBFZA2VJVWwWnzFj5gwMmgdKe6fxtET4SmpaJCDg7Uv51lwowZosK15q1kwwY5PmWK7mF/CE2rBZmHwVUQN+m990RVRvZkU37K0dCaXHWVxN9/v8NDmIeZVZA52qxKtqGzqsJCOTJunK6yraOh6axXXxVVcdfQVHXb5O+/dzpgW5GVJUfGjNGtL/SUUyRpzhwJbNLE4XWbXliycaMcnzxZd6+94PHRCy6Q8qNHT7pfdpnEP/KIS+O7e9OxSZNEVaw3NPWDzeYvveR0t2V790rK5Zfr7lM/IFWBf/Nm6Syo4LoKsPtC86XQtDfPV9bs2ZI7d67+7KpK8eee65FtUFXlVXV509bys88kb/58yf/6a93jzV9+WSJPP93lcQlNu0zHjQgggAACCCCAAAIIIICA0wKFv/4qBYsXi/rd0AJCQiTqvPMk+vzzJax/f6f7bIg3HM4qlcFPr9Kmfs+lp0pys+iGuAy357xw1QFZsuGw1s/L1/SSK/vHud2ntzuorKqURTWVp1ekrJGKyopaQ7aJaaUFp89sN0L6NHfinbm8PXn6RwABBBBAAAEEEEAAAQQakQCh6Ua02SwVAQQQQAABg0DZvn3GoLRpsNHwfFifPtVB6ZEjJbRbt3qF80hoWkRSLrlEV5lUBWRbvP++BLc5+daOZQcOSPq0aaJ8TJu/hKaLV62S1Ntv161NBafj7r9fqwIcEBrqkb1WFZJVJVvTlvTWWxJ+2mn6/isrpTwlRYKTk0UCAozPZf7nP5L36afGjyOGD5fE2bNFAgN191fm5kranXdKyebNuscdDU2rgKWqam3aosaPl/gHHpDApk2dssh+803Jeest3T0qOB03daqEDxqkW59DHVdWSsqll9Y6s4mvvSaqX12rqpKct98WNQfTVm+VpkUk49FHpcAsrBx3zz3S5MorRf3A05mm9lhVmDJtTSdPrq4+XnNutLNw9926sL66Pv7hh6WJWejambE9ea0vhabFi+er4sQJSbn4Yt0LGZRj7K23SvTll4v62utqUy/wUV+jTVvsHXdI0xtv1MY7duWVuhcOqK9vrb76StRbN7vSCE27osY9CCCAAAIIIIAAAggggIDjAup7cAW//qoFpU2/HxeclKSFpdWvkA4dHO/QT64c9tw6OZBWIBNHd5FBp7Twk1U5vozKyip5Yf5aycgp0m5acHd/GdA2yvEOfODKYwVpsiplnfxd8yu7OKfWrAa1PFXObDdc+5UY2cwHZs0UEEAAAQQQQAABBBBAAIHGIUBounHsM6tEAAEEEEBAEyj44YfqsPQff9QSiRg2TMIGDBAVEA7r1ctnxDwVmlbhWPMqpGqRqmqt+kFM2aFDUrR0qbZuFbRTATzTFtavn4S0by9NJk40hlbzv/lGTjz9tPEyVbG55eefW7Uzn4N5qLX8yBHJ++or3f3mFVu1+bZqZbwmIDhYVGjQ0XZixgzJ//77WperIGP44MES3LKlSFWVBCiDvDwp279fVLXfuPvuc7hia97nn0umhUrAkWPGSEi7dlJVWam9vWrx6tXaPFr//LMENW9unFP+d9/JiSef1M1RBfiVl7pOBa1LtmzRfqBmqKxsvmdhvXtLcOvWEtKpkzQ1qxxu6Fi93evRiy6qZaH6Ch8yRIJbtJCA6Gipys+X8rQ0qTh+XKIuvFCaXHpprXuqSkq0wGbZwYO1nlNrVudHGQeEhWnh74rMTM1V/VJnJighodZ9BT/9JBkWKkVrDklJmoWakwonWxq3PkPTBT/+KBmPPVb7nCUlSXj//pqtBAeLCjtXpKZq+6iqv4d2r11hR72wQ1WuNm/KNaRrV+3h4hUran3OKqPk777z2IsB7H2OqTNgHlw3vadgwQJt3w1NnYmwvn1rdRs1dqyE9uhhfFy9mCPFrNJ6myVLrAb7zc+NelFC8oIFdXq+1H9rMqZPt0imKj9rL5QIDpag2FipVJ9fKSlSunOn9kId9UIDS638+HE5dvnlun1WLyBoMW+eqK+DqqkXUBy//nrd7erFGknqBQVmL7qwt5/qeULTjihxDQIIIIAAAggggAACCCDgvIClqtKqF/U9SfWCdhWWDoyJcb5jP7njX5/ulW9Xp0i/Ts3lmrPMXjzvJ2u0tYyNe9Plw8Un38Fs69NDJC7KuRfh+xJTeWWFLD38t6xMWauFqA/nnnynODXPqJBILTh9RtvqADUNAQQQQAABBBBAAAEEEEDAuwKEpr3rS+8IIIAAAgj4jED2nDmSM2eOcT5BcXESMXq0FpRVoTJLoU1fmLynQtMqmHlk/HiHltT01lt1VqY3qWCnqmSsmqdD0yXr18vxG290aI6mF7Vbt87heypzcrQKxqbhTUduVkHGGLMworX7qgoL5ci4cbVCrNaubzF3rqiQs6FVZGRolcHNg+vW7ldB/6qyslpVhtX1KqjcevFiq0tU1aFtBV3Nb7QVRHZ1/8zXfxKiQlIuu8xiINrSglRov3T3buNT9RmaVsF7rbLymjWOHC/tmmazZknUuHEWrz/xxBOSbyH4a6vzZs89J1Fnn+3w+O5eqF5kcHj0aHe7kWYzZ0rUueca+/FWaFoqvHi+qqok7a67alUIt4cTMXSoJL7+eu3LKirk+C23iPocM22tvvxSQjp21D1m6XM69s47RVUnd7YRmnZWjOsRQAABBBBAAAEEEEAAAfsCqbfcovt+gXpRtPoeZcSoUdoLrWki8/5Ok4fm79QoXrx1ZKMjUYFpFZxWLS4qVLY+PdivDLZk7NRC1MuP/CNbM3bp1nZh57PlqZH3+9V6WQwCCCCAAAIIIIAAAggg4GsChKZ9bUeYDwIIIIAAAl4SUBVc8ubP1yqbqh9EmIZUvTSkR7r1VGhaTSbviy8kc9Ysm/NSwWBVdVsFPi21hh6aVmtSlV1VsDD3o48c3qPoCRMkwULlYGsdqKreqrK2I81SuLVo5UpJc6CCtqoKnfT225L9+usWA5r2QtNVpaWSduedxqrX9uarKlAnvfGG1ctU9erMF1/UKtQ62sxDsqb3VZw4IekPPFArLGret/rBYuTo0brK5/UamhYRFfZNvf12rZK0I81msLWiQnLefdehgLs6E82efVarWlyXrcGFpkXEq+erokJU1fis//7X4RdAqOrgrRctqrVtOe+8I9lmn3dx99wjMddeW3uLVcB6yhSt6rRpUxWpnX0XBULTdfkZxFgIIIAAAggggAACCCDQWATSH3xQKrOytKIEId26iXoROE0vsD+9SIbPqn4h+tQJp0qb5tGNhigjp0hemL9WKiurtDVfO7KNPH9Je79df1phhiw9vEoLUatfY9uPkv+c8bjfrpeFIYAAAggggAACCCCAAAK+IEBo2hd2gTkggAACCCCAgFUBT4am1SAlW7bIiRkzpGzfvlpjaqHN66+X0l275NikSRbnZBqaLvj5Z8l4+GHjdeqHPC0//9zqWrJeflkXVFaBPxX8M7TSHTusjmutU3uhYFtHq2zPHsl++20p3bZNyo/q3xbS/D711qjNnnnGqZNasmGDnHjySZuVklV4P/Zf/9Iqnps3FbpV1dGVs6UWOXasxN58s4R07izqB27qhQHmzSGfqiopWLRI8r780mK1atM+VVVbVd3WXitaulRyP/5YyvbutVvVO/7hh6XJ5Zdb7bKqvFxy3nxT8hcutBhAVm9ZG//QQ1KyaZMuaF7foWm1oKriYsmdN0/zLTt40CZbzKRJEjdtms1riteu1cKz5hWH1U1qr8MGDJD4++6ToMREe1vk8efVWg8NG+Z2v81fekkizzjD2E95aqocNauS32bJEgls2tTiWEV//ilpJl9X7H1d8vb5qsjKEhV6Ll61yuLXXdNFaJ+vv/wiEhh4cv0pKXL0/PN1aw3r109avP22SFCQRQP14oWjF12ke86eg6WOCE27fZzpAAEEEEAAAQQQQAABBBBAwEWBYbPWyoH0Qjm9b2s5f0gHF3tpeLct2XBYFq46YJz4onsHSN/WkQ1vIcwYAQQQQAABBBBAAAEEEEDAZwUITfvs1jAxBBBAAAEEEFAC5qFpWyrhAwdK0pw5DsFV5uZqIU5VdTkoJkaC27cXVaFWNVV9uDwlRQJCQiQgNLT6V0iIiPpY/e6HzbDmivR0qSoq0sKIAREREty8uajqr8rA1Wa0zssTqaqSwOhoCVTmrVs75Knmo4Kj5ceOSUBgYPW9rVrpQqMVaWlSVVIiUrNXxr1T+2USwLS3BhV8Ld2zR9SctVZeLgHh4RIQGSnBLVpowVxn+lNdqDNWfuSIVObkSGVRkQSEhUmgob/mzZ3qT/Wh5qfNKypKQtq21Ty0c1tcLGr/1L6pX4EREU71bc/G3edVeLb8wAHNQwu7VlRotir8q2wN63BonMpK7XO0/Phx7XIVZtf2huaWgNfPV0VF9efykSOiwtqGMxAUFydBLVsavwa7tQgnb1bvKlC8erVDd6n/RrRxooq8Q51yEQIIIIAAAggggAACCCCAAAIWBB7/dr+8u/SINIkIlSeuq11swB/Rysor5bXvNsrRjHxteb3aNpVf7u7jj0tlTQgggAACCCCAAAIIIIAAAvUoQGi6HvEZGgEEEEAAAQTsC3grNG1/ZK5AAAEEEPB3gWPXXiulW7c6tExC0w4xcRECCCCAAAIIIIAAAggggIAHBPZnFMnwZ9doPU06s5v071L376rlgWU41cWvaw/Jz2tOvlPZYxd1ln+NbulUH1yMAAIIIIAAAggggAACCCCAgD0BQtP2hHgeAQQQQAABBOpVgNB0vfIzOAIIIODXAoSm/Xp7WRwCCCCAAAIIIIAAAggg0KAFHvxqn3y4/Kh0bxsvN47v2aDXYm/yxzIL5LVvN0pJWYV2aVBggKx+fJC0aOr6u9/ZG5PnEUAAAQQQQAABBBBAAAEEGqcAoenGue+sGgEEEEAAgQYjkHLJJVJVXu7QfCOGD5f4hx5y6FouQgABBBBAIP3++6V0xw6HIIJbtpSkt95y6FouQgABBBBAAAEEEEAAAQQQQMBdgZTsEjn75Q2SmV8qU87pKT3axbvbpc/e/+mSnbJ2V5pxfmN7N5e5k0/x2fkyMQQQQAABBBBAAAEEEEAAgYYrQGi64e4dM0cAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAT8VeOGng/LKL4e0wLQKTvtj27w/Q+b+sl23tPm395YRXWL9cbmsCQEEEEAAAQQQQAABBBBAoJ4FCE3X8wYwPAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIGAukJpbKmNfWi8Zef5ZbbqqSuS17zbKwdRc49KvH9FaZk3owGFAAAEEEEAAAQQQQAABBBBAwCsChKa9wkqnCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgHsCL/18UF76+ZC0TWwiN47vKVHhIe516EN3L9lwRBau2m+cUbvmkbJoal+JjQz2oVkyFQQQQAABBBBAAAEEEEAAAX8SIDTtT7vJWhBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAwG8E8ksq5Mo3N8u6g3lyaufmcvWYU/xibXtSsuWdH7dKeUWlcT2vXd1NJgxI9Iv1sQgEEEAAAQQQQAABBBBAAAHfFCA07Zv7wqwQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABWbE3R67832Ypr6ySs/q3kXMGtm/QKln5JfK/7zdJZl6xcR3n90uUt67r1qDXxeQRQAABBBBAAAEEEEAAAQR8X4DQtO/vETNEAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAIFGLPDmH0flqe/3aQKXj+oig7u3aLAar327UQ6k5hrnHxMRLN/f2Ve6tohssGti4ggggAACCCCAAAIIIIAAAg1DgNB0w9gnZokAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAo1Y4F8f7ZBv16VrAjef20u6tYlrcBrzftkum/Zn6OY95/ruckHfZg1uLUwYAQQQQAABBBBAAAEEEECg4QkQmm54e8aMEUAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEECgEQqMmLVG9qUXaSu/5bxe0rV1wwlOf7t8ryzbkqLbNQLTjfAQs2QEEEAAAQQQQAABBBBAoB4FCE3XIz5DI4AAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAs4IDJ65Wg5nFmu3jB/UXsac2saZ2+vlWgLT9cLOoAgggAACCCCAAAIIIIAAAmYChKY5EggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEADErj6rS2yZEeWNuMBXRPlitO7SmBAgE+u4L9frZejGfm6uVFh2ie3ikkhgAACCCCAAAIIIIAAAn4vQGja77eYBSKAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgL8J/N/iwzLrxwPasromN5XxgzpIm8QmPrPMHYez5LMlOyW/qEw3JwLTPrNFTAQBBBBAAAEEEEAAAQQQaHQChKYb3ZazYAQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQ8AeBxdsy5bp3thqX0q1NnPRunyC9OzaTqPCQelvi8q0p8s2yvbrxT++eIPec1VoGdoipt3kxMAIIIIAAAggggAACCCCAQOMWIDTduPef1SOAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQAMW2Hg4X26bt10Onig2riI6IkR6dWgmfTokSNfWcXW2utSsQlm2JUVWbjtmHLNNQoTcNaaNXD0kqc7mwUAIIIAAAggggAACCCCAAAIIWBIgNM25QAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBqwQEp2iUybv1v+2JFVaxVtEptI+6QYaZfYRNomNZH4JuEeX+nhtDz5c9NR2bA3Xdf3rae3ljvHtJb4qPqreu3xxdIhAggggAACCCCAAAIIIIBAgxUgNN1gt46JI4AAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAtUCZRVV8sqvh2Tu8mOSWVBmlaV5bIQWotaC1C1ipEVcpFOE5RWVklNQKtn5JbJud5qs25MmZeWVxj6CAgNkTPd4uePM1jKwQ4xTfXMxAggggAACCCCAAAIIIIAAAt4UIDTtTV36RgABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBOpQ4HBmsRacnrvimBSUVNgdOTAwQCLDgiUiNFgiwoKr/1zzS/25sKRccvJLJbugRHIKSiS/qHYgOzo8WIZ3iZOx3WNlVLc4aR0XZndcLkAAAQQQQAABBBBAAAEEEECgrgUITde1OOMhgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACXhbYnVoo81Yc0wLU5ZVVXhmtU2KkXHxqc7lmaAtJign1yhh0igACCCCAAAIIIIAAAggggICnBAhNe0qSfhBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEfE9hyNF++Xpsum4/my5Yj+ZJTVO7yDNsmhMugDjFyWvsY7fdTWka53Bc3IoAAAggggAACCCCAAAIIIFDXAoSm61qc8RBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAIF6Eth0JF82Hc6XTUfyan7Pl/CQQAkLDtR+1/6sfq/5uF2zCBnSMUaGdGoqHZpF1NOsGRYBBBBAAAEEEEAAAQQQQAAB9wUITbtvSA8IIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4MMChKZ9eHOYGgIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgi4L0Bo2n1DekAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABHxYgNO3Dm8PUEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMB9AULT7hvSAwIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgj4sAChaR/eHKaGAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACaXB2xQAAIABJREFUCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAu4LEJp235AeEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMCHBQhN+/DmMDUEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQcF+A0LT7hvSAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAj4sQGjahzeHqSGAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICA+wKEpt03pAcEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQ8GEBQtM+vDlMDQEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQTcFyA07b4hPSCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICADwsQmvbhzWFqCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIOC+AKFp9w3pAQEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQR8WIDQtA9vDlNDAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAfcFCE27b0gPCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIODDAoSmfXhzmBoCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIuC9AaNp9Q3pAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAR8WIDTtw5vD1BBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAwLsC21MKZMyL63SD3H9OO7nn7LbeHZjeEbAhcNWcLfLnzizdFSkvj8QMAQQQQAABBBBAAAEEEEDADQFC027gcSsCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACjVVgX3qRLN6WqVv+uX2aSeu4MKdJftueKXvTioz3tUsIl3G9Epzux5UbtqUUyFlmoelp57STextoaLq0vFIWbT4hu1MLZW96kexPL5LswnKJjQyWxJhQ6d06Ws7plSC9kqMlIMAVMe6pC4GJ/9ssy3Zn64YiNF0X8oyBAAIIIIAAAggggAAC/ixAaNqfd5e1IYAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIOAlgV+3Zsr1727V9f7KVV1l4sAkp0c895UNsuFQnvG+sOBA2f/CcKf7ceUGfwlNV1RWyaerUmXmgv2SW1xul6JlbJg8d1lnGdsj3u61XFD3AoSm696cERFAAAEEEEAAAQQQQMD/BQhN+/8es0IEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEPC6wPaVAxphVaH74vPZy55g2To814Kl/5Fh2ifG+9s0iZMUjpzndjys3+ENoWlWXPuvF9bInrdBpAhWafvuG7hIaHOj0vdzgPQFC096zpWcEEEAAAQQQQAABBBBovAKEphvv3rNyBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBFwWyCkql+6PrtTdP3lEK3lmQien++zwwHIpKa803jeiS6zMv7230/24coM/hKZvm7dDvt+Q7srytXs6J0bKT/f2k8jQIJf74EbPChCa9qwnvSGAAAIIIIAAAggggAACSoDQNOcAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQRcEmg7bZmUV1YZ7z2nd4K8N7mHU32VVVRJu/uX6e65cnCSvHxFV6f6cfXihh6aXrEnRy57Y1Ot5QcHBsilpyVKz1ZREhwUKGsO5MrKvTm6it6Gm7q3jJJF9/Sj2rSrh8gL9xGa9gIqXSKAAAIIIIAAAggggECjFyA03eiPAAAIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIuCZw6oxVkppbary5X9sm8uPd/ZzqLCW7RE576h/dPQ+Mbyd3j23rVD+uXtzQQ9OPfbNX3vsrRbf8js0j5Iep/SQ2MrgWy4+bMuSOj3YaK3t3axGpXRsVRpVpV8+QN+4jNO0NVfpEAAEEEEAAAQQQQACBxi5AaLqxnwDWjwACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggICLAue+skE2HMoz3t0yNkzWTh/kVG/rD+XJea9s0N0ze1I3ufy0RKf6cfXihh6aHjJztRzKLNYtf/XjgyQ5LswqSXpeqdwyd4dkF5bJgqn9JJrAtKvHx2v3EZr2Gi0dI4AAAggggAACCCCAQCMWIDTdiDefpSOAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCDgjsBt83bI9xvSjV2EBQfK/heG67r8afMJKS6rND7WOSlCeiVHGz9WlY9v+mC77p6v7+gjQzo1tTm1jPwy2Xo0XzYfyZdNR/LlWE6pdEmKkN7J0dIzOVp6JUdJZKj96sm2QtOZBWXy4crjsuNYgRzJKpHEJiFyatsmoipq924dLU0jaldydsfTlXvbTlsm5ZVVxltjwoNlx7NDXenKoXvKKqpkT2qhbD5a7b49pUCrUt2zVVS1e+toaZ8Q7lBf6qK0vFL5v8WHJTgwQIKCAiQkMECCgwKlSXiQNFfe7WKc6s8w8K9bM6WgpMLiPNR8x/aMNz53IKNIvl2fLjuPF2r73DouTDujgzvGyGntYxxei2E9u44Xivq1NaVA9qUXSWhwgLSKDZPk2DAZ0yNeO0P2mq3QtDqzX61Nk0MniuV4bqnWb49WUdK3TbSM6honAQH2eud5BBBAAAEEEEAAAQQQQKBxChCabpz7zqoRQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQcFtg5oL98saSI7p+Dv5nhIQEVac2VZhXhXpN21k94mXeTT2ND72/LEUe/Xqv7pp/Hh+kBVcttZLySnn4qz3y2apUm/NXwdj/XXuKqPFsNWuh6a5JkfKvD3foAsnm/Tx1cUe5aVSy247udNDhgeWiTEzb7lnDtCCzp9uqfTly/TvbJLe43GbX5/dtJi9d0VULPttry3ZniwoI22oqjK/Cyw+MbycDOzgWYj7lkZVW56kC2odeHKEN+cKig/LKr4esDn/l4CR57tLOEhocaHOOx3NK5a5Pdopaj73WLDpE7h/fTq4d2tLqpZZC00deGik3fbBN1AsRrLU+raPl3ck9bFYatzc/nkcAAQQQQAABBBBAAAEE/FWA0LS/7izrQgABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMDLAh8sPyaPfLVHN8qa6YO0qrqqqQq+w55do3u+fbMIWfHIacbHZi08IP/322HdNSrQqoKt5k1VNb7mna1yLLvE4ZVddlqivDixi9XQq6XQtArKfrk6zWZg2jABFcpW4WxvhJQdWeQZL6zVKiSbtv+7uptcOiDRkdsduqaiskpm/XhA3vhdH5C3dbOqeP3elB4yrLPtiuGf/5Mq93y2y6F5qIvG9oiXF6/oIs2bhNq8x1ZoWt24+akhWsD59g932B37wn7N5c3rTrF4XVWVyLt/HZWnvt/v0Hkx7eTM7nHy1vXdLVZEtxSavuPM1vK6A3ugPneWPnyaSxW67WJwAQIIIIAAAggggAACCCDQgAUITTfgzWPqCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCNSnwG/bM+Xat7fqprDw7n5yatsm2mO/bs2U69/VP29a5Vddo6rzfrkmzdiHCh+rSsnmLTW3VE6dscql5U49q408eG57i/daCk2rSsAZ+WUOj6XCrx/d3Mvh6z154W3zdsj3G9J1XSrjZy/tLNcMbeGRoR78Yo98uPKY032pCtEbnxwsMRHBVu99+ZdD8uJPB53qu1dytPx876kSUDtXb+zHXmj6uzv7yqS3tkhBSYVDY6+fMViSYmoHtVWVdFUt3dU2cWCSvHJV11q3WwpNx0WFSFaBY+dydLc4+fTW+jmTrlpwHwIIIIAAAggggAACCCDgbQFC094Wpn8EEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE/FRAVThWlY5N23uTe8g5vRO0h1Rl4pk/7K+1elXlNyE6RHvcPBzasXmELHv4ZCVqw813fLRTvll3MlxteDw5LkwGd2wqkaGBkppTKkt3ZUtJeaVuTBUiXvvEIIvViS2Fpk1vHtghRoZ0bCotY8NEVbr+YVOGxeDq4mn9pUerqDrf6Y2H82X8f9dbHHd451h56uKO0t2NeR3KLJYhM1fX6l+ZKvc2CWESIAGy8XCeKEvzNmVkK5l5SSerLl+sSZOv16ZJcVmltm+l5ZVSWFqpVRM330fTTh4Y307uHtvWar8q5L3pcL72fEVVlXy2KlV3rWnVZhXuVsF3tR417pw/j9bq966z2shDZsH77ccKZMx/1lmcgwr/d2sRKUGBAZJVUC570vTVwE1vWjC1nwxoV/1CA0OzFJo2PKfO4rm9E6RLUqSk5ZbK+8uPWTyTC+7qKwPax9T5mWRABBBAAAEEEEAAAQQQQMBXBQhN++rOMC8EEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEfFwgv6RCuj68QjfLZyZ0kskjWmmP3fPZLvn8H31YVT3+7Z19ZVCH6jDnqOfW6gKllirk7kotlNOf14ez1b1PXtxRbhqZrKs4nF1YLrd/uEP+3Jmlm9dlpyXKq5O61RK1FppWoeA3rztFzu3TTHdPTlG5THhtk6jArGkb0SVW5t/eu1527Mb3t8mizSesjq3CwA+d204LBTvbrn9nq/y6LVN3mwqHvzelh7SND9c9vmBjhtzx4Q4pr6zSPf7P44OkdVyYs0NLWUWV/LEjS1797bCsPZBb635r1Z/NL6yorJI205bpHlbBYxWQVtWbf7+/v66K9Hfr07UzZNrG9oiXuTf11D2mAtPm50BdMO2c6kB3oEkl7LziCpm34pg8Y/YigqGdmmqWTc2qcVsLTV8xKEleuLyLhASd7LywtELOe2WDqBcxmDZ7wXKnN4QbEEAAAQQQQAABBBBAAIEGLkBouoFvINNHAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAoD4F2k5bpgvJ3jmmjTx8XnttSufP3iDrDuZpf44JD5bc4nLtzy9O7CKThrTQ/nzKIyuNj6uPrxnaQguFmrYp72+Tn8xCwc9e2lluGN7S4tJVZnfg0/9ooVhDU9WE978wvNb11kLTqkLzTaOSLfZfUFIh3R9dWSsc/PdjA2sFietib4pKK+XKOZtl9f7awWLT8VXY+fnLOjtcfXhfepGMmLVGtwQVMl73xCBRnpaaqgauqoKbticu6ii3jrZs6ajPXZ/slC/X6CuNvz+lh4zrVV3V3FazFJpW16tg/MpHB4qqVm7a1PWdH1qhq3TdKzlafrnvVONlP2zMkFvmbq81rArOqwC9tfbx38fl/vm7tacfPLe9TD2rjcVLLYWmk2JCZd0Tg3UvEjDcvP5QnhacNm0TBiTKa1fXfqGAPS+eRwABBBBAAAEEEEAAAQT8VYDQtL/uLOtCAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAoA4EBjylDyebVnQ2DUSP751grIZ82+mtZfqFHbTZtbr3L90sVeBaBa9Nm/kYnRMj5c8HB1gMjxru+3FThtz0gT7UumvWMIkOC9L1bSk0rQLeW2YO0UK11poKvqoArGlTlYhVReL6aKq687TPd8v81bUre5vPR81x1mWdpVWs7erPliouz7upp5xlZ41DZq6WQ5nFxmGvHdpSnr+8s1ssqup0u/v11aLvG9dO7hvX1m6/1kLTl/RPlNevsRwqHvbsGjmQUWTsu1l0iGx6aojx4ye+3SdvLz2qG/vM7nHy0c297M5HvQAgITpEBtZUW7d0g6XQtOmLDczvqaoSSb5P/7nUp3W0/HTvyaC33YlxAQIIIIAAAggggAACCCDg5wKEpv18g1keAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAt4UuODVjbL2wMkKx6rKrqq2ax5ynXlJJ3nsm73aVAzh0pyicq1is2l745pT5OL+zXWPORKsNl9jfkmFdH14he7hn+89VXq3jtY9Zik0PbxzrHzxr9422SxV9rVVndqbe2Da9+Yj+XLPZ7tErctWU5Wi1T7ZCu7+95dD8p+fDhq7USHyg/8ZYTOsri5+8Is98uHKY8b7hnZqKl/d0cdtAvMg89k9E+SDG3vY7ddaaFrtsdprS00Fok0NVZXum00qj181Z4v8uTNLd+tv9/eX7i2j7M7HkQsshabt9d/z8b8lq6DM2H37ZhGy4pHTHBmOaxBAAAEEEEAAAQQQQACBRiFAaLpRbDOLRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMA7And8tFO+WZdm7FxVgV760ADZlVoopz+/VntcVW6ec/0pooKmqrWND5e/Hxuou8bQwXd39tUFeVNzS+XUGat0k7//nHYypFNTuwu69PVNumvevO4UubCfPpBtKTQ9cWCSvHJVV5v9n8gvk97T/9ZdM2VkK1HhcF9of+3Klhd+OqgLtFual63K0f/+eKd8vfbk3qqg9Se32q+k/Pk/qbqK1y1jw2Tt9EE2WbanFMgnq47LoRPFciSrRI5mlUhhaYUEBQZIm/hw6ZwUIapCs2lztJKypdC0owFwa5M2r36urjv84ghtvp5olkLTliql6zym/y0Z+YSmPeFPHwgggAACCCCAAAIIIOCfAoSm/XNfWRUCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACdSLw3I8H5NXFh41jqYD0jmeHyo+bMuSmD7Zrj6vqu+9O7i6qUrBqKrB66MURsmx3tqhwqGlT4VoVsjW0v/fmyASz8LOrC3vioo5y6+hk3e2WQtP/OqO1PHZBB5vDVFWJJN/3l+6aM06Jk49vsR8qdnX+rty3/ViBvPzzIVm4KcPi7VFhQbLxycESGRpU6/lzXl4vm47kuzKs7h4Vtt7/wnCL/azYkyOzfjxgN9xt6WZ3QtPNokNk01NDXFpbZZVIa7O9j4sKka1Pu9afpUlYCk2nvDzS5nz7EJp2aT+5CQEEEEAAAQQQQAABBBqPAKHpxrPXrBQBBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABjwt8/PdxuX/+bl2/Ktw5e/Fhef7HA9rjZ/dMkLdv6C7t7l9mvE4FVn/fnil3f7pLd++Rl0aKabHen7eckMnvbfPIvJ+Z0Ekmj2il68tSaHraOe3k3rPb2hzTUmh6RJdYmX97b4/M1dOdHMsukevf3SZbjtYOQd9xZmt59PzaIXEVcj+QUeT2VFQwe/esYbX6+Wptmtz58U6X+3cnNK2C/L/d39+lsVUF7M4PrdDdmxQTKutnDHapP0s3EZr2GCUdIYAAAggggAACCCCAAAJGAULTHAYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEHBZ4M+dWXLVnC26+3c8M1Qe+XqvfL02TXv8ltHJMuOijtLl4RVSUFKhPfbNv/vIP/tzZdbC6mC1aoYq1aad7U4tlNHPr3V5fqY3vnJVV5k4MEnXl6uh6dTcUjl1xipdX1NGtpKZl3TyyFy90UlFZZVMm79bPv8nVde9tQDxNW9vkd+3Z7k9FUtVnVV4++yX1lvtW1UjDwsJlPKKKikpr7R4nTuh6ZFdY+Xz21wPuHd4YHmteR19aaQEBLjNpXVAaNozjvSCAAIIIIAAAggggAACCJgKEJrmPCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAgMsCe9IKZdRz+lDzHw8OkH9/tNNY1dhQ4Vldp65X7cWJXWRrSoG8vyzFOHa3FpGy5IEBurmUV1ZJ22knK1SrJ1XId+IgffjZkQWM65Ug7RPCdZe6GppeuTdHLn19k66v/0zsIlcPaeHIVOrtmpyicun+6Erd+GHBgbL/heG15jTzh/3yxu9HdI8/cl57CQkOdGr+KjR96YBE3T23zdsh329I1z3Wo1WUTBvXTs7oHidqToZWWSVy6ESRXPP2VtmXfrLytTuh6fG9E+TdyT2cWofpxWP+s062HyvQ3b/68UGSHBfmcp+mNxKa9ggjnSCAAAIIIIAAAggggAACOgFC0xwIBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwWaCwtEI6P7RCd//823vL5Pe2GatKf3RzLzmze5yYVi6+dXSyHMwslp82nzDeq65R15q3PtP/loz8MuPDLWPDZO30Qf/fzr0HWVnWcQD/wXLZBUTuhNwV1KCEwDRF07TsbmNajdWUFYUjaU5qZVpmGZl4mezqmGV2oZosUlMHzZoClFDzCgrIZRUJXG67wO7CLts8h3bdwzm77O6hc2rm88zsLOe8z/P8nvfzvvz33V+Xz9x6YVdD01++c1XcvmhD1hnuvmhKTB/X/6Cc67+5yYlzHo21Va+Gj1OtdXNPip5l2W2S73xsU1z4y+ezjvKjjx0dZ04dWvDxUhA+BeKbRwrML7hkWs4ZWhdKnb1Th+/mUcrQdL7Q98VvGxNfeOfYgm3SBkLTB4XRJgQIECBAgAABAgQIEMgSEJr2QhAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUJDA/gHYa8+ZEF/63aqWPf/2pekxYVifuPIPL8RP/r6vs3QKSG/Z2RBPVNa0zDtvxoiYc/aEnLN84AdPx6JV27K+v+FDE+Pc4wvv6tyV0PT6rfXxxm/8I+ecK751YvTrXVaQZTEWT/7KI7F156sh9LY6TT+zfkecccM/s46UOik/dNn0OKS86/e5o74xjrw8O2h/+bvHxYWnj27z9l/cUhfHX7M063opQ9M3LaiMufevyzlvCvOnUH+hQ2i6UEHrCRAgQIAAAQIECBAgkCsgNO2tIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECgIIEUIE5B4ubxnilD4p4nq1o+r7luRqRg7q1/Wx9XzV+d+X7MoPLYs7cpNmx7dd2V7xkfF5w2Kucs85b8Ky75zcqc7w9G1+POhqZT4PfcW56Jx9ZWZ53nlKMGxrxZuV2yC4I9wOK1m+viFw9viPdPGxaTDuvboVJ3P1kVs362PGvutLGHxD2fm5qzfk9jU0y/eklWl+806bUj+sZdF02Jvl0MiFfXNsTRVzycVS899/T8843GvU3xyZ8siweWbcm6XMrQ9HMbdsZpcx/POW6yuWPm5Ejh8kKG0HQhetYSIECAAAECBAgQIEAgv4DQtDeDAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAIGCBM763lOxZPX2lj0G9u3Z0sm4dRfjB57dEh+/7dnMvB7du0VZ925R37C3ZV17Ieh33PjPeOqlHTnnTIHfS94+Nk4+ckBmz9YjhW3Xba6Ll7fVx0kTB+S9x46GppuaIh5ZvT1m3r48q0tz8708etVxMeyQXgU5dnbxXU+8Euff8Vxm2fD+veLs6cPivVOHxhHDKnI6Xm/b1RC3L3o5rrsvtzvyJ08+LK4564i85Rev2h7n/OCpnGvpuc5882Ex880jM7X3H9trG2LFv3Zlui6PyhMgnnj54thZ39iyLD27P182LSYO75O11a7djXHebcti4crsTuNp0rghFfGzT02KMYPLM6H8tkZ6D0ZfujDr8jtfPzhu+8SkzpJnzb/i9y/ETxfu65zeeqR7+eK7xsUZkwfF+KEVWe9leo82Vu+Oyi11sbthb5vvpdB0QY/GYgIECBAgQIAAAQIECOQVEJr2YhAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUJDARb96Pn736Ka8e6Rg6+IvH5u51lZ33uaFf7p4arxhzCF590ldlU/85tJ2z5k6H6cAb0NjU9TUN2aFmyuvPyknVJ02yxeaHtKvZxw9om/0Ky/L1Fu1sTbWVtVGw96mvPXnfnBifORNrynIsCuLv/Pgi/Hte9fmXZqCu8MP7ZUJT2+q2ZMT9G5elOY9cOm0OOo12WHl1pvO/sXz8YfH8z/fNC/tMbhfz0zn6RSETgHt5jD8BW8ZFVe+N7eD9P5B++Z6qWN3Cn1X1ezJhOSTe/NIzzYFjvONFJr+9fmvi+MPPzTS+3jf05uzprUOaDdf2L9T9nHj+8cvP9PxbuFpz+lX/yOq6xrafXzpjwgaG5syJq3/SKB/eY94bs4JedcKTXflf4Q1BAgQIECAAAECBAgQaF9AaNobQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIBAQQJz718XNy2ozLtHCsHOm7UviJpCpqnDcFvjiauPb7dbc+rqm7r7dmWk4HYKcO8/8oWmO7P/mVOHRuqQXYpx8bwV8dulGwsq/f2PHhVnTRvW7h7VtQ1x+vWPx/qt9Z2udcbkwXH7p3I7Oj+6tjrOvPnJTu33tfcdHl/74+o21/z805Pj9NcOio/e+kw8tHxrp/ZOk183sl8suOQNnVr35+Vb4hO3LWszUH+gzdoK8wv6Dsy7AAAHLklEQVRNH0jOdQIECBAgQIAAAQIECHReQGi682ZWECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECDQSuDXSzbG53+zIq/JeTNGxJyzJ7RcG3PpwjYDputvODm6dWufduXGXfG5eSviicqaTj2DO2ZOjrdOGpSzpquh6REDesfNHz4yZkwY0KlzHMzJH/vxs/Hgsi1d2jJ1h/7iu8bF7NNGdWh9496muOWv6+Pae9d2KiB8+NCKWHj5vk7j+48f/uWl+MbdazpUf/q4/vH72cfE2MsWtjm/FKHpdJgN2+oz7+TClds6dC+tJy2+4o0xbnB5zjqh6U5TWkCAAAECBAgQIECAAIEDCghNH5DIBAIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE2hNIYdEU8sw3rnrf4THrlJEtl950zdKo3FKXM7V/eY94bs4JHYae//gr8b2HXowXNtVGfcPeA6777keOirOn53ZU7kxouneP7jFuSHmcc+zwmHXqyEjB41KOpqaIx9ZVx5+erIp7nqrqUCfogX17xuy3jIqPzxgRfXuXdfr4m2p2x9fvWhMPv7A9ExY+0Bg5sHcs/cpxbU57+qUd8dX5q2PJ6u1556Qzfvb00XH+qSMj+Y//wqI2n3ehoekTjjg07px9zIFuqc3r9z+9OW5cUBkp2N+RdzLdz/wLp8SU0f1y9tw/NJ3mrrluRrtnO+arj0TVjj0tc7rSObvLN28hAQIECBAgQIAAAQIE/g8EhKb/Dx6SIxIgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAg0LbAzvrGWLWpNtZW1cbuxqZoamqKil5lMbRfzxg/tCKG9+/VLl9NXWNs3rE7XqnZE9t2NUTtnsao27M381Pes3scMaxPTBxWEf0revxPP4ZduxszodlXqvfdSwruJo+RA3rHYQN7Z373LDt4Qe8U2n5pa12s3Fgb1bUNkepX9Ooe/cp7xJhB5Zmf9LkjIz2Dys11saaqNhoamzKB7tGDemfsW595+cs7I7pFJkCdnk2v//xOnw/mvXXkzO3NSfezYuOueHlbfdTUNkSPsm6Z8/bpVRYpSD52cHnm3wYBAgQIECBAgAABAgQIFE9AaLp41ioRIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFACAaHpEqArSYAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIBA8QSEpotnrRIBAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAiUQEJouAbqSBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgUT0BounjWKhEgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUAIBoekSoCtJgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgEDxBISmi2etEgECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECJRAQmi4BupIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBRPQGi6eNYqESBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQAgGh6RKgK0mAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAQPEEhKaLZ60SAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIlEBCaLgG6kgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIFE9AaLp41ioRIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFACAaHpEqArSYAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIBA8QSEpotnrRIBAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAiUQEJouAbqSBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgUT0BounjWKhEgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUAIBoekSoCtJgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgEDxBP4NJf698XGgZecAAAAASUVORK5CYII="}}, "cell_type": "markdown", "id": "f79d29eb-13c4-401f-8062-dc321854c73f", "metadata": {}, "source": ["# Local RAG agent with LLaMA3\n", "\n", "We'll combine ideas from paper RAG papers into a RAG agent:\n", "\n", "- **Routing:**  Adaptive RAG ([paper](https://arxiv.org/abs/2403.14403)). Route questions to different retrieval approaches\n", "- **Fallback:** Corrective RAG ([paper](https://arxiv.org/pdf/2401.15884.pdf)). Fallback to web search if docs are not relevant to query\n", "- **Self-correction:** Self-RAG ([paper](https://arxiv.org/abs/2310.11511)). Fix answers w/ hallucinations or don’t address question\n", "\n", "![langgraph_adaptive_rag.png](attachment:6cd777a6-a0b3-4feb-bd07-8e9e8a4b32a0.png)\n", "\n", "## Local models\n", "\n", "### Embedding\n", " \n", "[GPT4All Embeddings](https://blog.nomic.ai/posts/nomic-embed-text-v1):\n", "\n", "```\n", "pip install langchain-nomic\n", "```\n", "\n", "### LLM\n", "\n", "Use [Ollama](https://x.com/ollama/status/1839007158865899651) and [llama3.2](https://ai.meta.com/blog/llama-3-2-connect-2024-vision-edge-mobile-devices/):\n", "\n", "```\n", "ollama pull llama3.2:3b-instruct-fp16 \n", "```"]}, {"cell_type": "code", "execution_count": 8, "id": "23dbd2fd-2526-474e-bc43-cbd96207a109", "metadata": {}, "outputs": [], "source": ["### LLM\n", "from langchain_ollama import ChatOllama\n", "\n", "local_llm = \"llama3.2:3b-instruct-fp16\"\n", "llm = ChatOllama(model=local_llm, temperature=0)\n", "llm_json_mode = ChatOllama(model=local_llm, temperature=0, format=\"json\")"]}, {"cell_type": "markdown", "id": "ab990e71", "metadata": {}, "source": ["### Search\n", "\n", "For search, we use [<PERSON><PERSON>](https://tavily.com/), which is a search engine optimized for LLMs and RAG."]}, {"cell_type": "code", "execution_count": null, "id": "8a8792f5", "metadata": {}, "outputs": [], "source": ["import os\n", "import getpass\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"TAVILY_API_KEY\")\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"true\""]}, {"cell_type": "markdown", "id": "4fdeaced", "metadata": {}, "source": ["### Tracing \n", "\n", "Optionally, use [<PERSON><PERSON><PERSON>](https://www.langchain.com/langsmith) for tracing. "]}, {"cell_type": "code", "execution_count": 5, "id": "e4ba31b0", "metadata": {}, "outputs": [], "source": ["_set_env(\"LANGSMITH_API_KEY\")\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_PROJECT\"] = \"local-llama32-rag\""]}, {"cell_type": "markdown", "id": "bfaf9938", "metadata": {}, "source": ["### Vectorstore "]}, {"cell_type": "code", "execution_count": 6, "id": "86ff9905-313b-46a7-94e7-8ba75c59c708", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import SKLearnVectorStore\n", "from langchain_nomic.embeddings import NomicEmbeddings\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "# Load documents\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "# Split documents\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=1000, chunk_overlap=200\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = SKLearnVectorStore.from_documents(\n", "    documents=doc_splits,\n", "    embedding=NomicEmbeddings(model=\"nomic-embed-text-v1.5\", inference_mode=\"local\"),\n", ")\n", "\n", "# Create retriever\n", "retriever = vectorstore.as_retriever(k=3)"]}, {"cell_type": "markdown", "id": "d8c47fd1", "metadata": {}, "source": ["### Components"]}, {"cell_type": "code", "execution_count": 9, "id": "73e3ee8a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'datasource': 'websearch'} {'datasource': 'websearch'} {'datasource': 'vectorstore'}\n"]}], "source": ["### Router\n", "import json\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "# Prompt\n", "router_instructions = \"\"\"You are an expert at routing a user question to a vectorstore or web search.\n", "\n", "The vectorstore contains documents related to agents, prompt engineering, and adversarial attacks.\n", "                                    \n", "Use the vectorstore for questions on these topics. For all else, and especially for current events, use web-search.\n", "\n", "Return JSON with single key, datasource, that is 'websearch' or 'vectorstore' depending on the question.\"\"\"\n", "\n", "# Test router\n", "test_web_search = llm_json_mode.invoke(\n", "    [SystemMessage(content=router_instructions)]\n", "    + [\n", "        HumanMessage(\n", "            content=\"Who is favored to win the NFC Championship game in the 2024 season?\"\n", "        )\n", "    ]\n", ")\n", "test_web_search_2 = llm_json_mode.invoke(\n", "    [SystemMessage(content=router_instructions)]\n", "    + [HumanMessage(content=\"What are the models released today for llama3.2?\")]\n", ")\n", "test_vector_store = llm_json_mode.invoke(\n", "    [SystemMessage(content=router_instructions)]\n", "    + [HumanMessage(content=\"What are the types of agent memory?\")]\n", ")\n", "print(\n", "    json.loads(test_web_search.content),\n", "    json.loads(test_web_search_2.content),\n", "    json.loads(test_vector_store.content),\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "14275fad-89c2-49ef-847e-4fbc932eb96a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'binary_score': 'yes'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["### Retrieval Grader\n", "\n", "# Doc grader instructions\n", "doc_grader_instructions = \"\"\"You are a grader assessing relevance of a retrieved document to a user question.\n", "\n", "If the document contains keyword(s) or semantic meaning related to the question, grade it as relevant.\"\"\"\n", "\n", "# Grader prompt\n", "doc_grader_prompt = \"\"\"Here is the retrieved document: \\n\\n {document} \\n\\n Here is the user question: \\n\\n {question}. \n", "\n", "This carefully and objectively assess whether the document contains at least some information that is relevant to the question.\n", "\n", "Return JSON with single key, binary_score, that is 'yes' or 'no' score to indicate whether the document contains at least some information that is relevant to the question.\"\"\"\n", "\n", "# Test\n", "question = \"What is Chain of thought prompting?\"\n", "docs = retriever.invoke(question)\n", "doc_txt = docs[1].page_content\n", "doc_grader_prompt_formatted = doc_grader_prompt.format(\n", "    document=doc_txt, question=question\n", ")\n", "result = llm_json_mode.invoke(\n", "    [SystemMessage(content=doc_grader_instructions)]\n", "    + [HumanMessage(content=doc_grader_prompt_formatted)]\n", ")\n", "json.loads(result.content)"]}, {"cell_type": "code", "execution_count": 11, "id": "3b381ea4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chain of Thought (CoT) prompting is a technique used in natural language processing to generate human-like responses by iteratively asking questions and refining the search space through external search queries, such as Wikipedia APIs. CoT prompting involves decomposing problems into multiple thought steps, generating multiple thoughts per step, and evaluating each state using a classifier or majority vote. The goal is to find an optimal instruction that leads to the desired output, which can be achieved by optimizing prompt parameters directly on the embedding space via gradient descent or searching over a pool of model-generated instruction candidates.\n"]}], "source": ["### Generate\n", "\n", "# Prompt\n", "rag_prompt = \"\"\"You are an assistant for question-answering tasks. \n", "\n", "Here is the context to use to answer the question:\n", "\n", "{context} \n", "\n", "Think carefully about the above context. \n", "\n", "Now, review the user question:\n", "\n", "{question}\n", "\n", "Provide an answer to this questions using only the above context. \n", "\n", "Use three sentences maximum and keep the answer concise.\n", "\n", "Answer:\"\"\"\n", "\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Test\n", "docs = retriever.invoke(question)\n", "docs_txt = format_docs(docs)\n", "rag_prompt_formatted = rag_prompt.format(context=docs_txt, question=question)\n", "generation = llm.invoke([HumanMessage(content=rag_prompt_formatted)])\n", "print(generation.content)"]}, {"cell_type": "code", "execution_count": 12, "id": "c7adf546", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'binary_score': 'yes',\n", " 'explanation': 'The student answer provides a clear and accurate description of Chain of Thought (CoT) prompting, its components, and its goals. It also mentions various techniques used in CoT prompting, such as external search queries, prompt tuning, and automatic prompt engineering. The answer demonstrates an understanding of the concept and its applications in natural language processing.'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["### Hallucination Grader\n", "\n", "# Hallucination grader instructions\n", "hallucination_grader_instructions = \"\"\"\n", "\n", "You are a teacher grading a quiz. \n", "\n", "You will be given FACTS and a STUDENT ANSWER. \n", "\n", "Here is the grade criteria to follow:\n", "\n", "(1) Ensure the STUDENT ANSWER is grounded in the FACTS. \n", "\n", "(2) Ensure the STUDENT ANSWER does not contain \"hallucinated\" information outside the scope of the FACTS.\n", "\n", "Score:\n", "\n", "A score of yes means that the student's answer meets all of the criteria. This is the highest (best) score. \n", "\n", "A score of no means that the student's answer does not meet all of the criteria. This is the lowest possible score you can give.\n", "\n", "Explain your reasoning in a step-by-step manner to ensure your reasoning and conclusion are correct. \n", "\n", "Avoid simply stating the correct answer at the outset.\"\"\"\n", "\n", "# Grader prompt\n", "hallucination_grader_prompt = \"\"\"FACTS: \\n\\n {documents} \\n\\n STUDENT ANSWER: {generation}. \n", "\n", "Return JSON with two two keys, binary_score is 'yes' or 'no' score to indicate whether the STUDENT ANSWER is grounded in the FACTS. And a key, explanation, that contains an explanation of the score.\"\"\"\n", "\n", "# Test using documents and generation from above\n", "hallucination_grader_prompt_formatted = hallucination_grader_prompt.format(\n", "    documents=docs_txt, generation=generation.content\n", ")\n", "result = llm_json_mode.invoke(\n", "    [SystemMessage(content=hallucination_grader_instructions)]\n", "    + [HumanMessage(content=hallucination_grader_prompt_formatted)]\n", ")\n", "json.loads(result.content)"]}, {"cell_type": "code", "execution_count": 13, "id": "2ecd225d-8deb-4b2e-adb3-2736619b7671", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'binary_score': 'yes',\n", " 'explanation': \"The student's answer helps to answer the question by providing specific details about the vision models released as part of Llama 3.2. The answer mentions two vision models (Llama 3.2 11B Vision Instruct and Llama 3.2 90B Vision Instruct) and their availability on Azure AI Model Catalog via managed compute. Additionally, the student provides context about <PERSON><PERSON>'s first foray into multimodal AI and compares these models to other visual reasoning models like Claude 3 Haiku and GPT-4o mini. This extra information is not explicitly asked for in the question, but it demonstrates a thorough understanding of the topic. The answer also correctly states that these models replace the older text-only Llama 3.1 models, which meets all the criteria specified in the question.\"}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["### Answer Grader\n", "\n", "# Answer grader instructions\n", "answer_grader_instructions = \"\"\"You are a teacher grading a quiz. \n", "\n", "You will be given a QUESTION and a STUDENT ANSWER. \n", "\n", "Here is the grade criteria to follow:\n", "\n", "(1) The STUDENT ANSWER helps to answer the QUESTION\n", "\n", "Score:\n", "\n", "A score of yes means that the student's answer meets all of the criteria. This is the highest (best) score. \n", "\n", "The student can receive a score of yes if the answer contains extra information that is not explicitly asked for in the question.\n", "\n", "A score of no means that the student's answer does not meet all of the criteria. This is the lowest possible score you can give.\n", "\n", "Explain your reasoning in a step-by-step manner to ensure your reasoning and conclusion are correct. \n", "\n", "Avoid simply stating the correct answer at the outset.\"\"\"\n", "\n", "# Grader prompt\n", "answer_grader_prompt = \"\"\"QUESTION: \\n\\n {question} \\n\\n STUDENT ANSWER: {generation}. \n", "\n", "Return JSON with two two keys, binary_score is 'yes' or 'no' score to indicate whether the STUDENT ANSWER meets the criteria. And a key, explanation, that contains an explanation of the score.\"\"\"\n", "\n", "# Test\n", "question = \"What are the vision models released today as part of Llama 3.2?\"\n", "answer = \"The Llama 3.2 models released today include two vision models: Llama 3.2 11B Vision Instruct and Llama 3.2 90B Vision Instruct, which are available on Azure AI Model Catalog via managed compute. These models are part of Meta's first foray into multimodal AI and rival closed models like Anthropic's Claude 3 Haiku and OpenAI's GPT-4o mini in visual reasoning. They replace the older text-only Llama 3.1 models.\"\n", "\n", "# Test using question and generation from above\n", "answer_grader_prompt_formatted = answer_grader_prompt.format(\n", "    question=question, generation=answer\n", ")\n", "result = llm_json_mode.invoke(\n", "    [SystemMessage(content=answer_grader_instructions)]\n", "    + [HumanMessage(content=answer_grader_prompt_formatted)]\n", ")\n", "json.loads(result.content)"]}, {"cell_type": "markdown", "id": "f91fb35d", "metadata": {}, "source": ["## Web Search Tool"]}, {"cell_type": "code", "execution_count": 14, "id": "4bf32d8f-a640-43a7-bc8e-5f424f5f64eb", "metadata": {}, "outputs": [], "source": ["### Search\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "web_search_tool = TavilySearchResults(k=3)"]}, {"cell_type": "markdown", "id": "d0d1c5f5-b769-411e-b3b6-a7e9032288b2", "metadata": {}, "source": ["# Graph \n", "\n", "We build the above workflow as a graph using [LangGraph](https://langchain-ai.github.io/langgraph/).\n", "\n", "### Graph state\n", "\n", "The graph `state` schema contains keys that we want to:\n", "\n", "* Pass to each node in our graph\n", "* Optionally, modify in each node of our graph \n", "\n", "See conceptual docs [here](https://langchain-ai.github.io/langgraph/concepts/low_level/#state)."]}, {"cell_type": "code", "execution_count": 15, "id": "eaa2feff-6dd6-4de4-b41b-52f931e21eda", "metadata": {}, "outputs": [], "source": ["import operator\n", "from typing_extensions import TypedDict\n", "from typing import List, Annotated\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Graph state is a dictionary that contains information we want to propagate to, and modify in, each graph node.\n", "    \"\"\"\n", "\n", "    question: str  # User question\n", "    generation: str  # LLM generation\n", "    web_search: str  # Binary decision to run web search\n", "    max_retries: int  # Max number of retries for answer generation\n", "    answers: int  # Number of answers generated\n", "    loop_step: Annotated[int, operator.add]\n", "    documents: List[str]  # List of retrieved documents"]}, {"cell_type": "markdown", "id": "057a47f4-ce81-4420-90e9-bf6e26877c5c", "metadata": {}, "source": ["Each node in our graph is simply a function that:\n", "\n", "(1) Take `state` as an input\n", "\n", "(2) Modifies `state` \n", "\n", "(3) Write the modified `state` to the state schema (dict)\n", "\n", "See conceptual docs [here](https://langchain-ai.github.io/langgraph/concepts/low_level/#nodes).\n", "\n", "Each edge routes between nodes in the graph.\n", "\n", "See conceptual docs [here](https://langchain-ai.github.io/langgraph/concepts/low_level/#edges)."]}, {"cell_type": "code", "execution_count": 16, "id": "a7d560a9-eb9a-4f6d-91d9-ba6f985211ae", "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "from langgraph.graph import END\n", "\n", "\n", "### Nodes\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents from vectorstore\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    question = state[\"question\"]\n", "\n", "    # Write retrieved documents to documents key in state\n", "    documents = retriever.invoke(question)\n", "    return {\"documents\": documents}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer using RAG on retrieved documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    loop_step = state.get(\"loop_step\", 0)\n", "\n", "    # RAG generation\n", "    docs_txt = format_docs(documents)\n", "    rag_prompt_formatted = rag_prompt.format(context=docs_txt, question=question)\n", "    generation = llm.invoke([HumanMessage(content=rag_prompt_formatted)])\n", "    return {\"generation\": generation, \"loop_step\": loop_step + 1}\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question\n", "    If any document is not relevant, we will set a flag to run web search\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Filtered out irrelevant documents and updated web_search state\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> DOCUMENT RELEVANCE TO QUESTION---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Score each doc\n", "    filtered_docs = []\n", "    web_search = \"No\"\n", "    for d in documents:\n", "        doc_grader_prompt_formatted = doc_grader_prompt.format(\n", "            document=d.page_content, question=question\n", "        )\n", "        result = llm_json_mode.invoke(\n", "            [SystemMessage(content=doc_grader_instructions)]\n", "            + [HumanMessage(content=doc_grader_prompt_formatted)]\n", "        )\n", "        grade = json.loads(result.content)[\"binary_score\"]\n", "        # Document relevant\n", "        if grade.lower() == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        # Document not relevant\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            # We do not include the document in filtered_docs\n", "            # We set a flag to indicate that we want to run web search\n", "            web_search = \"Yes\"\n", "            continue\n", "    return {\"documents\": filtered_docs, \"web_search\": web_search}\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search based based on the question\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Appended web results to documents\n", "    \"\"\"\n", "\n", "    print(\"---WEB SEARCH---\")\n", "    question = state[\"question\"]\n", "    documents = state.get(\"documents\", [])\n", "\n", "    # Web search\n", "    docs = web_search_tool.invoke({\"query\": question})\n", "    web_results = \"\\n\".join([d[\"content\"] for d in docs])\n", "    web_results = Document(page_content=web_results)\n", "    documents.append(web_results)\n", "    return {\"documents\": documents}\n", "\n", "\n", "### Edges\n", "\n", "\n", "def route_question(state):\n", "    \"\"\"\n", "    Route question to web search or RAG\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ROUTE QUESTION---\")\n", "    route_question = llm_json_mode.invoke(\n", "        [SystemMessage(content=router_instructions)]\n", "        + [HumanMessage(content=state[\"question\"])]\n", "    )\n", "    source = json.loads(route_question.content)[\"datasource\"]\n", "    if source == \"websearch\":\n", "        print(\"---ROUT<PERSON> QUESTION TO WEB SEARCH---\")\n", "        return \"websearch\"\n", "    elif source == \"vectorstore\":\n", "        print(\"---ROUT<PERSON> QUESTION TO RAG---\")\n", "        return \"vectorstore\"\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or add web search\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ASSESS GRADED DOCUMENTS---\")\n", "    question = state[\"question\"]\n", "    web_search = state[\"web_search\"]\n", "    filtered_documents = state[\"documents\"]\n", "\n", "    if web_search == \"Yes\":\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\n", "            \"---DECISION: NOT ALL DOCUMENTS ARE RELEVANT TO QUESTION, INCLUDE WEB SEARCH---\"\n", "        )\n", "        return \"websearch\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\"\n", "\n", "\n", "def grade_generation_v_documents_and_question(state):\n", "    \"\"\"\n", "    Determines whether the generation is grounded in the document and answers question\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---CHEC<PERSON> HALLUCINATIONS---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    generation = state[\"generation\"]\n", "    max_retries = state.get(\"max_retries\", 3)  # Default to 3 if not provided\n", "\n", "    hallucination_grader_prompt_formatted = hallucination_grader_prompt.format(\n", "        documents=format_docs(documents), generation=generation.content\n", "    )\n", "    result = llm_json_mode.invoke(\n", "        [SystemMessage(content=hallucination_grader_instructions)]\n", "        + [HumanMessage(content=hallucination_grader_prompt_formatted)]\n", "    )\n", "    grade = json.loads(result.content)[\"binary_score\"]\n", "\n", "    # Check hallucination\n", "    if grade == \"yes\":\n", "        print(\"---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---\")\n", "        # Check question-answering\n", "        print(\"---GRADE GENERATION vs QUESTION---\")\n", "        # Test using question and generation from above\n", "        answer_grader_prompt_formatted = answer_grader_prompt.format(\n", "            question=question, generation=generation.content\n", "        )\n", "        result = llm_json_mode.invoke(\n", "            [SystemMessage(content=answer_grader_instructions)]\n", "            + [HumanMessage(content=answer_grader_prompt_formatted)]\n", "        )\n", "        grade = json.loads(result.content)[\"binary_score\"]\n", "        if grade == \"yes\":\n", "            print(\"---DECISION: GENERATION ADDRESSES QUESTION---\")\n", "            return \"useful\"\n", "        elif state[\"loop_step\"] <= max_retries:\n", "            print(\"---DECISION: GENERATION DOES NOT ADDRESS QUESTION---\")\n", "            return \"not useful\"\n", "        else:\n", "            print(\"---DECISION: MAX RETRIES REACHED---\")\n", "            return \"max retries\"\n", "    elif state[\"loop_step\"] <= max_retries:\n", "        print(\"---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---\")\n", "        return \"not supported\"\n", "    else:\n", "        print(\"---DECISION: MAX RETRIES REACHED---\")\n", "        return \"max retries\""]}, {"cell_type": "markdown", "id": "60b34375-20c8-440e-a860-e6abc05a950f", "metadata": {}, "source": ["## Control Flow"]}, {"cell_type": "code", "execution_count": 17, "id": "7c5b82ff-ab09-4e86-a395-13a894b2a9fe", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langgraph.graph import StateGraph\n", "from IPython.display import Image, display\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"websearch\", web_search)  # web search\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "\n", "# Build graph\n", "workflow.set_conditional_entry_point(\n", "    route_question,\n", "    {\n", "        \"websearch\": \"websearch\",\n", "        \"vectorstore\": \"retrieve\",\n", "    },\n", ")\n", "workflow.add_edge(\"websearch\", \"generate\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"websearch\": \"websearch\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_conditional_edges(\n", "    \"generate\",\n", "    grade_generation_v_documents_and_question,\n", "    {\n", "        \"not supported\": \"generate\",\n", "        \"useful\": END,\n", "        \"not useful\": \"websearch\",\n", "        \"max retries\": END,\n", "    },\n", ")\n", "\n", "# Compile\n", "graph = workflow.compile()\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": null, "id": "469567ff-26af-4902-b5e4-3fef1c091634", "metadata": {}, "outputs": [], "source": ["inputs = {\"question\": \"What are the types of agent memory?\", \"max_retries\": 3}\n", "for event in graph.stream(inputs, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "fdd44294-b172-456a-98da-7f924e112fa2", "metadata": {}, "source": ["Trace:\n", "\n", "https://smith.langchain.com/public/1e01baea-53e9-4341-a6d1-b1614a800a97/r"]}, {"cell_type": "code", "execution_count": null, "id": "fb20a1a9-f55b-489b-ab9e-d063c59ddfa6", "metadata": {}, "outputs": [], "source": ["# Test on current events\n", "inputs = {\n", "    \"question\": \"What are the models released today for llama3.2?\",\n", "    \"max_retries\": 3,\n", "}\n", "for event in graph.stream(inputs, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "60a16669-0b3e-45f9-9f96-e21a2a426a8f", "metadata": {}, "source": ["Trace:\n", "\n", "https://smith.langchain.com/public/acdfa49d-aa11-48fb-9d9c-13a687ff311f/r"]}, {"cell_type": "markdown", "id": "745d630e", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}